package com.alibaba.copilot.app.service.rewrite.service;

import com.alibaba.copilot.app.service.rewrite.service.impl.TextTranslateServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

import static org.junit.Assert.*;

/**
 * 文本翻译服务签名功能测试类
 * 测试TextTranslateServiceImpl中的签名生成功能
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class TextTranslateSignatureTest {

    @Autowired
    private TextTranslateServiceImpl textTranslateService;

    /**
     * 测试签名生成功能
     */
    @Test
    public void testSignatureGeneration() throws Exception {
        // 设置测试用的密钥
        String testSecret = "test_secret_key";
        String testTimestamp = "1733194861440";

        // 使用反射调用私有方法
        String signature = (String) ReflectionTestUtils.invokeMethod(
                textTranslateService,
                "generateSignature",
                testSecret,
                testTimestamp
        );

        assertNotNull("签名不应为空", signature);
        assertFalse("签名不应为空字符串", signature.isEmpty());
        assertTrue("签名应为大写", signature.equals(signature.toUpperCase()));
        assertEquals("签名长度应为64位（SHA256十六进制）", 64, signature.length());

        // 验证签名是否正确
        String expectedSignature = generateExpectedSignature(testSecret, testTimestamp);
        assertEquals("签名应与预期一致", expectedSignature, signature);

        System.out.println("Generated signature: " + signature);
        System.out.println("Expected signature: " + expectedSignature);
    }

    /**
     * 测试带签名URL构建功能
     */
    @Test
    public void testSignedUrlBuilding() {
        // 设置测试配置
        ReflectionTestUtils.setField(textTranslateService, "appKey", "test_app_key");
        ReflectionTestUtils.setField(textTranslateService, "appSecret", "test_secret");
        ReflectionTestUtils.setField(textTranslateService, "baseUrl", "https://cn-api.aidc-ai.com");

        // 使用反射调用私有方法
        String signedUrl = (String) ReflectionTestUtils.invokeMethod(
                textTranslateService,
                "buildSignedUrl",
                "/ai/text/marco/translator"
        );

        assertNotNull("签名URL不应为空", signedUrl);
        assertTrue("URL应包含基础URL", signedUrl.startsWith("https://cn-api.aidc-ai.com"));
        assertTrue("URL应包含API路径", signedUrl.contains("/ai/text/marco/translator"));
        assertTrue("URL应包含partner_id参数", signedUrl.contains("partner_id=aidge"));
        assertTrue("URL应包含sign_method参数", signedUrl.contains("sign_method=sha256"));
        assertTrue("URL应包含sign_ver参数", signedUrl.contains("sign_ver=v2"));
        assertTrue("URL应包含app_key参数", signedUrl.contains("app_key=test_app_key"));
        assertTrue("URL应包含timestamp参数", signedUrl.contains("timestamp="));
        assertTrue("URL应包含sign参数", signedUrl.contains("sign="));
        assertTrue("URL应包含/rest路径", signedUrl.contains("/rest/"));

        System.out.println("Generated signed URL: " + signedUrl);
    }

    /**
     * 测试签名一致性
     */
    @Test
    public void testSignatureConsistency() throws Exception {
        String testSecret = "consistent_test_secret";
        String testTimestamp = "1733194861440";

        // 多次生成相同参数的签名，应该得到相同结果
        String signature1 = (String) ReflectionTestUtils.invokeMethod(
                textTranslateService,
                "generateSignature",
                testSecret,
                testTimestamp
        );

        String signature2 = (String) ReflectionTestUtils.invokeMethod(
                textTranslateService,
                "generateSignature",
                testSecret,
                testTimestamp
        );

        assertEquals("相同参数应生成相同签名", signature1, signature2);
    }

    /**
     * 测试不同参数生成不同签名
     */
    @Test
    public void testDifferentSignatures() throws Exception {
        String testSecret = "test_secret";
        String timestamp1 = "1733194861440";
        String timestamp2 = "1733194861441";

        String signature1 = (String) ReflectionTestUtils.invokeMethod(
                textTranslateService,
                "generateSignature",
                testSecret,
                timestamp1
        );

        String signature2 = (String) ReflectionTestUtils.invokeMethod(
                textTranslateService,
                "generateSignature",
                testSecret,
                timestamp2
        );

        assertNotEquals("不同时间戳应生成不同签名", signature1, signature2);
    }

    /**
     * 生成预期的签名用于验证
     */
    private String generateExpectedSignature(String secret, String timestamp) throws Exception {
        String data = secret + timestamp;
        Mac mac = Mac.getInstance("HmacSHA256");
        SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
        mac.init(secretKeySpec);
        byte[] signData = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));

        StringBuilder hexString = new StringBuilder();
        for (byte b : signData) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString().toUpperCase();
    }
}
