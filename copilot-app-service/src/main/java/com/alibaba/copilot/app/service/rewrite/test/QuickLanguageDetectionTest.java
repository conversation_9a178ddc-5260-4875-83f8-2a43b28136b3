package com.alibaba.copilot.app.service.rewrite.test;

import com.github.pemistahl.lingua.api.Language;
import com.github.pemistahl.lingua.api.LanguageDetector;
import com.github.pemistahl.lingua.api.LanguageDetectorBuilder;

import java.util.Map;

/**
 * 快速语言检测测试类
 * 可以通过命令行参数直接测试
 * 
 * 使用方法:
 * java com.alibaba.copilot.app.service.rewrite.test.QuickLanguageDetectionTest "这是中文测试"
 */
public class QuickLanguageDetectionTest {

    public static void main(String[] args) {
        if (args.length == 0) {
            System.out.println("请提供要检测的文本作为命令行参数");
            System.out.println("例如: java QuickLanguageDetectionTest \"这是中文测试\"");
            return;
        }

        String text = args[0];
        System.out.println("正在检测文本: " + text);
        
        // 创建语言检测器
        System.out.println("初始化语言检测器...");
        LanguageDetector detector = LanguageDetectorBuilder.fromAllLanguages()
                .build();
        
        // 检测语言
        long startTime = System.currentTimeMillis();
        Language detectedLanguage = detector.detectLanguageOf(text);
        long endTime = System.currentTimeMillis();
        
        System.out.println("\n检测结果:");
        System.out.println("检测到的语言: " + detectedLanguage.toString());
        System.out.println("ISO 639-1 代码: " + detectedLanguage.getIsoCode639_1());
        System.out.println("ISO 639-3 代码: " + detectedLanguage.getIsoCode639_3());
        System.out.println("检测耗时: " + (endTime - startTime) + "ms");
        
        // 计算各语言的置信度
        System.out.println("\n各语言置信度 (前5个):");
        Map<Language, Double> confidenceValues = detector.computeLanguageConfidenceValues(text);
        
        confidenceValues.entrySet().stream()
                .sorted(Map.Entry.<Language, Double>comparingByValue().reversed())
                .limit(5)
                .forEach(entry -> {
                    Language language = entry.getKey();
                    Double confidence = entry.getValue();
                    System.out.printf("%-15s: %.4f%n", language.toString(), confidence);
                });
    }
}
