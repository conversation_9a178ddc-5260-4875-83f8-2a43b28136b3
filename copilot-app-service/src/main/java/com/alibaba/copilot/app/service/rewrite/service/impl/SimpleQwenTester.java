package com.alibaba.copilot.app.service.rewrite.service.impl;

import com.alibaba.fastjson.JSON;
import okhttp3.*;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 简单的Qwen API测试类
 * 可以单独运行，不依赖Spring等框架
 */
public class SimpleQwenTester {
    
    private static final String QWEN_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
    private static final String QWEN_API_KEY = "sk-2a2b98ede2234d7b837722603f8c5035";
    private static final String QWEN_MODEL = "qwen-plus-latest";

    public static void main(String[] args) {
        // 创建OkHttpClient
        OkHttpClient client = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .build();

        try {
            // 创建请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", QWEN_MODEL);
            requestBody.put("stream", true);
            
            List<Map<String, String>> messages = new ArrayList<>();
            Map<String, String> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", "ruhe ？");
            messages.add(message);
            
            requestBody.put("messages", messages);
            
            // 转换为JSON
            String jsonBody = JSON.toJSONString(requestBody);
            System.out.println("请求内容: " + jsonBody);

            // 创建请求
            Request request = new Request.Builder()
                    .url(QWEN_API_URL)
                    .post(RequestBody.create(MediaType.parse("application/json"), jsonBody))
                    .header("Authorization", "Bearer " + QWEN_API_KEY)
                    .header("Content-Type", "application/json")
                    .build();

            // 执行请求
            try (Response response = client.newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    System.err.println("请求失败: " + response.code() + " " + response.message());
                    return;
                }

                ResponseBody responseBody = response.body();
                if (responseBody == null) {
                    System.err.println("响应体为空");
                    return;
                }

                System.out.println("开始接收流式响应:");
                
                // 读取流式响应
                BufferedReader reader = new BufferedReader(responseBody.charStream());
                String line;
                StringBuilder fullResponse = new StringBuilder();
                
                while ((line = reader.readLine()) != null) {
                    if (line.isEmpty()) {
                        continue;
                    }
                    
                    System.out.println("收到原始数据: " + line);
                    
                    if (line.startsWith("data:")) {
                        String data = line.substring(5).trim();
                        
                        if ("[DONE]".equals(data)) {
                            System.out.println("接收完成");
                            break;
                        }
                        
                        try {
                            Map<String, Object> jsonResponse = JSON.parseObject(data, Map.class);
                            List<Map<String, Object>> choices = (List<Map<String, Object>>) jsonResponse.get("choices");
                            
                            if (choices != null && !choices.isEmpty()) {
                                Map<String, Object> choice = choices.get(0);
                                Map<String, Object> delta = (Map<String, Object>) choice.get("delta");
                                
                                if (delta != null && delta.containsKey("content")) {
                                    String content = (String) delta.get("content");
                                    fullResponse.append(content);
                                    System.out.println("内容片段: " + content);
                                }
                            }
                        } catch (Exception e) {
                            System.err.println("解析响应失败: " + e.getMessage());
                        }
                    }
                }
                
                System.out.println("\n完整响应内容:");
                System.out.println(fullResponse.toString());
            }
        } catch (IOException e) {
            System.err.println("请求发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
} 