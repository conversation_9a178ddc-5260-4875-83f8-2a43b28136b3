package com.alibaba.copilot.app.service.rewrite.service.impl;

import com.alibaba.copilot.app.domain.rewrite.entity.RewriteDocument;
import com.alibaba.copilot.app.domain.rewrite.entity.TextGenerateRecord;
import com.alibaba.copilot.app.domain.rewrite.repository.TextGenerateRecordRepository;
import com.alibaba.copilot.app.service.rewrite.config.RewriteAuthService;
import com.alibaba.copilot.app.service.rewrite.dto.DetectLanguageRequest;
import com.alibaba.copilot.app.service.rewrite.dto.DetectLanguageResponse;
import com.alibaba.copilot.app.service.rewrite.dto.TranslateTextRequest;
import com.alibaba.copilot.app.service.rewrite.dto.TranslateTextResponse;
import com.alibaba.copilot.app.service.rewrite.enums.TextGenerateType;
import com.alibaba.copilot.app.service.rewrite.service.RewriteDocumentService;
import com.alibaba.copilot.app.service.rewrite.service.RewriteDocumentVersionService;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pemistahl.lingua.api.Language;
import com.github.pemistahl.lingua.api.LanguageDetector;
import com.github.pemistahl.lingua.api.LanguageDetectorBuilder;
import com.taobao.common.keycenter.security.Cryptograph;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Pattern;

@Slf4j
@Service
public class TextTranslateServiceImpl {

    @Resource
    private TextGenerateRecordRepository textGenerateRecordRepository;

    @Resource
    private RewriteAuthService authService;

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private Cryptograph cryptograph;

    @Resource
    private RewriteDocumentService rewriteDocumentService;

    @Resource
    private RewriteDocumentVersionService rewriteDocumentVersionService;

    @Value("${marco.translator.baseUrl:https://cn-api.aidc-ai.com}")
    private String baseUrl;

    @Value("${marco.translator.appKey:}")
    private String appKey;

    @Value("${marco.translator.appSecret:}")
    private String appSecretEncrypted;

    @Value("${key.center.app.key}")
    private String keyCenterAppKey;

    private static final String PARTNER_ID = "aidge";

    private String appSecret;
    private LanguageDetector languageDetector;

    // 中文繁体简体检测相关常量
    private static final Pattern CHINESE_PATTERN = Pattern.compile("[\u4e00-\u9fff]");
    
    // 繁体中文特有字符集合（常见的繁体字）
    private static final Set<Character> TRADITIONAL_CHINESE_CHARS = new HashSet<>();
    
    // 简体中文特有字符集合（常见的简体字）
    private static final Set<Character> SIMPLIFIED_CHINESE_CHARS = new HashSet<>();

    static {
        // 初始化繁体中文特有字符（这些字符在简体中文中有对应的简化字）
        String traditionalChars = "繁體中文檢測測試資料庫網絡連接設置頁面內容顯示結果處理過程開發環境運行時間問題解決方案實現功能模塊系統配置參數優化性能提升用戶體驗改進產品質量保證項目管理團隊協作溝通交流學習成長進步發展機會挑戰困難克服成功達成目標計劃執行監控評估反饋調整完善持續改進創新突破領先優勢競爭力市場份額客戶滿意度服務質量標準規範流程制度建立健全完整體系架構設計開發實施部署維護支持培訓指導幫助協助配合支援資源整合利用效率最大化價值創造貢獻社會責任擔當使命願景理念文化傳承發揚光大";
        for (char c : traditionalChars.toCharArray()) {
            TRADITIONAL_CHINESE_CHARS.add(c);
        }
        
        // 初始化简体中文特有字符（这些字符在繁体中文中有对应的繁体字）
        String simplifiedChars = "简体中文检测测试资料库网络连接设置页面内容显示结果处理过程开发环境运行时间问题解决方案实现功能模块系统配置参数优化性能提升用户体验改进产品质量保证项目管理团队协作沟通交流学习成长进步发展机会挑战困难克服成功达成目标计划执行监控评估反馈调整完善持续改进创新突破领先优势竞争力市场份额客户满意度服务质量标准规范流程制度建立健全完整体系架构设计开发实施部署维护支持培训指导帮助协助配合支援资源整合利用效率最大化价值创造贡献社会责任担当使命愿景理念文化传承发扬光大";
        for (char c : simplifiedChars.toCharArray()) {
            SIMPLIFIED_CHINESE_CHARS.add(c);
        }
    }

    @PostConstruct
    public void init() {
        // 使用 KeyCenter 解密 API Secret
        if (StringUtils.isNotBlank(appSecretEncrypted) && StringUtils.isNotBlank(keyCenterAppKey)) {
            try {
                appSecret = cryptograph.decrypt(appSecretEncrypted, keyCenterAppKey);
                log.info("Marco translator API secret decrypted successfully");
            } catch (Exception e) {
                log.error("Failed to decrypt Marco translator API secret", e);
                appSecret = appSecretEncrypted; // 降级使用原始值
            }
        } else {
            appSecret = appSecretEncrypted;
        }

        // Initialize language detector with supported languages only
        List<Language> supportedLanguages = Arrays.asList(
            Language.ARABIC,
            Language.AZERBAIJANI,
            Language.BULGARIAN,
            Language.BENGALI,
            Language.CZECH,
            Language.GERMAN,
            Language.GREEK,
            Language.ENGLISH,
            Language.SPANISH,
            Language.FINNISH,
            Language.FRENCH,
            Language.HEBREW,
            Language.HINDI,
            Language.HUNGARIAN,
            Language.INDONESIAN,
            Language.ITALIAN,
            Language.JAPANESE,
            Language.KOREAN,
            Language.MALAY,
            Language.DUTCH,
            Language.BOKMAL, // Norwegian
            Language.POLISH,
            Language.PORTUGUESE,
            Language.ROMANIAN,
            Language.RUSSIAN,
            Language.SLOVAK,
            Language.SWEDISH,
            Language.THAI,
            Language.TURKISH,
            Language.UKRAINIAN,
            Language.URDU,
            Language.VIETNAMESE,
            Language.CHINESE
        );

        languageDetector = LanguageDetectorBuilder.fromLanguages(supportedLanguages.toArray(new Language[0]))
                .withPreloadedLanguageModels()
                .build();

        log.info("Language detector initialized with {} supported languages", supportedLanguages.size());
    }

    /**
     * 检测中文文本是繁体还是简体
     *
     * @param text 中文文本
     * @return "zh-CN" 表示简体中文，"zh-TW" 表示繁体中文，"zh" 表示无法确定
     */
    private String detectChineseVariant(String text) {
        if (StringUtils.isBlank(text)) {
            return "zh";
        }

        int traditionalCount = 0;
        int simplifiedCount = 0;
        int totalChineseChars = 0;

        // 统计繁体字、简体字和总中文字符数
        for (char c : text.toCharArray()) {
            if (CHINESE_PATTERN.matcher(String.valueOf(c)).matches()) {
                totalChineseChars++;
                if (TRADITIONAL_CHINESE_CHARS.contains(c)) {
                    traditionalCount++;
                } else if (SIMPLIFIED_CHINESE_CHARS.contains(c)) {
                    simplifiedCount++;
                }
            }
        }

        // 如果中文字符太少，无法准确判断
        if (totalChineseChars < 10) {
            log.debug("Chinese characters too few ({}) to determine variant accurately", totalChineseChars);
            return "zh";
        }

        // 计算繁体字和简体字的比例
        double traditionalRatio = (double) traditionalCount / totalChineseChars;
        double simplifiedRatio = (double) simplifiedCount / totalChineseChars;

        log.debug("Chinese variant detection - Traditional: {}/{} ({:.2%}), Simplified: {}/{} ({:.2%})", 
                traditionalCount, totalChineseChars, traditionalRatio,
                simplifiedCount, totalChineseChars, simplifiedRatio);

        // 如果繁体字比例超过5%，认为是繁体中文
        if (traditionalRatio > 0.05) {
            return "zh-tw";
        }
        // 如果简体字比例超过5%，认为是简体中文
        else if (simplifiedRatio > 0.05) {
            return "zh";
        }
        // 如果都没有明显特征，根据更细致的规则判断
        else {
            // 检查一些常见的繁简体差异词汇
            if (containsTraditionalPhrases(text)) {
                return "zh-tw";
            } else if (containsSimplifiedPhrases(text)) {
                return "zh";
            }
            // 无法确定，返回通用中文代码
            return "zh";
        }
    }

    /**
     * 检查文本是否包含繁体中文常用词汇
     */
    private boolean containsTraditionalPhrases(String text) {
        String[] traditionalPhrases = {
            "資訊", "網絡", "軟體", "硬體", "資料庫", "網站", "電腦", "行動電話",
            "網際網路", "資料", "檔案", "資料夾", "螢幕", "滑鼠", "鍵盤", "印表機",
            "軟件", "應用程式", "作業系統", "瀏覽器", "搜尋引擎", "電子郵件"
        };
        
        for (String phrase : traditionalPhrases) {
            if (text.contains(phrase)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查文本是否包含简体中文常用词汇
     */
    private boolean containsSimplifiedPhrases(String text) {
        String[] simplifiedPhrases = {
            "信息", "网络", "软体", "硬体", "数据库", "网站", "电脑", "手机",
            "互联网", "数据", "文件", "文件夹", "屏幕", "鼠标", "键盘", "打印机",
            "软件", "应用程序", "操作系统", "浏览器", "搜索引擎", "电子邮件"
        };
        
        for (String phrase : simplifiedPhrases) {
            if (text.contains(phrase)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 生成HMAC SHA256签名
     *
     * @param secret 密钥
     * @param timestamp 时间戳
     * @return 签名字符串（大写）
     */
    private String generateSignature(String secret, String timestamp) {
        try {
            String data = secret + timestamp;
            Mac mac = Mac.getInstance("HmacSHA256");
            SecretKeySpec secretKeySpec = new SecretKeySpec(secret.getBytes(StandardCharsets.UTF_8), "HmacSHA256");
            mac.init(secretKeySpec);
            byte[] signData = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));

            // 转换为十六进制字符串并转为大写
            StringBuilder hexString = new StringBuilder();
            for (byte b : signData) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString().toUpperCase();
        } catch (Exception e) {
            log.error("生成签名失败", e);
            throw new RuntimeException("签名生成失败", e);
        }
    }

    /**
     * 构建带签名的请求URL
     *
     * @param apiPath API路径
     * @return 带签名的完整URL
     */
    private String buildSignedUrl(String apiPath) {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String signature = generateSignature(appSecret, timestamp);

        return UriComponentsBuilder.fromHttpUrl(baseUrl + "/rest" + apiPath)
                .queryParam("partner_id", PARTNER_ID)
                .queryParam("sign_method", "sha256")
                .queryParam("sign_ver", "v2")
                .queryParam("app_key", appKey)
                .queryParam("timestamp", timestamp)
                .queryParam("sign", signature)
                .build()
                .toUriString();
    }

    /**
     * 翻译文本
     *
     * @param request 翻译请求
     * @return 翻译结果
     */
    public SingleResult<TranslateTextResponse> translate(TranslateTextRequest request) {
        try {
            log.info("TextTranslateServiceImpl translate request={}", JSON.toJSONString(request));

            // 检查必要的配置
            if (StringUtils.isBlank(appKey) || StringUtils.isBlank(appSecret)) {
                log.error("TextTranslateServiceImpl.translate API credentials not configured, request={}", JSON.toJSONString(request));
                return (SingleResult<TranslateTextResponse>) SingleResult.buildFailure("API credentials not configured");
            }

            // 构建带签名的URL
            String signedUrl = buildSignedUrl("/ai/text/marco/translator");

            // 构建请求参数
            Map<String, Object> requestParams = new HashMap<>();

            // 将文本转换为JSON数组格式
            JSONArray textArray = new JSONArray();
            textArray.add(request.getText());
            requestParams.put("text", textArray.toJSONString());

            if (StringUtils.isNotBlank(request.getSourceLanguage())) {
                requestParams.put("sourceLanguage", request.getSourceLanguage());
            }

            requestParams.put("targetLanguage", request.getTargetLanguage());

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            // 创建请求实体
            HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(requestParams, headers);

            // 发送请求到带签名的URL
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    signedUrl,
                    HttpMethod.POST,
                    requestEntity,
                    String.class
            );

            // 解析响应
            String responseBody = responseEntity.getBody();
            log.info("TextTranslateServiceImpl.translate response={}, request={}", responseBody, JSON.toJSONString(request));

            JSONObject responseJson = JSON.parseObject(responseBody);

            // 检查响应状态
            if (responseJson.getInteger("resCode") != 200 || !responseJson.getBoolean("success")) {
                String errorMessage = responseJson.getString("resMessage");
                log.error("TextTranslateServiceImpl.translate error: {}, request={}", errorMessage, JSON.toJSONString(request));
                return (SingleResult<TranslateTextResponse>) SingleResult.buildFailure(errorMessage);
            }

            // 解析数据
            JSONObject data = responseJson.getJSONObject("data");
            JSONArray translations = data.getJSONArray("translations");
            JSONObject translation = translations.getJSONObject(0);

            // 构建响应对象
            TranslateTextResponse response = new TranslateTextResponse();
            response.setTranslatedText(translation.getString("translatedText"));
            response.setCharacters(translation.getInteger("characters"));
            response.setDetectedLanguage(translation.getString("detectedLanguage"));

            // 保存记录
            saveTranslateRecord(request, response);

            return SingleResult.buildSuccess(response);
        } catch (Exception e) {
            log.error("TextTranslateServiceImpl.translate error, request={}", JSON.toJSONString(request), e);
            return (SingleResult<TranslateTextResponse>) SingleResult.buildFailure("Translation failed: " + e.getMessage());
        }
    }

    /**
     * 保存翻译记录
     */
    private void saveTranslateRecord(TranslateTextRequest request, TranslateTextResponse response) {
        try {
            TextGenerateRecord record = new TextGenerateRecord();
            record.setUserId(request.getUserId());
            record.setType(TextGenerateType.TRANSLATE.name());

            // 保存输入
            record.setInput(JSON.toJSONString(request));

            // 保存输出
            TextGenerateRecord.Output output = new TextGenerateRecord.Output();
            output.setText(response.getTranslatedText());
            record.setOutput(JSON.toJSONString(output));
            record.setPromptTemplateId(-1L);
            // 保存记录
            textGenerateRecordRepository.save(record);

            // 设置记录ID到响应中
            response.setRecordId(record.getId());

            // 创建文档
            if (StringUtils.isNotBlank(record.getOutput()) && StringUtils.isNotBlank(record.getInput())) {
                try {
                    // 解析输出获取翻译的文本
                    TextGenerateRecord.Output outputObj = JSON.parseObject(record.getOutput(), TextGenerateRecord.Output.class);
                    if (outputObj != null && StringUtils.isNotBlank(outputObj.getText())) {
                        createTranslateDocument(record, outputObj.getText(), request);
                    }
                } catch (Exception e) {
                    log.error("TextTranslateServiceImpl.saveTranslateRecord 创建翻译文档失败", e);
                }
            }
        } catch (Exception e) {
            log.error("TextTranslateServiceImpl.saveTranslateRecord 保存翻译记录失败, request={}, response={}", JSON.toJSONString(request), JSON.toJSONString(response), e);
        }
    }

    /**
     * 检测文本语言
     *
     * @param request 语言检测请求
     * @return 语言检测结果
     */
    public SingleResult<DetectLanguageResponse> detectLanguage(DetectLanguageRequest request) {
        try {
            log.info("TextTranslateServiceImpl detectLanguage request={}", JSON.toJSONString(request));

            if (StringUtils.isBlank(request.getText())) {
                return (SingleResult<DetectLanguageResponse>) SingleResult.buildFailure("Text cannot be empty");
            }

            // 使用Lingua库检测语言
            String text = request.getText();
            Language detectedLanguage = languageDetector.detectLanguageOf(text);

            // 构建响应对象
            DetectLanguageResponse response = new DetectLanguageResponse();

            // 设置检测到的语言
            String languageCode;
            if (detectedLanguage != Language.UNKNOWN) {
                languageCode = detectedLanguage.getIsoCode639_1().toString();
                
                // 如果检测到的是中文，进一步区分繁体和简体
                if ("zh".equals(languageCode)) {
                    String chineseVariant = detectChineseVariant(text);
                    response.setDetectedLanguage(chineseVariant);
                    log.info("TextTranslateServiceImpl.detectLanguage Chinese variant detected: {}", chineseVariant);
                } else {
                    response.setDetectedLanguage(languageCode);
                }
            } else {
                response.setDetectedLanguage("unknown");
            }

            // 保存记录
            saveDetectionRecord(request, response);

            return SingleResult.buildSuccess(response);
        } catch (Exception e) {
            log.error("TextTranslateServiceImpl.detectLanguage error, request={}", JSON.toJSONString(request), e);
            return (SingleResult<DetectLanguageResponse>) SingleResult.buildFailure("Language detection failed: " + e.getMessage());
        }
    }

    /**
     * 保存语言检测记录
     */
    private void saveDetectionRecord(DetectLanguageRequest request, DetectLanguageResponse response) {
        try {
            TextGenerateRecord record = new TextGenerateRecord();
            record.setUserId(request.getUserId());
            record.setType(TextGenerateType.LANGUAGE_DETECT.name());

            // 保存输入
            record.setInput(JSON.toJSONString(request));

            // 保存输出
            TextGenerateRecord.Output output = new TextGenerateRecord.Output();
            output.setText(response.getDetectedLanguage());
            record.setOutput(JSON.toJSONString(output));

            // 保存记录
            textGenerateRecordRepository.save(record);

            // 设置记录ID到响应中
            response.setRecordId(record.getId());

            // 创建文档
            if (record != null && StringUtils.isNotBlank(record.getOutput()) && StringUtils.isNotBlank(record.getInput())) {
                try {
                    // 解析输出获取检测的语言
                    TextGenerateRecord.Output outputObj = JSON.parseObject(record.getOutput(), TextGenerateRecord.Output.class);
                    if (outputObj != null && StringUtils.isNotBlank(outputObj.getText())) {
                        createDetectionDocument(record, outputObj.getText(), request);
                    }
                } catch (Exception e) {
                    log.error("TextTranslateServiceImpl.saveDetectionRecord 创建语言检测文档失败", e);
                }
            }
        } catch (Exception e) {
            log.error("TextTranslateServiceImpl.saveDetectionRecord 保存语言检测记录失败, request={}, response={}", JSON.toJSONString(request), JSON.toJSONString(response), e);
        }
    }

    /**
     * 创建翻译文档及其版本
     *
     * @param record 文本生成记录
     * @param translatedText 翻译的文本
     * @param request 原始翻译请求对象
     */
    private void createTranslateDocument(TextGenerateRecord record, String translatedText, TranslateTextRequest request) {
        try {
            RewriteDocument document = new RewriteDocument();
            document.setOuterUserId(record.getUserId());
            if (record.getUserId() == null) {
                document.setOuterUserId("");
            }
            document.setSource("WEB");
            document.setType("TRANSLATE");

            // 设置标题，使用翻译文本截取前500个字符
            document.setTitle(StringUtils.substring(translatedText, 0, 500));

            // 保存文档
            rewriteDocumentService.createRewriteDocument(document);

            // 创建翻译文本版本，并保存输入数据
            Object inputData = request != null ? request : JSON.parse(record.getInput());
            rewriteDocumentVersionService.createVersion(document.getId(), translatedText, inputData);
            log.info("TextTranslateServiceImpl.createTranslateDocument 翻译文档创建成功，ID={}", document.getId());
        } catch (Exception e) {
            log.error("TextTranslateServiceImpl.createTranslateDocument 创建翻译文档版本失败", e);
        }
    }

    /**
     * 创建语言检测文档及其版本
     *
     * @param record 文本生成记录
     * @param detectedLanguage 检测到的语言
     * @param request 原始语言检测请求对象
     */
    private void createDetectionDocument(TextGenerateRecord record, String detectedLanguage, DetectLanguageRequest request) {
        try {
            RewriteDocument document = new RewriteDocument();
            document.setOuterUserId(record.getUserId());
            if (record.getUserId() == null) {
                document.setOuterUserId("");
            }
            document.setSource("WEB");
            document.setType("LANGUAGE");

            // 设置标题，使用检测语言和原文本截取前500个字符
            String title = "Language: " + detectedLanguage;
            if (request != null && StringUtils.isNotBlank(request.getText())) {
                title += " - " + StringUtils.substring(request.getText(), 0, 450);
            }
            document.setTitle(StringUtils.substring(title, 0, 500));

            // 保存文档
            rewriteDocumentService.createRewriteDocument(document);

            // 创建语言检测结果版本，并保存输入数据
            Object inputData = request != null ? request : JSON.parse(record.getInput());
            rewriteDocumentVersionService.createVersion(document.getId(), detectedLanguage, inputData);
            log.info("TextTranslateServiceImpl.createDetectionDocument 语言检测文档创建成功，ID={}", document.getId());
        } catch (Exception e) {
            log.error("TextTranslateServiceImpl.createDetectionDocument 创建语言检测文档版本失败", e);
        }
    }
}
