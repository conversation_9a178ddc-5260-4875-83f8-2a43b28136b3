package com.alibaba.copilot.app.service.rewrite.controller;


import com.alibaba.copilot.app.domain.seocopilot.model.PromptTemplate;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShopSiteUser;
import com.alibaba.copilot.app.domain.seocopilot.repository.PromptTemplateRepository;
import com.alibaba.copilot.app.domain.seocopilot.repository.SeoShopSiteUserRepository;
import com.alibaba.copilot.app.domain.seocopilot.request.PromptTemplateQuery;
import com.alibaba.copilot.app.infrastructure.base.switchs.SwitchConfig;
import com.alibaba.copilot.app.service.rewrite.config.RewriteAuthService;
import com.alibaba.copilot.app.service.rewrite.config.UserIdentity;
import com.alibaba.copilot.app.service.rewrite.dto.ListPromptTemplateResponse;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.monitor.annotation.MonitorResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.security.spring.annotation.RawJsonValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping(value = "/text2go/api/prompt-template")
public class PromptTemplateController {

    @Resource
    private PromptTemplateRepository promptTemplateRepository;
    @Resource
    private RewriteAuthService authService;
    @Resource
    private SeoShopSiteUserRepository seoShopSiteUserRepository;

    /**
     * 获取模板列表
     * @param codes 模板编码列表
     * @return 模板列表
     */
    @GetMapping(value = "/list")
    @Monitor(name = "[listPromptTemplate] 获取模板列表", level = Monitor.Level.P0, layer = Monitor.Layer.WEB)
    @MonitorResult(errCode = "code", errMsg = "message")
    @RawJsonValue
    public SingleResult<ListPromptTemplateResponse> listPromptTemplate(@RequestParam(value = "codes", required = false) List<String> codes) {
        UserIdentity currentUserIdentity = authService.getCurrentUserIdentity();
        SeoShopSiteUser seoShopSiteUser = seoShopSiteUserRepository.getByOuterUserId(currentUserIdentity.getUserId());
        String email = seoShopSiteUser.getEmail();
        if (!email.endsWith("@alibaba-inc.com") && !SwitchConfig.rewriteTemplateAllowEmail.contains(email)) {
            return SingleResult.buildFailure("not permission to view this page");
        }

        log.info("PromptTemplateController listPromptTemplate codes={}", JSON.toJSONString(codes));
        
        PromptTemplateQuery query = new PromptTemplateQuery();
        query.setCodes(codes);
        
        List<PromptTemplate> templates = promptTemplateRepository.listPromptTemplate(query);
        
        ListPromptTemplateResponse response = new ListPromptTemplateResponse(templates);
        log.info("PromptTemplateController listPromptTemplate result size={}", templates.size());
        
        return SingleResult.buildSuccess(response);
    }
} 