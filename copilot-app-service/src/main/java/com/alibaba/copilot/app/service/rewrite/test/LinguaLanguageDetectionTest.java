package com.alibaba.copilot.app.service.rewrite.test;

import com.github.pemistahl.lingua.api.Language;
import com.github.pemistahl.lingua.api.LanguageDetector;
import com.github.pemistahl.lingua.api.LanguageDetectorBuilder;

import java.util.Map;
import java.util.Scanner;

/**
 * 语言检测测试类
 * 这个类提供了一个简单的main方法来测试Lingua语言检测功能
 */
public class LinguaLanguageDetectionTest {

    public static void main(String[] args) {
        System.out.println("初始化语言检测器...");
        // 创建语言检测器，使用所有支持的语言
        LanguageDetector detector = LanguageDetectorBuilder.fromAllLanguages()
                .build();
        System.out.println("语言检测器初始化完成，支持" + Language.values().length + "种语言");
        
        Scanner scanner = new Scanner(System.in);
        
        while (true) {
            System.out.println("\n请输入要检测的文本 (输入'exit'退出):");
            String text = scanner.nextLine();
            
            if ("exit".equalsIgnoreCase(text)) {
                System.out.println("退出程序");
                break;
            }
            
            if (text.trim().isEmpty()) {
                System.out.println("文本不能为空，请重新输入");
                continue;
            }
            
            // 检测语言
            long startTime = System.currentTimeMillis();
            Language detectedLanguage = detector.detectLanguageOf(text);
            long endTime = System.currentTimeMillis();
            
            System.out.println("检测结果:");
            System.out.println("检测到的语言: " + detectedLanguage.toString());
            System.out.println("ISO 639-1 代码: " + detectedLanguage.getIsoCode639_1());
            System.out.println("ISO 639-3 代码: " + detectedLanguage.getIsoCode639_3());
            System.out.println("检测耗时: " + (endTime - startTime) + "ms");
            
            // 计算各语言的置信度
            System.out.println("\n各语言置信度 (前5个):");
            Map<Language, Double> confidenceValues = detector.computeLanguageConfidenceValues(text);
            
            confidenceValues.entrySet().stream()
                    .sorted(Map.Entry.<Language, Double>comparingByValue().reversed())
                    .limit(5)
                    .forEach(entry -> {
                        Language language = entry.getKey();
                        Double confidence = entry.getValue();
                        System.out.printf("%-15s: %.4f%n", language.toString(), confidence);
                    });
        }
        
        scanner.close();
    }
}
