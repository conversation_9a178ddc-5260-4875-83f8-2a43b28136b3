package com.alibaba.copilot.app.service.rewrite.service.impl;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.domain.conversation.service.PromptTemplateService;
import com.alibaba.copilot.app.domain.rewrite.entity.RewriteDocument;
import com.alibaba.copilot.app.domain.rewrite.entity.TextGenerateRecord;
import com.alibaba.copilot.app.domain.rewrite.repository.TextGenerateRecordRepository;
import com.alibaba.copilot.app.domain.seocopilot.response.PromptGenerateResponse;
import com.alibaba.copilot.app.service.rewrite.config.RewriteAuthService;
import com.alibaba.copilot.app.service.rewrite.dto.GenerateTextRequest;
import com.alibaba.copilot.app.service.rewrite.dto.TextGenerateRecordQuery;
import com.alibaba.copilot.app.service.rewrite.service.RewriteDocumentService;
import com.alibaba.copilot.app.service.rewrite.service.RewriteDocumentVersionService;
import com.alibaba.copilot.app.service.subscription.dto.PageData;
import com.alibaba.copilot.app.service.subscription.dto.PageResult;
import com.alibaba.copilot.boot.llm.openai.client.OpenAiDefaultClient;
import com.alibaba.copilot.boot.llm.openai.client.auth.AuthTokenSimpleSupplier;
import com.alibaba.copilot.boot.llm.openai.data.completion.chat.ChatCompletionChoice;
import com.alibaba.copilot.boot.llm.openai.data.completion.chat.ChatCompletionChunk;
import com.alibaba.copilot.boot.llm.openai.data.completion.chat.ChatCompletionRequest;
import com.alibaba.copilot.boot.llm.openai.data.completion.chat.ChatMessage;
import com.alibaba.fastjson.JSON;
import com.taobao.common.keycenter.security.Cryptograph;
import com.taobao.eagleeye.EagleEye;
import com.taobao.eagleeye.RpcContext_inner;
import io.reactivex.Flowable;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Service
public class TextGenerateServiceImpl {
    private ExecutorService executorService = Executors.newCachedThreadPool();

    @Value("${key.center.t2g.secret}")
    private String keycenterSecret;
    @Value("${key.center.app.key}")
    private String keyCenterAppKey;
    @Value("${seocopilot.openai.baseUrl}")
    private String baseUrl;
    @Resource
    private Cryptograph cryptograph;
    private OpenAiDefaultClient claudeClient;
    // Qwen 客户端
    private OpenAiDefaultClient qwenClient;
    
    // Qwen API 信息
    private static final String QWEN_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
    private static final String QWEN_API_KEY = "sk-2a2b98ede2234d7b837722603f8c5035";
    private static final String QWEN_MODEL = "qwen-plus-latest";
    
    // OkHttp 客户端
    private OkHttpClient okHttpClient;

    @Resource
    private PromptTemplateService promptTemplateService;

    @Resource
    private TextGenerateRecordRepository textGenerateRecordRepository;

    @Resource
    private RewriteDocumentService rewriteDocumentService;
    @Resource
    private RewriteDocumentVersionService rewriteDocumentVersionService;
    @Resource
    private RewriteAuthService authService;


    @PostConstruct
    public void init() {
        String claudeToken = cryptograph.decrypt(keycenterSecret, keyCenterAppKey);
        claudeClient = new OpenAiDefaultClient(baseUrl, new AuthTokenSimpleSupplier(Collections.singletonList(claudeToken)), Duration.ofMinutes(5));

        // 初始化 Qwen 客户端，直接使用硬编码的 baseUrl 和 token
        String qwenToken = "sk-2a2b98ede2234d7b837722603f8c5035";
        qwenClient = new OpenAiDefaultClient("https://dashscope.aliyuncs.com/compatible-mode/", new AuthTokenSimpleSupplier(Collections.singletonList(qwenToken)), Duration.ofMinutes(5));

        // 初始化 OkHttp 客户端
        okHttpClient = new OkHttpClient.Builder()
                .readTimeout(Duration.ofMinutes(5))
                .build();
    }

    public SseEmitter generate(GenerateTextRequest request) {
        SseEmitter emitter = new SseEmitter(180000L); // 3分钟超时

        executorService.execute(() -> {
            ChatCompletionRequest chatRequest = new ChatCompletionRequest();
            List<ChatMessage> messages = new ArrayList<>();
            chatRequest.setMessages(messages);
            chatRequest.setModel("anthropic.claude-3-7-sonnet-20250219-v1:0");
            chatRequest.setMaxTokens(4096);

            PromptGenerateResponse promptGenerateResponse;
            try {
                promptGenerateResponse = promptTemplateService.renderByCode(request.getType().name(), request.getInput());
                messages.add(new ChatMessage("user", promptGenerateResponse.getPrompt()));
                log.info("TextGenerateServiceImpl_generate_request {} ", JSON.toJSONString(chatRequest));

                TextGenerateRecord record = new TextGenerateRecord();
                record.setType(request.getType().name());
                record.setInput(JSON.toJSONString(request.getInput()));
                record.setPromptTemplateId(promptGenerateResponse.getPromptTemplateId());
                record.setUserId(request.getUserId());

                StringBuilder resultBuffer = new StringBuilder();

                Flowable<ChatCompletionChunk> chatCompletionChunkFlowable = claudeClient.streamChatCompletion(chatRequest);

                // 这里可能会返回null，但不会导致异常
                chatCompletionChunkFlowable
                        .doOnNext(c -> log.info("TextGenerateServiceImpl_generate {} ", JSON.toJSONString(c)))
                        .filter(c -> CollectionUtils.isNotEmpty(c.getChoices()))
                        .map(ChatCompletionChunk::getChoices)
                        .filter(c -> c.get(0) != null)
                        .map(c -> c.get(0))
                        .filter(c -> c.getMessage() != null)
                        .map(ChatCompletionChoice::getMessage)
                        .filter(m -> m.getContent() != null)
                        .map(ChatMessage::getContent)
                        .subscribe(
                                content -> {
                                    try {
                                        resultBuffer.append(content);
                                        // 发送普通消息，使用默认的message事件类型
                                        emitter.send(content);
                                    } catch (IOException e) {
                                        log.error("发送SSE消息失败", e);
                                        emitter.completeWithError(e);
                                    }
                                },
                                error -> {
                                    log.error("TextGenerateServiceImpl_generate error", error);
                                    emitter.completeWithError(error);
                                },
                                () -> {
                                    try {
                                        log.info("TextGenerateServiceImpl_generate completed");
                                        // 保存结果
                                        TextGenerateRecord.Output output = new TextGenerateRecord.Output();
                                        output.setText(resultBuffer.toString());
                                        record.setOutput(JSON.toJSONString(output));
                                        saveOutput(record, request);

                                        // 向前端发送记录ID，使用record_id事件类型
                                        if (record.getId() != null) {
                                            emitter.send(SseEmitter.event()
                                                .name("record_id")
                                                .data(record.getId().toString()));
                                            log.info("TextGenerateServiceImpl_generate 记录ID已发送, id={}", record.getId());
                                        }

                                        emitter.complete();
                                        if (request.getUserId().startsWith("ami")) {
                                            authService.markAnonymousUserAsUsed(request.getUserId(), "TEXT_GENERATE");
                                        }
                                    } catch (Exception e) {
                                        log.error("完成SSE处理失败", e);
                                        emitter.completeWithError(e);
                                    }
                                }
                        );
            } catch (Exception e) {
                log.error("生成文本过程中发生错误", e);
                emitter.completeWithError(e);
            }
        });

        // 设置超时和错误处理
        emitter.onTimeout(() -> log.warn("TextGenerateServiceImpl_generate SSE连接超时"));
        emitter.onError(e -> log.error("TextGenerateServiceImpl_generate SSE连接错误", e));

        return emitter;
    }

    /**
     * 调试版本的生成接口，直接使用前端传入的promptTemplate
     * @param request 包含模板内容和变量的请求
     * @return SSE发射器
     */
    public SseEmitter debugGenerate(DebugGenerateRequest request) {
        SseEmitter emitter = new SseEmitter(180000L); // 3分钟超时
        RpcContext_inner rpcContext = EagleEye.getRpcContext();
        executorService.execute(() -> {
            EagleEye.setRpcContext(rpcContext);
            
            try {
                // 直接使用提供的模板和变量渲染
                String generatedPrompt = promptTemplateService.generate(request.getTemplateContent(), request.getInput());
                
                TextGenerateRecord record = new TextGenerateRecord();
                record.setType("DEBUG");
                record.setInput(JSON.toJSONString(request.getInput()));
                record.setUserId(request.getUserId());
                
                StringBuilder resultBuffer = new StringBuilder();

                // 根据模型选择使用不同的客户端和请求方式
                if (request.getModel() != null && "qwen".equalsIgnoreCase(request.getModel())) {
                    // 使用 OkHttp 直接请求 Qwen API
                    log.info("TextGenerateServiceImpl_debugGenerate 使用 OkHttp 直接请求 Qwen API");
                    streamQwenCompletion(generatedPrompt, emitter, resultBuffer, record, request);
                } else {
                    // 使用 Claude 客户端
                    ChatCompletionRequest chatRequest = new ChatCompletionRequest();
                    List<ChatMessage> messages = new ArrayList<>();
                    chatRequest.setMessages(messages);
                    chatRequest.setModel("anthropic.claude-3-7-sonnet-20250219-v1:0");
                    chatRequest.setMaxTokens(4096);
                    
                    messages.add(new ChatMessage("user", generatedPrompt));
                    log.info("TextGenerateServiceImpl_debugGenerate_request model={}, request={} ",
                            chatRequest.getModel(), JSON.toJSONString(chatRequest));
                    
                    Flowable<ChatCompletionChunk> chatCompletionChunkFlowable = claudeClient.streamChatCompletion(chatRequest);
                    log.info("TextGenerateServiceImpl_debugGenerate 使用 Claude 客户端");

                    // 这里可能会返回null，但不会导致异常
                    chatCompletionChunkFlowable
                            .doOnNext(c -> log.info("TextGenerateServiceImpl_debugGenerate {} ", JSON.toJSONString(c)))
                            .filter(c -> CollectionUtils.isNotEmpty(c.getChoices()))
                            .map(ChatCompletionChunk::getChoices)
                            .filter(c -> c.get(0) != null)
                            .map(c -> c.get(0))
                            .filter(c -> c.getMessage() != null)
                            .map(ChatCompletionChoice::getMessage)
                            .filter(m -> m.getContent() != null)
                            .map(ChatMessage::getContent)
                            .subscribe(
                                    content -> {
                                        try {
                                            resultBuffer.append(content);
                                            // 发送普通消息，使用默认的message事件类型
                                            emitter.send(content);
                                        } catch (IOException e) {
                                            log.error("发送SSE消息失败", e);
                                            emitter.completeWithError(e);
                                        }
                                    },
                                    error -> {
                                        log.error("TextGenerateServiceImpl_debugGenerate error", error);
                                        emitter.completeWithError(error);
                                    },
                                    () -> {
                                        try {
                                            log.info("TextGenerateServiceImpl_debugGenerate completed");
                                            // 保存结果
                                            TextGenerateRecord.Output output = new TextGenerateRecord.Output();
                                            output.setText(resultBuffer.toString());
                                            record.setOutput(JSON.toJSONString(output));
                                            // 保存调试请求对象
                                            saveOutput(record, request);

                                            // 向前端发送记录ID，使用record_id事件类型
                                            if (record.getId() != null) {
                                                emitter.send(SseEmitter.event()
                                                    .name("record_id")
                                                    .data(record.getId().toString()));
                                                log.info("TextGenerateServiceImpl_debugGenerate 记录ID已发送, id={}", record.getId());
                                            }

                                            emitter.complete();
                                        } catch (Exception e) {
                                            log.error("完成SSE处理失败", e);
                                            emitter.completeWithError(e);
                                        }
                                    }
                            );
                }
            } catch (Exception e) {
                log.error("调试生成文本过程中发生错误", e);
                emitter.completeWithError(e);
            }
        });

        // 设置超时和错误处理
        emitter.onTimeout(() -> log.warn("TextGenerateServiceImpl_debugGenerate SSE连接超时"));
        emitter.onError(e -> log.error("TextGenerateServiceImpl_debugGenerate SSE连接错误", e));

        return emitter;
    }

    /**
     * 调试版本生成的请求类
     */
    @Data
    public static class DebugGenerateRequest {
        /**
         * 模板内容
         */
        private String templateContent;

        /**
         * 变量映射
         */
        private Map<String, Object> input;

        /**
         * 用户ID
         */
        private String userId;

        /**
         * 模型选择，可选值：qwen, claude
         * 默认为claude
         */
        private String model;
    }

    private void saveOutput(TextGenerateRecord record) {
        // 调用带请求参数的方法，但传入null表示没有原始请求
        saveOutput(record, null);
    }

    /**
     * 保存输出并创建文档，包含原始请求
     */
    private void saveOutput(TextGenerateRecord record, Object request) {
        log.info("TextGenerateServiceImpl_generate final result{} ", JSON.toJSONString(new Object[]{record}));
        textGenerateRecordRepository.save(record);

        // 创建文档
        if (record != null && StringUtils.isNotBlank(record.getOutput()) && StringUtils.isNotBlank(record.getInput())) {
            try {
                // 解析输出获取生成的文本
                TextGenerateRecord.Output output = JSON.parseObject(record.getOutput(), TextGenerateRecord.Output.class);
                if (output != null && StringUtils.isNotBlank(output.getText())) {
                    createDocument(record, output.getText(), request);
                }
            } catch (Exception e) {
                log.error("创建文档失败", e);
            }
        }
    }

    /**
     * 创建文档及其版本
     *
     * @param record 文本生成记录
     * @param generatedText 生成的文本
     * @param request 原始请求对象
     */
    private void createDocument(TextGenerateRecord record, String generatedText, Object request) {
        RewriteDocument document = new RewriteDocument();
        document.setOuterUserId(record.getUserId());
        if (record.getUserId() == null) {
            document.setOuterUserId("");
        }
        document.setSource("WEB");
        // 适配没有下划线的情况
        String type = record.getType();
        int underscoreIndex = type.indexOf("_");
        document.setType(underscoreIndex != -1 ? type.substring(0, underscoreIndex) : type);

        // 设置标题，使用输入内容截取前500个字符
        document.setTitle(StringUtils.substring(generatedText, 0, 500));

        // 保存文档
        rewriteDocumentService.createRewriteDocument(document);

        // 只创建生成的文本版本
        try {
            // 如果request为null，则创建一个简单的input对象
            Object inputData;
            if (request == null) {
                // 从record中提取输入
                if (StringUtils.isNotBlank(record.getInput())) {
                    try {
                        inputData = JSON.parse(record.getInput());
                    } catch (Exception e) {
                        inputData = record.getInput();
                    }
                } else {
                    inputData = null;
                }
            } else {
                // 直接使用原始请求作为input
                inputData = request;
            }

            // 生成的文本作为唯一版本，并保存输入数据
            rewriteDocumentVersionService.createVersion(document.getId(), generatedText, inputData);
            log.info("文档创建成功，ID={}", document.getId());
        } catch (Exception e) {
            log.error("创建文档版本失败", e);
        }
    }

    /**
     * 分页查询文本生成记录
     *
     * @param query 查询条件
     * @return 分页结果
     */
    public PageResult<TextGenerateRecord> listRecords(TextGenerateRecordQuery query) {
        try {
            if (query == null || query.getUserId() == null) {
                throw new IllegalArgumentException("用户ID不能为空");
            }

            // 调用仓库层查询数据
            PageWrapper<TextGenerateRecord> pageWrapper = textGenerateRecordRepository.getByUserIdAndCodes(
                    query.getUserId(),
                    query.getCodes(),
                    query.getPageNum(),
                    query.getPageSize()
            );

            // 封装为PageResult返回
            PageResult<TextGenerateRecord> pageResult = new PageResult<>();
            PageData<TextGenerateRecord> pageData = new PageData<>();

            pageData.setData(pageWrapper.getList());
            pageData.setTotal(pageWrapper.getTotal());
            pageData.setPageNum(Long.valueOf(query.getPageNum()));
            pageData.setPageSize(Long.valueOf(query.getPageSize()));

            // 计算总页数
            Long totalPage = pageWrapper.getTotal() / query.getPageSize();
            if (pageWrapper.getTotal() % query.getPageSize() != 0) {
                totalPage += 1;
            }
            pageData.setTotalPage(totalPage);

            pageResult.setData(pageData);
            pageResult.setSuccess(true);

            return pageResult;
        } catch (Exception e) {
            log.error("查询文本生成记录异常", e);
            // 返回空结果
            PageResult<TextGenerateRecord> errorResult = new PageResult<>();
            PageData<TextGenerateRecord> emptyData = new PageData<>();
            emptyData.setData(new ArrayList<>());
            emptyData.setTotal(0L);
            emptyData.setPageNum(Long.valueOf(query.getPageNum()));
            emptyData.setPageSize(Long.valueOf(query.getPageSize()));
            emptyData.setTotalPage(0L);

            errorResult.setData(emptyData);
            errorResult.setSuccess(false);
            errorResult.setMessage("查询记录失败：" + e.getMessage());

            return errorResult;
        }
    }

    /**
     * 更新文本生成记录的反馈信息
     *
     * @param recordId 记录ID
     * @param feedbackType 反馈类型
     * @return 是否更新成功
     */
    public boolean updateFeedback(Long recordId, String feedbackType) {
        if (recordId == null || StringUtils.isBlank(feedbackType)) {
            log.warn("TextGenerateServiceImpl_updateFeedback 参数无效, recordId={}, feedbackType={}", recordId, feedbackType);
            return false;
        }

        try {
            // 获取已有记录
            TextGenerateRecord record = textGenerateRecordRepository.getById(recordId);
            if (record == null) {
                log.warn("TextGenerateServiceImpl_updateFeedback 记录不存在, recordId={}", recordId);
                return false;
            }

            // 处理属性
            TextGenerateRecord.Attributes attributes = record.getAttributes();
            if (attributes == null) {
                attributes = new TextGenerateRecord.Attributes();
                record.setAttributes(attributes);
            }

            // 处理反馈列表
            if (attributes.getFeedback() == null) {
                attributes.setFeedback(new ArrayList<>());
            }

            // 添加反馈（如果不存在）
            if (!attributes.getFeedback().contains(feedbackType)) {
                attributes.getFeedback().add(feedbackType);
                log.info("TextGenerateServiceImpl_updateFeedback 添加反馈, recordId={}, feedbackType={}", recordId, feedbackType);
            }

            // 保存更新
            return textGenerateRecordRepository.save(record);
        } catch (Exception e) {
            log.error("TextGenerateServiceImpl_updateFeedback 更新反馈失败", e);
            return false;
        }
    }

    /**
     * 取消文本生成记录的反馈信息
     *
     * @param recordId 记录ID
     * @param feedbackType 反馈类型
     * @return 是否取消成功
     */
    public boolean cancelFeedback(Long recordId, String feedbackType) {
        if (recordId == null || StringUtils.isBlank(feedbackType)) {
            log.warn("TextGenerateServiceImpl_cancelFeedback 参数无效, recordId={}, feedbackType={}", recordId, feedbackType);
            return false;
        }

        try {
            // 获取已有记录
            TextGenerateRecord record = textGenerateRecordRepository.getById(recordId);
            if (record == null) {
                log.warn("TextGenerateServiceImpl_cancelFeedback 记录不存在, recordId={}", recordId);
                return false;
            }

            // 处理属性
            TextGenerateRecord.Attributes attributes = record.getAttributes();
            if (attributes == null || attributes.getFeedback() == null || !attributes.getFeedback().contains(feedbackType)) {
                log.info("TextGenerateServiceImpl_cancelFeedback 反馈不存在, recordId={}, feedbackType={}", recordId, feedbackType);
                return false;
            }

            // 移除反馈
            attributes.getFeedback().remove(feedbackType);
            log.info("TextGenerateServiceImpl_cancelFeedback 移除反馈, recordId={}, feedbackType={}", recordId, feedbackType);

            // 保存更新
            return textGenerateRecordRepository.save(record);
        } catch (Exception e) {
            log.error("TextGenerateServiceImpl_cancelFeedback 取消反馈失败", e);
            return false;
        }
    }

    /**
     * 使用OkHttp直接请求Qwen API进行流式生成
     * 
     * @param prompt 提示文本
     * @param emitter SSE发射器
     * @param resultBuffer 结果缓冲区
     * @param record 文本生成记录
     * @param request 原始请求
     */
    private void streamQwenCompletion(String prompt, SseEmitter emitter, StringBuilder resultBuffer, 
                                     TextGenerateRecord record, DebugGenerateRequest request) {
        try {
            // 构建请求JSON
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("model", QWEN_MODEL);
            requestMap.put("stream", true);
            
            List<Map<String, String>> messages = new ArrayList<>();
            Map<String, String> message = new HashMap<>();
            message.put("role", "user");
            message.put("content", prompt);
            messages.add(message);
            
            requestMap.put("messages", messages);
            
            String requestJson = JSON.toJSONString(requestMap);
            
            log.info("Qwen API请求: {}", requestJson);
            
            // 构建请求体
            RequestBody body = RequestBody.create(
                    MediaType.parse("application/json"), 
                    requestJson
            );
            
            // 构建请求
            Request okRequest = new Request.Builder()
                    .url(QWEN_API_URL)
                    .post(body)
                    .header("Authorization", "Bearer " + QWEN_API_KEY)
                    .header("Content-Type", "application/json")
                    .build();
            
            // 发送请求
            okHttpClient.newCall(okRequest).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    log.error("Qwen API请求失败", e);
                    emitter.completeWithError(e);
                }
                
                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        if (!response.isSuccessful()) {
                            ResponseBody errorBody = response.body();
                            String errorMessage = errorBody != null ? errorBody.string() : "Empty response";
                            log.error("Qwen API请求失败: HTTP {}, {}", response.code(), errorMessage);
                            emitter.completeWithError(new RuntimeException("Qwen API请求失败: " + errorMessage));
                            return;
                        }
                        
                        // 获取响应体
                        ResponseBody responseBody = response.body();
                        if (responseBody == null) {
                            emitter.completeWithError(new RuntimeException("Qwen API返回空响应"));
                            return;
                        }
                        
                        // 创建响应体读取器
                        BufferedReader reader = new BufferedReader(responseBody.charStream());
                        String line;
                        
                        // 逐行读取流式响应
                        while ((line = reader.readLine()) != null) {
                            // 处理每一行数据
                            if (line.isEmpty()) {
                                continue; // 跳过空行
                            }
                            
                            if (line.startsWith("data:")) {
                                // 提取data后的JSON
                                String data = line.substring(5).trim();
                                
                                // 检查是否是流结束标记
                                if ("[DONE]".equals(data)) {
                                    break;
                                }
                                
                                try {
                                    // 解析JSON数据
                                    Map<String, Object> jsonMap = JSON.parseObject(data, Map.class);
                                    List<Map<String, Object>> choices = (List<Map<String, Object>>) jsonMap.get("choices");
                                    
                                    if (choices != null && !choices.isEmpty()) {
                                        Map<String, Object> choice = choices.get(0);
                                        Map<String, Object> delta = (Map<String, Object>) choice.get("delta");
                                        
                                        if (delta != null && delta.containsKey("content")) {
                                            String content = (String) delta.get("content");
                                            if (content != null) {
                                                // 追加到结果缓冲区
                                                resultBuffer.append(content);
                                                // 发送到前端
                                                emitter.send(content);
                                            }
                                        }
                                    }
                                } catch (Exception e) {
                                    log.error("解析Qwen API响应JSON失败: {}", data, e);
                                }
                            }
                        }
                        
                        // 处理完成，保存结果并关闭SSE连接
                        try {
                            log.info("Qwen API流式响应完成");
                            // 保存结果
                            TextGenerateRecord.Output output = new TextGenerateRecord.Output();
                            output.setText(resultBuffer.toString());
                            record.setOutput(JSON.toJSONString(output));
                            saveOutput(record, request);
                            
                            // 向前端发送记录ID
                            if (record.getId() != null) {
                                emitter.send(SseEmitter.event()
                                        .name("record_id")
                                        .data(record.getId().toString()));
                                log.info("记录ID已发送, id={}", record.getId());
                            }
                            
                            emitter.complete();
                        } catch (Exception e) {
                            log.error("处理Qwen API完成事件失败", e);
                            emitter.completeWithError(e);
                        }
                    } catch (Exception e) {
                        log.error("处理Qwen API响应失败", e);
                        emitter.completeWithError(e);
                    } finally {
                        response.close();
                    }
                }
            });
        } catch (Exception e) {
            log.error("创建Qwen API请求失败", e);
            emitter.completeWithError(e);
        }
    }
}
