package com.alibaba.copilot.app.service.rewrite.controller;

import com.alibaba.copilot.app.domain.rewrite.entity.TextGenerateRecord;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShopSiteUser;
import com.alibaba.copilot.app.domain.seocopilot.repository.SeoShopSiteUserRepository;
import com.alibaba.copilot.app.infrastructure.base.switchs.SwitchConfig;
import com.alibaba.copilot.app.service.rewrite.RewriteUserUtil;
import com.alibaba.copilot.app.service.rewrite.config.RewriteAuthService;
import com.alibaba.copilot.app.service.rewrite.config.UserIdentity;
import com.alibaba.copilot.app.service.rewrite.dto.GenerateTextRequest;
import com.alibaba.copilot.app.service.rewrite.dto.TextGenerateFeedbackRequest;
import com.alibaba.copilot.app.service.rewrite.dto.TextGenerateRecordQuery;
import com.alibaba.copilot.app.service.rewrite.service.impl.TextGenerateServiceImpl;
import com.alibaba.copilot.app.service.rewrite.service.impl.TextGenerateServiceImpl.DebugGenerateRequest;
import com.alibaba.copilot.app.service.subscription.dto.PageData;
import com.alibaba.copilot.app.service.subscription.dto.PageResult;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/text2go/api/textGenerator")
public class TextGeneratorController {
    @Resource
    private TextGenerateServiceImpl textGenerateService;
    @Resource
    private RewriteAuthService authService;
    @Resource
    private SeoShopSiteUserRepository seoShopSiteUserRepository;

    @PostMapping(value = "/generate", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter generate(HttpServletRequest servletRequest, @RequestBody GenerateTextRequest request) {
        UserIdentity currentUserIdentity = authService.getCurrentUserIdentity();
        if (currentUserIdentity == null) {
            String anonymousId = RewriteUserUtil.generateAnonymousId(servletRequest);
            anonymousId = "ami_" + anonymousId;
            boolean usedFeature = authService.hasAnonymousUserUsedFeature(anonymousId, "TEXT_GENERATE");
            if (usedFeature) {
                SseEmitter emitter = new SseEmitter();
                try {
                    emitter.send(SseEmitter.event().name("requiredLogin").data("User has used", MediaType.TEXT_PLAIN));
                    emitter.complete();
                } catch (Exception e) {
                    emitter.completeWithError(e);
                }
                return emitter;
            } else {
                request.setUserId(anonymousId);
            }
        } else {
            request.setUserId(currentUserIdentity.getUserId());
        }
        return textGenerateService.generate(request);
    }

    /**
     * 调试版本的生成接口，直接使用前端传入的promptTemplate
     * @param request 包含模板内容和变量的请求
     * @return SSE发射器
     */
    @PostMapping(value = "/tryGenerate", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter debugGenerate(@RequestBody DebugGenerateRequest request) {
        // 获取当前用户信息并进行权限校验
        UserIdentity currentUserIdentity = authService.getCurrentUserIdentity();
        if (currentUserIdentity == null) {
            SseEmitter emitter = new SseEmitter();
            try {
                emitter.send(SseEmitter.event().name("error").data("User not login", MediaType.TEXT_PLAIN));
                emitter.complete();
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
            return emitter;
        }

        // 获取用户邮箱进行校验
        SeoShopSiteUser seoShopSiteUser = seoShopSiteUserRepository.getByOuterUserId(currentUserIdentity.getUserId());
        String email = seoShopSiteUser.getEmail();
        if (!email.endsWith("@alibaba-inc.com") && !SwitchConfig.rewriteTemplateAllowEmail.contains(email)) {
            SseEmitter emitter = new SseEmitter();
            try {
                emitter.send(SseEmitter.event().name("error").data("No permission to access this feature, please contact the administrator", MediaType.TEXT_PLAIN));
                emitter.complete();
            } catch (Exception e) {
                emitter.completeWithError(e);
            }
            return emitter;
        }

        log.info("TextGeneratorController debugGenerate templateContent={}, variableMap={}, model={}",
                request.getTemplateContent(), JSON.toJSONString(request.getInput()), request.getModel());

        // 设置当前用户ID
        request.setUserId(currentUserIdentity.getUserId());

        // 调用服务
        return textGenerateService.debugGenerate(request);
    }

    @GetMapping("/generateRecord")
    public PageResult<TextGenerateRecord> listRecord(@RequestParam(required = false) List<String> codes,
                                                    @RequestParam(defaultValue = "1") Integer pageNum,
                                                    @RequestParam(defaultValue = "100") Integer pageSize) {
        try {
            // 创建查询对象
            TextGenerateRecordQuery query = new TextGenerateRecordQuery();
            // 设置当前用户ID
            query.setUserId(authService.getCurrentUserIdentity().getUserId());
            // 设置查询参数
            query.setCodes(codes);
            query.setPageNum(pageNum);
            query.setPageSize(pageSize);

            // 调用服务查询数据
            return textGenerateService.listRecords(query);
        } catch (Exception e) {
            log.error("查询文本生成记录异常", e);
            // 返回空结果
            PageResult<TextGenerateRecord> errorResult = new PageResult<>();
            PageData<TextGenerateRecord> emptyData = new PageData<>();
            emptyData.setData(new ArrayList<>());
            emptyData.setTotal(0L);
            emptyData.setPageNum(Long.valueOf(pageNum));
            emptyData.setPageSize(Long.valueOf(pageSize));
            emptyData.setTotalPage(0L);

            errorResult.setData(emptyData);
            errorResult.setSuccess(false);
            errorResult.setMessage("Failed to query records: " + e.getMessage());

            return errorResult;
        }
    }

    /**
     * 处理文本生成反馈
     *
     * 前端提交反馈示例：
     * <pre>{@code
     * // 使用前面从SSE中获取的recordId
     * fetch('/text2go/api/textGenerator/feedback', {
     *   method: 'POST',
     *   headers: {
     *     'Content-Type': 'application/json'
     *   },
     *   body: JSON.stringify({
     *     recordId: recordId,
     *     feedbackType: 'LIKE', // 或 'DISLIKE', 'COPY' 等
     *     content: '用户附加反馈内容'
     *   })
     * })
     * .then(response => response.json())
     * .then(data => console.log('反馈结果:', data))
     * .catch(error => console.error('反馈提交错误:', error));
     * }</pre>
     *
     * @param request 反馈请求
     * @return 处理结果
     */
    @PostMapping("/feedback")
    public SingleResult<Boolean> feedback(@RequestBody TextGenerateFeedbackRequest request) {
        log.info("收到反馈请求: {}", JSON.toJSONString(request));

        try {
            // 参数校验
            if (request.getRecordId() == null || request.getFeedBackType() == null) {
                return SingleResult.buildFailure("Incomplete parameters, recordId and feedbackType are required");
            }

            // 更新反馈
            boolean success = textGenerateService.updateFeedback(request.getRecordId(), request.getFeedBackType());

            // 返回结果
            if (success) {
                return SingleResult.buildSuccess(Boolean.TRUE);
            } else {
                return SingleResult.buildFailure("Feedback processing failed");
            }
        } catch (Exception e) {
            log.error("处理反馈时出错", e);
            return SingleResult.buildFailure("Error processing feedback: " + e.getMessage());
        }
    }

    /**
     * 取消文本生成反馈
     *
     * 前端取消反馈示例：
     * <pre>{@code
     * // 使用前面从SSE中获取的recordId
     * fetch('/text2go/api/textGenerator/cancelFeedback', {
     *   method: 'POST',
     *   headers: {
     *     'Content-Type': 'application/json'
     *   },
     *   body: JSON.stringify({
     *     recordId: recordId,
     *     feedBackType: 'LIKE', // 或 'DISLIKE', 'COPY' 等
     *   })
     * })
     * .then(response => response.json())
     * .then(data => console.log('取消反馈结果:', data))
     * .catch(error => console.error('取消反馈提交错误:', error));
     * }</pre>
     *
     * @param request 反馈请求
     * @return 处理结果
     */
    @PostMapping("/cancelFeedback")
    public SingleResult<Boolean> cancelFeedback(@RequestBody TextGenerateFeedbackRequest request) {
        log.info("收到取消反馈请求: {}", JSON.toJSONString(request));

        try {
            // 参数校验
            if (request.getRecordId() == null || request.getFeedBackType() == null) {
                return SingleResult.buildFailure("Incomplete parameters, recordId and feedbackType are required");
            }

            // 取消反馈
            boolean success = textGenerateService.cancelFeedback(request.getRecordId(), request.getFeedBackType());

            // 返回结果
            if (success) {
                return SingleResult.buildSuccess(Boolean.TRUE);
            } else {
                return SingleResult.buildFailure("No corresponding feedback found or cancellation failed");
            }
        } catch (Exception e) {
            log.error("处理取消反馈时出错", e);
            return SingleResult.buildFailure("Error processing feedback cancellation: " + e.getMessage());
        }
    }
}
