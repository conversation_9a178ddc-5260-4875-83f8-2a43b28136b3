package com.alibaba.copilot.app.service.rewrite.dto;

import lombok.Data;

import java.util.List;

/**
 * 文本生成记录查询对象
 */
@Data
public class TextGenerateRecordQuery {
    /**
     * 用户ID（必填，在Service层填充）
     */
    private String userId;
    
    /**
     * 类型代码列表（可选）
     */
    private List<String> codes;
    
    /**
     * 页码（默认为1）
     */
    private Integer pageNum = 1;
    
    /**
     * 每页大小（默认为10）
     */
    private Integer pageSize = 10;
} 