package com.alibaba.copilot.app.service.rewrite.controller;

import com.alibaba.copilot.app.domain.rewrite.entity.TextGenerateRecord;
import com.alibaba.copilot.app.domain.seocopilot.repository.SeoShopSiteUserRepository;
import com.alibaba.copilot.app.service.rewrite.RewriteUserUtil;
import com.alibaba.copilot.app.service.rewrite.config.RewriteAuthService;
import com.alibaba.copilot.app.service.rewrite.config.UserIdentity;
import com.alibaba.copilot.app.service.rewrite.constant.RewriteErrorCode;
import com.alibaba.copilot.app.service.rewrite.dto.*;
import com.alibaba.copilot.app.service.rewrite.service.impl.TextTranslateServiceImpl;
import com.alibaba.copilot.app.service.subscription.dto.PageData;
import com.alibaba.copilot.app.service.subscription.dto.PageResult;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/text2go/api/textTranslator")
public class TextTranslatorController {
    @Resource
    private TextTranslateServiceImpl textTranslateService;
    @Resource
    private RewriteAuthService authService;
    @Resource
    private SeoShopSiteUserRepository seoShopSiteUserRepository;

    @PostMapping(value = "/translate")
    public SingleResult<TranslateTextResponse> translate(HttpServletRequest servletRequest, @RequestBody TranslateTextRequest request) {
        UserIdentity currentUserIdentity = authService.getCurrentUserIdentity();
        if (currentUserIdentity == null) {
            String anonymousId = RewriteUserUtil.generateAnonymousId(servletRequest);
            anonymousId = "ami_" + anonymousId;
            boolean usedFeature = authService.hasAnonymousUserUsedFeature(anonymousId, "TEXT_TRANSLATE");
            if (usedFeature) {
                return SingleResult.buildFailure(
                        RewriteErrorCode.GUEST_ACCESS_LIMIT.getCode(),
                        RewriteErrorCode.GUEST_ACCESS_LIMIT.getMessage()
                );
            }
            request.setUserId(anonymousId);
        } else {
            request.setUserId(currentUserIdentity.getUserId());
        }

        log.info("TextTranslatorController translate request={}", JSON.toJSONString(request));
        SingleResult<TranslateTextResponse> result = textTranslateService.translate(request);

        // 如果翻译成功且用户是匿名用户，标记为已使用
        if (result.isSuccess() && request.getUserId().startsWith("ami")) {
            authService.markAnonymousUserAsUsed(request.getUserId(), "TEXT_TRANSLATE");
        }

        return result;
    }

    /**
     * 检测文本语言
     *
     * @param servletRequest HTTP请求
     * @param request 语言检测请求
     * @return 语言检测结果
     */
    @PostMapping(value = "/detectLanguage")
    public SingleResult<DetectLanguageResponse> detectLanguage(HttpServletRequest servletRequest, @RequestBody DetectLanguageRequest request) {
        UserIdentity currentUserIdentity = authService.getCurrentUserIdentity();
        if (currentUserIdentity == null) {
            String anonymousId = RewriteUserUtil.generateAnonymousId(servletRequest);
            anonymousId = "ami_" + anonymousId;
            request.setUserId(anonymousId);
        } else {
            request.setUserId(currentUserIdentity.getUserId());
        }
        log.info("TextTranslatorController detectLanguage request={}", JSON.toJSONString(request));
        SingleResult<DetectLanguageResponse> result = textTranslateService.detectLanguage(request);

        return result;
    }


}
