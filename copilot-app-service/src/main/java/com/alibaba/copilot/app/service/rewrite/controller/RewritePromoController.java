package com.alibaba.copilot.app.service.rewrite.controller;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.copilot.app.domain.base.repository.CacheRepository;
import com.alibaba.copilot.app.domain.rewrite.entity.RewriteEventLog;
import com.alibaba.copilot.app.domain.seocopilot.constant.CacheKeyConst;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShopSiteUser;
import com.alibaba.copilot.app.domain.seocopilot.repository.SeoShopSiteUserRepository;
import com.alibaba.copilot.app.domain.seocopilot.service.AccountService;
import com.alibaba.copilot.app.infrastructure.base.dto.RegionalPromoConfig;
import com.alibaba.copilot.app.infrastructure.base.switchs.SwitchConfig;
import com.alibaba.copilot.app.service.rewrite.RewriteUserUtil;
import com.alibaba.copilot.app.service.rewrite.config.RewriteAuthService;
import com.alibaba.copilot.app.service.rewrite.config.UserIdentity;
import com.alibaba.copilot.app.service.rewrite.dto.ApplyDiscountRequest;
import com.alibaba.copilot.app.service.rewrite.dto.RewritePromoInfoConfig;
import com.alibaba.copilot.app.service.rewrite.service.RewriteEventLogService;
import com.alibaba.copilot.app.service.rewrite.vo.ApplyDiscountResponse;
import com.alibaba.copilot.app.service.rewrite.vo.PromoInfoVO;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifySubscribedPlanQueryDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscriptionOrderResult;
import com.alibaba.copilot.enabler.client.subscription.facade.SubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.request.SubscriptionOrderQueryDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.fastjson.JSON;
import com.aliexpress.geoip.GeoipLocation;
import com.aliexpress.geoip.GeoipService;
import com.google.common.collect.Lists;
import io.fury.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.Instant;

@Slf4j
@RestController
@RequestMapping("/text2go/promo")
public class RewritePromoController {
    @Resource
    private RewriteAuthService authService;
    @Resource
    private RewriteEventLogService rewriteEventLogService;
    @Resource
    private SubscriptionHsfApi subscriptionHsfApi;
    @Resource
    private AccountService accountService;
    @Resource
    private SeoShopSiteUserRepository seoShopSiteUserRepository;
    @Resource
    private CacheRepository cacheRepository;
    @Resource
    private GeoipService geoipService;

    @GetMapping("/promoInfo")
    public SingleResult<PromoInfoVO> currentPromo(HttpServletRequest servletRequest) {
        UserIdentity currentUserIdentity = authService.getCurrentUserIdentity();
        PromoInfoVO noShowResult = new PromoInfoVO();
        noShowResult.setShowPromoInfo(Boolean.FALSE);
        if (currentUserIdentity == null || !currentUserIdentity.isLoggedIn() || StringUtils.isBlank(currentUserIdentity.getUserId())) {
            return SingleResult.buildSuccess(noShowResult);
        }
        RewritePromoInfoConfig rewritePromoInfoConfig = JSON.parseObject(SwitchConfig.t2gPromoInfo, RewritePromoInfoConfig.class);
        if (rewritePromoInfoConfig == null ) {
            return SingleResult.buildSuccess(noShowResult);
        }
        if (rewritePromoInfoConfig.getStartTime() == null || rewritePromoInfoConfig.getEndTime() == null) {
            return SingleResult.buildSuccess(noShowResult);
        }
        if (rewritePromoInfoConfig.getStartTime()>Instant.now().getEpochSecond()
                || Instant.now().getEpochSecond()>=rewritePromoInfoConfig.getEndTime()) {
            return SingleResult.buildSuccess(noShowResult);
        }

        if (rewritePromoInfoConfig.getGrayPercentage() == null) {
            return SingleResult.buildSuccess(noShowResult);
        }
        if (!RewriteUserUtil.hitGray(rewritePromoInfoConfig.getDiscountCode() + currentUserIdentity.getUserId()
                , rewritePromoInfoConfig.getGrayPercentage())) {
            return SingleResult.buildSuccess(noShowResult);
        }

        PromoInfoVO promoInfoVO = JSON.parseObject(SwitchConfig.t2gPromoInfo, PromoInfoVO.class);

        // 无论是否显示原本的优惠信息，都尝试获取地区优惠信息
        applyRegionalPromoCode(servletRequest, promoInfoVO);

        if (!Boolean.TRUE.equals(promoInfoVO.getShowPromoInfo())) {
            // 即使不显示原本的优惠，如果有地区优惠也要返回
            if (StringUtils.isNotBlank(promoInfoVO.getRegionalDiscountCode())) {
                // 创建一个只包含地区优惠信息的结果
                PromoInfoVO regionalOnlyResult = new PromoInfoVO();
                regionalOnlyResult.setShowPromoInfo(Boolean.TRUE);
                regionalOnlyResult.setRegionalDiscountCode(promoInfoVO.getRegionalDiscountCode());
                regionalOnlyResult.setRegionalDiscountDescription(promoInfoVO.getRegionalDiscountDescription());
                regionalOnlyResult.setRegionalDiscountPercentage(promoInfoVO.getRegionalDiscountPercentage());
                regionalOnlyResult.setRegionalName(promoInfoVO.getRegionalName());
                regionalOnlyResult.setRegionalBannerText(promoInfoVO.getRegionalBannerText());
                regionalOnlyResult.setCountryCode(promoInfoVO.getCountryCode());
                regionalOnlyResult.setApplied(Boolean.FALSE); // 地区优惠默认未应用
                return SingleResult.buildSuccess(regionalOnlyResult);
            }
            return SingleResult.buildSuccess(noShowResult);
        }

        // 获取当前用户是否已经订阅
        // 构建订阅计划查询参数
        boolean subscribed = checkSubscribed(currentUserIdentity);
        if (subscribed) {
            return SingleResult.buildSuccess(noShowResult);
        }

        // 获取当前用户是否已经 APPLIED
        Object appliedResult = cacheRepository.get(CacheKeyConst
                .getUserAppliedDiscount(currentUserIdentity.getUserId(), promoInfoVO.getDiscountCode()));

        promoInfoVO.setApplied(String.valueOf(true).equals(appliedResult));
        RewriteEventLog rewriteEventLog = new RewriteEventLog();
        rewriteEventLog.setEventType("SHOW_PROMO_PAGE_BANNER");
        rewriteEventLog.setEventData(SwitchConfig.t2gPromoInfo);
        rewriteEventLog.setUserId(currentUserIdentity.getUserId());
        rewriteEventLogService.save(rewriteEventLog);
        return SingleResult.buildSuccess(promoInfoVO);
    }

    private boolean checkSubscribed(UserIdentity currentUserIdentity) {
        SeoShopSiteUser seoShopSiteUser = seoShopSiteUserRepository.getByOuterUserId(currentUserIdentity.getUserId());
        Long transferredUserId = accountService.getUserIdTransferred(seoShopSiteUser.getId());
        ShopifySubscribedPlanQueryDTO queryDTO = new ShopifySubscribedPlanQueryDTO();
        queryDTO.setUserId(transferredUserId);
        queryDTO.setAppCode(AppEnum.TEXT2GO.getCode());

        // 获取订阅计划信息
        SubscriptionOrderQueryDTO subscriptionOrderQueryDTO = new SubscriptionOrderQueryDTO();
        subscriptionOrderQueryDTO.setAppCode(AppEnum.TEXT2GO.getCode());
        subscriptionOrderQueryDTO.setUserId(transferredUserId);
        subscriptionOrderQueryDTO.setStatusList(Lists.newArrayList(SubscriptionOrderStatus.IN_EFFECT));
        SingleResult<SubscriptionOrderResult> orderResultSingleResult = subscriptionHsfApi.querySubscribeOrder(subscriptionOrderQueryDTO);
        if (!orderResultSingleResult.isSuccess()) {
            log.error("currentPromo querySubscribeOrder failed {} result {}"
                    , JSON.toJSONString(queryDTO), JSON.toJSONString(orderResultSingleResult));
        }
        SubscriptionOrderResult result = orderResultSingleResult.getData();
        return result != null && CollectionUtils.isNotEmpty(result.getOrderList());
    }

    @PostMapping("apply")
    public SingleResult<ApplyDiscountResponse> apply(@RequestBody ApplyDiscountRequest request) {
        UserIdentity currentUserIdentity = authService.getCurrentUserIdentity();
        RewriteEventLog rewriteEventLog = new RewriteEventLog();
        rewriteEventLog.setEventType("APPLIED_PROMO_PAGE_BANNER");
        rewriteEventLog.setEventData(SwitchConfig.t2gPromoInfo);
        rewriteEventLog.setUserId(currentUserIdentity.getUserId());
        rewriteEventLogService.save(rewriteEventLog);
        cacheRepository.put(CacheKeyConst
                .getUserAppliedDiscount(currentUserIdentity.getUserId(), request.getDiscountCode()), true, 86400L * 365);
        ApplyDiscountResponse response = new ApplyDiscountResponse();
        response.setDiscountCode(request.getDiscountCode());
        return SingleResult.buildSuccess(response);
    }

    /**
     * 根据用户地区应用相应的优惠码
     *
     * @param servletRequest HTTP请求，用于获取客户端IP
     * @param promoInfoVO 优惠信息VO，将被修改以添加地区优惠信息
     */
    private void applyRegionalPromoCode(HttpServletRequest servletRequest, PromoInfoVO promoInfoVO) {
        try {
            // 获取客户端IP
            String clientIP = ServletUtil.getClientIP(servletRequest);
            log.info("applyRegionalPromoCode: clientIP={}", clientIP);

            if (StringUtils.isBlank(clientIP)) {
                log.warn("applyRegionalPromoCode: clientIP is blank, no regional promo applied");
                return;
            }

            // 使用GeoIpService获取地理位置信息
            GeoipLocation geoipLocation = geoipService.query(clientIP, "en");
            log.info("applyRegionalPromoCode: geoipLocation={}", JSON.toJSONString(geoipLocation));

            if (geoipLocation == null || StringUtils.isBlank(geoipLocation.getCountryCode())) {
                log.warn("applyRegionalPromoCode: unable to get country code, no regional promo applied");
                return;
            }

            String countryCode = geoipLocation.getCountryCode();
            log.info("applyRegionalPromoCode: countryCode={}", countryCode);

            // 设置国家代码
            promoInfoVO.setCountryCode(countryCode);

            // 检查是否有该地区的特殊优惠配置
            RegionalPromoConfig regionalConfig = SwitchConfig.regionalPromoConfigs.get(countryCode);
            if (regionalConfig != null) {
                log.info("applyRegionalPromoCode: found regional config for {}: {}", countryCode, JSON.toJSONString(regionalConfig));

                // 设置地区优惠信息到单独的字段，不覆盖原有字段
                promoInfoVO.setRegionalDiscountCode(regionalConfig.getDiscountCode());
                promoInfoVO.setRegionalDiscountDescription(regionalConfig.getDiscountDescription());

                if (regionalConfig.getDiscountPercentage() != null) {
                    promoInfoVO.setRegionalDiscountPercentage(regionalConfig.getDiscountPercentage());
                }
                if (StringUtils.isNotBlank(regionalConfig.getName())) {
                    promoInfoVO.setRegionalName(regionalConfig.getName());
                }
                if (StringUtils.isNotBlank(regionalConfig.getBannerText())) {
                    promoInfoVO.setRegionalBannerText(regionalConfig.getBannerText());
                }

                log.info("applyRegionalPromoCode: applied regional promo for {}, regionalDiscountCode={}", countryCode, regionalConfig.getDiscountCode());
            } else {
                log.info("applyRegionalPromoCode: no regional config found for {}, no regional promo applied", countryCode);
            }

        } catch (Exception e) {
            log.error("applyRegionalPromoCode: error occurred, no regional promo applied", e);
            // 发生异常时不设置地区优惠，不影响正常流程
        }
    }
}
