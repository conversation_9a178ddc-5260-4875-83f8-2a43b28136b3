package com.alibaba.copilot.app.service.rewrite.controller;

import com.alibaba.copilot.app.domain.base.repository.CacheRepository;
import com.alibaba.copilot.app.domain.rewrite.entity.RewriteEventLog;
import com.alibaba.copilot.app.domain.seocopilot.constant.CacheKeyConst;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShopSiteUser;
import com.alibaba.copilot.app.domain.seocopilot.repository.SeoShopSiteUserRepository;
import com.alibaba.copilot.app.domain.seocopilot.service.AccountService;
import com.alibaba.copilot.app.infrastructure.base.switchs.SwitchConfig;
import com.alibaba.copilot.app.service.rewrite.RewriteUserUtil;
import com.alibaba.copilot.app.service.rewrite.config.RewriteAuthService;
import com.alibaba.copilot.app.service.rewrite.config.UserIdentity;
import com.alibaba.copilot.app.service.rewrite.dto.ApplyDiscountRequest;
import com.alibaba.copilot.app.service.rewrite.dto.RewritePromoInfoConfig;
import com.alibaba.copilot.app.service.rewrite.service.RewriteEventLogService;
import com.alibaba.copilot.app.service.rewrite.vo.ApplyDiscountResponse;
import com.alibaba.copilot.app.service.rewrite.vo.PromoInfoVO;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.constant.SubscriptionOrderStatus;
import com.alibaba.copilot.enabler.client.subscription.dto.ShopifySubscribedPlanQueryDTO;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscriptionOrderResult;
import com.alibaba.copilot.enabler.client.subscription.facade.SubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.request.SubscriptionOrderQueryDTO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import io.fury.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.time.Instant;

@Slf4j
@RestController
@RequestMapping("/text2go/promo")
public class RewritePromoController {
    @Resource
    private RewriteAuthService authService;
    @Resource
    private RewriteEventLogService rewriteEventLogService;
    @Resource
    private SubscriptionHsfApi subscriptionHsfApi;
    @Resource
    private AccountService accountService;
    @Resource
    private SeoShopSiteUserRepository seoShopSiteUserRepository;
    @Resource
    private CacheRepository cacheRepository;

    @GetMapping("/promoInfo")
    public SingleResult<PromoInfoVO> currentPromo() {
        UserIdentity currentUserIdentity = authService.getCurrentUserIdentity();
        PromoInfoVO noShowResult = new PromoInfoVO();
        noShowResult.setShowPromoInfo(Boolean.FALSE);
        if (currentUserIdentity == null || !currentUserIdentity.isLoggedIn() || StringUtils.isBlank(currentUserIdentity.getUserId())) {
            return SingleResult.buildSuccess(noShowResult);
        }
        RewritePromoInfoConfig rewritePromoInfoConfig = JSON.parseObject(SwitchConfig.t2gPromoInfo, RewritePromoInfoConfig.class);
        if (rewritePromoInfoConfig == null ) {
            return SingleResult.buildSuccess(noShowResult);
        }
        if (rewritePromoInfoConfig.getStartTime() == null || rewritePromoInfoConfig.getEndTime() == null) {
            return SingleResult.buildSuccess(noShowResult);
        }
        if (rewritePromoInfoConfig.getStartTime()>Instant.now().getEpochSecond()
                || Instant.now().getEpochSecond()>=rewritePromoInfoConfig.getEndTime()) {
            return SingleResult.buildSuccess(noShowResult);
        }

        if (rewritePromoInfoConfig.getGrayPercentage() == null) {
            return SingleResult.buildSuccess(noShowResult);
        }
        if (!RewriteUserUtil.hitGray(rewritePromoInfoConfig.getDiscountCode() + currentUserIdentity.getUserId()
                , rewritePromoInfoConfig.getGrayPercentage())) {
            return SingleResult.buildSuccess(noShowResult);
        }

        PromoInfoVO promoInfoVO = JSON.parseObject(SwitchConfig.t2gPromoInfo, PromoInfoVO.class);
        if (!Boolean.TRUE.equals(promoInfoVO.getShowPromoInfo())) {
            return SingleResult.buildSuccess(noShowResult);

        }

        // 获取当前用户是否已经订阅
        // 构建订阅计划查询参数
        boolean subscribed = checkSubscribed(currentUserIdentity);
        if (subscribed) {
            return SingleResult.buildSuccess(noShowResult);
        }

        // 获取当前用户是否已经 APPLIED
        Object appliedResult = cacheRepository.get(CacheKeyConst
                .getUserAppliedDiscount(currentUserIdentity.getUserId(), promoInfoVO.getDiscountCode()));

        promoInfoVO.setApplied(String.valueOf(true).equals(appliedResult));
        RewriteEventLog rewriteEventLog = new RewriteEventLog();
        rewriteEventLog.setEventType("SHOW_PROMO_PAGE_BANNER");
        rewriteEventLog.setEventData(SwitchConfig.t2gPromoInfo);
        rewriteEventLog.setUserId(currentUserIdentity.getUserId());
        rewriteEventLogService.save(rewriteEventLog);
        return SingleResult.buildSuccess(promoInfoVO);
    }

    private boolean checkSubscribed(UserIdentity currentUserIdentity) {
        SeoShopSiteUser seoShopSiteUser = seoShopSiteUserRepository.getByOuterUserId(currentUserIdentity.getUserId());
        Long transferredUserId = accountService.getUserIdTransferred(seoShopSiteUser.getId());
        ShopifySubscribedPlanQueryDTO queryDTO = new ShopifySubscribedPlanQueryDTO();
        queryDTO.setUserId(transferredUserId);
        queryDTO.setAppCode(AppEnum.TEXT2GO.getCode());

        // 获取订阅计划信息
        SubscriptionOrderQueryDTO subscriptionOrderQueryDTO = new SubscriptionOrderQueryDTO();
        subscriptionOrderQueryDTO.setAppCode(AppEnum.TEXT2GO.getCode());
        subscriptionOrderQueryDTO.setUserId(transferredUserId);
        subscriptionOrderQueryDTO.setStatusList(Lists.newArrayList(SubscriptionOrderStatus.IN_EFFECT));
        SingleResult<SubscriptionOrderResult> orderResultSingleResult = subscriptionHsfApi.querySubscribeOrder(subscriptionOrderQueryDTO);
        if (!orderResultSingleResult.isSuccess()) {
            log.error("currentPromo querySubscribeOrder failed {} result {}"
                    , JSON.toJSONString(queryDTO), JSON.toJSONString(orderResultSingleResult));
        }
        SubscriptionOrderResult result = orderResultSingleResult.getData();
        return result != null && CollectionUtils.isNotEmpty(result.getOrderList());
    }

    @PostMapping("apply")
    public SingleResult<ApplyDiscountResponse> apply(@RequestBody ApplyDiscountRequest request) {
        UserIdentity currentUserIdentity = authService.getCurrentUserIdentity();
        RewriteEventLog rewriteEventLog = new RewriteEventLog();
        rewriteEventLog.setEventType("APPLIED_PROMO_PAGE_BANNER");
        rewriteEventLog.setEventData(SwitchConfig.t2gPromoInfo);
        rewriteEventLog.setUserId(currentUserIdentity.getUserId());
        rewriteEventLogService.save(rewriteEventLog);
        cacheRepository.put(CacheKeyConst
                .getUserAppliedDiscount(currentUserIdentity.getUserId(), request.getDiscountCode()), true, 86400L * 365);
        ApplyDiscountResponse response = new ApplyDiscountResponse();
        response.setDiscountCode(request.getDiscountCode());
        return SingleResult.buildSuccess(response);
    }
}
