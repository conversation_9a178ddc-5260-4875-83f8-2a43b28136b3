package com.alibaba.copilot.app.service.rewrite.dto;

import com.alibaba.copilot.app.service.rewrite.enums.TextGenerateType;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * Request DTO for language detection
 */
@Data
public class DetectLanguageRequest {
    /**
     * User ID (will be set by the controller)
     */
    private String userId;

    /**
     * Text to detect language for
     */
    private String text;

    /**
     * Convert to GenerateTextRequest
     *
     * @return GenerateTextRequest object
     */
    public GenerateTextRequest toGenerateTextRequest() {
        GenerateTextRequest request = new GenerateTextRequest();
        request.setUserId(this.userId);
        request.setType(TextGenerateType.LANGUAGE_DETECT);

        // Build input parameters
        Map<String, Object> input = new HashMap<>();
        input.put("text", this.text);

        request.setInput(input);
        return request;
    }
}
