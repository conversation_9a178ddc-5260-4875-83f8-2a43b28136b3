package com.alibaba.copilot.app.service.rewrite.dto;

import com.alibaba.copilot.app.service.rewrite.enums.TextGenerateType;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class TranslateTextRequest {
    /**
     * 用户ID
     */
    private String userId;

    /**
     * 翻译文本
     */
    private String text;

    /**
     * 源语言代码，不传入则自动检测语种
     */
    private String sourceLanguage;

    /**
     * 目标语言代码
     */
    private String targetLanguage;

    /**
     * 转换为 GenerateTextRequest
     *
     * @return GenerateTextRequest 对象
     */
    public GenerateTextRequest toGenerateTextRequest() {
        GenerateTextRequest request = new GenerateTextRequest();
        request.setUserId(this.userId);
        request.setType(TextGenerateType.TRANSLATE);

        // 构建输入参数
        Map<String, Object> input = new HashMap<>();
        input.put("text", this.text);
        input.put("sourceLanguage", this.sourceLanguage);
        input.put("targetLanguage", this.targetLanguage);

        request.setInput(input);
        return request;
    }
}
