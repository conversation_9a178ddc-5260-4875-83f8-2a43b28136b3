package com.alibaba.copilot.app.service.rewrite.service.impl;

import com.alibaba.fastjson.JSON;
import okhttp3.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.IOException;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * 用于测试直接调用Qwen API的测试类
 */
public class QwenApiTester {

    private static final String QWEN_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
    private static final String QWEN_API_KEY = "sk-2a2b98ede2234d7b837722603f8c5035";
    private static final String QWEN_MODEL = "qwen-plus-latest";
    
    public static void main(String[] args) throws Exception {
        // 创建一个OkHttpClient实例
        OkHttpClient okHttpClient = new OkHttpClient.Builder()
                .readTimeout(Duration.ofMinutes(5))
                .build();
        
        // 创建测试请求
        String prompt = "请介绍一下你自己，你是什么模型?";
        
        // 构建请求JSON
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("model", QWEN_MODEL);
        requestMap.put("stream", true);
        
        List<Map<String, String>> messages = new ArrayList<>();
        Map<String, String> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", prompt);
        messages.add(message);
        
        requestMap.put("messages", messages);
        
        String requestJson = JSON.toJSONString(requestMap);
        
        System.out.println("Qwen API请求: " + requestJson);
        
        // 构建请求体
        RequestBody body = RequestBody.create(
                MediaType.parse("application/json"), 
                requestJson
        );
        
        // 构建请求
        Request okRequest = new Request.Builder()
                .url(QWEN_API_URL)
                .post(body)
                .header("Authorization", "Bearer " + QWEN_API_KEY)
                .header("Content-Type", "application/json")
                .build();
        
        // 创建一个计数器等待请求完成
        CountDownLatch latch = new CountDownLatch(1);
        final StringBuilder resultBuffer = new StringBuilder();
        
        // 发送请求
        okHttpClient.newCall(okRequest).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                System.err.println("Qwen API请求失败: " + e.getMessage());
                e.printStackTrace();
                latch.countDown();
            }
            
            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (!response.isSuccessful()) {
                        ResponseBody errorBody = response.body();
                        String errorMessage = errorBody != null ? errorBody.string() : "Empty response";
                        System.err.println("Qwen API请求失败: HTTP " + response.code() + ", " + errorMessage);
                        latch.countDown();
                        return;
                    }
                    
                    // 获取响应体
                    ResponseBody responseBody = response.body();
                    if (responseBody == null) {
                        System.err.println("Qwen API返回空响应");
                        latch.countDown();
                        return;
                    }
                    
                    // 创建响应体读取器
                    BufferedReader reader = new BufferedReader(responseBody.charStream());
                    String line;
                    
                    System.out.println("开始接收Qwen API响应:");
                    
                    // 逐行读取流式响应
                    while ((line = reader.readLine()) != null) {
                        // 处理每一行数据
                        if (line.isEmpty()) {
                            continue; // 跳过空行
                        }
                        
                        System.out.println("原始响应行: " + line);
                        
                        if (line.startsWith("data:")) {
                            // 提取data后的JSON
                            String data = line.substring(5).trim();
                            
                            // 检查是否是流结束标记
                            if ("[DONE]".equals(data)) {
                                System.out.println("接收到流结束标记");
                                break;
                            }
                            
                            try {
                                // 解析JSON数据
                                Map<String, Object> jsonMap = JSON.parseObject(data, Map.class);
                                System.out.println("解析后的JSON: " + JSON.toJSONString(jsonMap));
                                
                                List<Map<String, Object>> choices = (List<Map<String, Object>>) jsonMap.get("choices");
                                
                                if (choices != null && !choices.isEmpty()) {
                                    Map<String, Object> choice = choices.get(0);
                                    Map<String, Object> delta = (Map<String, Object>) choice.get("delta");
                                    
                                    if (delta != null && delta.containsKey("content")) {
                                        String content = (String) delta.get("content");
                                        if (content != null) {
                                            // 追加到结果缓冲区
                                            resultBuffer.append(content);
                                            System.out.println("收到内容片段: " + content);
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                System.err.println("解析Qwen API响应JSON失败: " + data);
                                e.printStackTrace();
                            }
                        }
                    }
                    
                    System.out.println("\n完整响应内容:");
                    System.out.println(resultBuffer.toString());
                    
                } catch (Exception e) {
                    System.err.println("处理Qwen API响应失败");
                    e.printStackTrace();
                } finally {
                    response.close();
                    latch.countDown();
                }
            }
        });
        
        // 等待请求完成，最多等待3分钟
        latch.await(3, TimeUnit.MINUTES);
        System.out.println("测试完成");
    }
    
    /**
     * 使用回调的方式模拟SseEmitter
     */
    static class TestSseEmitter extends SseEmitter {
        public TestSseEmitter() {
            super(180000L);
        }
        
        @Override
        public void complete() {
            System.out.println("TestSseEmitter完成");
            super.complete();
        }
        
        @Override
        public void completeWithError(Throwable ex) {
            System.err.println("TestSseEmitter错误: " + ex.getMessage());
            ex.printStackTrace();
            super.completeWithError(ex);
        }
        
        @Override
        public void send(Object object) throws IOException {
            System.out.println("TestSseEmitter发送数据: " + object);
            super.send(object);
        }
    }
} 