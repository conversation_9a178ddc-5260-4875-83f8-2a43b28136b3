package com.alibaba.copilot.app.service.rewrite.controller;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.copilot.app.domain.base.repository.CacheRepository;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShopSiteUser;
import com.alibaba.copilot.app.domain.seocopilot.repository.SeoShopSiteUserRepository;
import com.alibaba.copilot.app.domain.seocopilot.service.AccountService;
import com.alibaba.copilot.app.infrastructure.base.switchs.SwitchConfig;
import com.alibaba.copilot.app.service.rewrite.config.RewriteAuthService;
import com.alibaba.copilot.app.service.rewrite.config.UserIdentity;
import com.alibaba.copilot.app.service.rewrite.constant.CacheKeyConstant;
import com.alibaba.copilot.app.service.rewrite.constant.RewriteErrorCode;
import com.alibaba.copilot.app.service.rewrite.constant.Text2GoFeatureType;
import com.alibaba.copilot.app.service.rewrite.constant.Text2GoPayConst;
import com.alibaba.copilot.app.service.rewrite.dto.ClaimGiftRequest;
import com.alibaba.copilot.app.service.rewrite.dto.RewriteSubscribeRequest;
import com.alibaba.copilot.app.service.rewrite.vo.ClaimGiftResponse;
import com.alibaba.copilot.app.service.rewrite.vo.PreviewSubscribeResult;
import com.alibaba.copilot.app.service.rewrite.vo.SessionData;
import com.alibaba.copilot.app.service.rewrite.vo.SubscribeResult;
import com.alibaba.copilot.app.service.subscription.constant.SubscribeSeoConst;
import com.alibaba.copilot.app.service.subscription.dto.CurrentSubscriptionPlanVO;
import com.alibaba.copilot.app.service.subscription.dto.FeatureUsageDTO;
import com.alibaba.copilot.app.service.subscription.dto.SubscribablePlanVO;
import com.alibaba.copilot.app.service.subscription.factory.SubscribablePlanConverter;
import com.alibaba.copilot.app.service.subscription_pic.converter.ModelConverter;
import com.alibaba.copilot.app.service.subscription_pic.util.ModelConvertUtils;
import com.alibaba.copilot.app.service.subscription_pic.vo.CardVO;
import com.alibaba.copilot.app.service.subscription_pic.vo.CycleFeeDetailVO;
import com.alibaba.copilot.app.service.subscription_pic.vo.SelectedPlanInfoVO;
import com.alibaba.copilot.boot.basic.result.MultiResult;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.constant.SeoFeatureType;
import com.alibaba.copilot.enabler.client.subscription.dto.*;
import com.alibaba.copilot.enabler.client.subscription.enums.UserInvitationSourceType;
import com.alibaba.copilot.enabler.client.subscription.facade.ShopifySubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.facade.SubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.facade.Text2GoSubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.request.SelectedPlanInfoQuery;
import com.alibaba.copilot.enabler.client.subscription.request.StripePortalQuery;
import com.alibaba.copilot.enabler.client.subscription.request.SubscribablePlanQuery;
import com.alibaba.copilot.enabler.client.subscription.service.UserInvitationService;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.alibaba.copilot.enabler.client.user.constants.UserDTO;
import com.alibaba.copilot.enabler.client.user.facade.UserQueryHsfApi;
import com.google.common.collect.Lists;
import com.stripe.Stripe;
import com.stripe.exception.StripeException;
import com.stripe.model.Discount;
import com.stripe.model.Plan;
import com.stripe.model.PromotionCode;
import com.stripe.model.Subscription;
import com.stripe.model.checkout.Session;
import com.stripe.net.RequestOptions;
import com.stripe.param.SubscriptionRetrieveParams;
import com.taobao.unifiedsession.core.json.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.stream.Collectors;

/**
 * 改写订阅服务控制器
 *
 * <AUTHOR>
 * @date 2025/2/19
 */
@Slf4j
@RestController
@Api(tags = "改写订阅服务")
@RequestMapping("/rewrite/subscription")
public class RewriteSubscriptionController {

    @Autowired
    private SubscriptionHsfApi subscriptionHsfApi;

    @Autowired
    private SeoShopSiteUserRepository seoShopSiteUserRepository;

    @Autowired
    private ShopifySubscriptionHsfApi shopifySubscriptionHsfApi;

    @Resource
    private Text2GoSubscriptionHsfApi text2GoSubscriptionHsfApi;

    @Resource
    private AccountService accountService;
    @Resource
    private RewriteAuthService authService;
    @Resource
    private CacheRepository cacheRepository;
    @Resource
    private UserQueryHsfApi userQueryHsfApi;

    @Resource
    private UserInvitationService userInvitationService;

    @ApiOperation("可订阅计划列表")
    @GetMapping("/getSubscribablePlanList")
    public MultiResult<SubscribablePlanVO> getSubscribablePlanList(
        @ApiParam(value = "折扣码") @RequestParam(required = false) String shareCode) {
        try {
            // 构建查询参数构造器
            SubscribablePlanQuery.SubscribablePlanQueryBuilder queryBuilder = SubscribablePlanQuery.builder()
                .appCode(AppEnum.TEXT2GO.getCode())
                .shareCode(shareCode);

            // 获取用户身份(如果有)
            UserIdentity userIdentity = authService.getCurrentUserIdentity();
            if (userIdentity != null) {
                SeoShopSiteUser seoShopSiteUser = seoShopSiteUserRepository.getByOuterUserId(userIdentity.getUserId());
                Long transferredUserId = accountService.getUserIdTransferred(seoShopSiteUser.getId());
                if (transferredUserId != null) {
                    queryBuilder.userId(transferredUserId);
                }
            }

            // 构建最终查询参数
            SubscribablePlanQuery subscribablePlanQuery = queryBuilder.build();

            // 获取订阅计划列表
            List<SubscribablePlanDTO> subscribablePlanList = subscriptionHsfApi.getSubscribablePlanList(
                subscribablePlanQuery).getData();

            if (subscribablePlanList == null) {
                return MultiResult.buildSuccess(Collections.emptyList());
            }

            // 转换并返回结果
            List<SubscribablePlanVO> subscribablePlanVoList = SubscribablePlanConverter.INSTANCE.convertA2B(
                subscribablePlanList);
            return MultiResult.buildSuccess(subscribablePlanVoList);

        } catch (Exception e) {
            log.error("Failed to get subscribable plan list", e);
            return MultiResult.buildFailure(RewriteErrorCode.SYSTEM_ERROR.getCode(),
                RewriteErrorCode.SYSTEM_ERROR.getMessage());
        }
    }


    @ApiOperation("发起订阅")
    @PostMapping("/subscribe")
    public SingleResult<SubscribeResult> subscribe(HttpServletRequest servletRequest, @RequestBody RewriteSubscribeRequest request) {
        log.info("RewriteSubscriptionController_subscribe {} ", JSON.toJSONString(new Object[]{request}));
        UserIdentity userIdentity = authService.getCurrentUserIdentity();
        if (userIdentity == null) {
            return SingleResult.buildSuccess(null);
        }
        // 获取用户信息
        SeoShopSiteUser seoShopSiteUser = seoShopSiteUserRepository.getByOuterUserId(userIdentity.getUserId());
        if (seoShopSiteUser == null) {
            return SingleResult.buildSuccess(null);
        }
        SubscribePlanDTO subscribePlanDTO = new SubscribePlanDTO();
        subscribePlanDTO.setAppCode(AppEnum.TEXT2GO.getCode());
        subscribePlanDTO.setRedirectUrl(request.getRedirectUrl());

        Long transferredUserId = accountService.getUserIdTransferred(seoShopSiteUser.getId());
        subscribePlanDTO.setUserId(transferredUserId);
        subscribePlanDTO.setEmail(seoShopSiteUser.getEmail());
        subscribePlanDTO.setPlanId(Long.parseLong(request.getPlanId()));
        if (StringUtils.isNotBlank(request.getShareCodeToken())) {
            Object value = cacheRepository.get(CacheKeyConstant.getShareCodeKey(request.getShareCodeToken(), seoShopSiteUser.getOuterUserId()));
            if (value == null) {
                return SingleResult.buildFailure("Your discount has expired.");
            }
            subscribePlanDTO.setShareCode(String.valueOf(value));
        }
        subscribePlanDTO.setDiscountCode(request.getDiscountCode());
        subscribePlanDTO.setStripeUiMode(request.getStripeUiMode());
        String clientIP = ServletUtil.getClientIP(servletRequest);
        subscribePlanDTO.setClientIp(clientIP);
        SingleResult<SubscribePlanResultDTO> subscribePlanResultDTOSingleResult = subscriptionHsfApi.subscribePlan(subscribePlanDTO);
        if(!Boolean.TRUE.equals(subscribePlanResultDTOSingleResult.getSuccess())) {
            return SingleResult.buildFailure(subscribePlanResultDTOSingleResult.getMessage());
        }
        SubscribePlanResultDTO data = subscribePlanResultDTOSingleResult.getData();
        String subscriptionUrl = data.getSubscriptionUrl();
        SubscribeResult subscribeResult = new SubscribeResult();
        subscribeResult.setHostedUrl(subscriptionUrl);
        return SingleResult.buildSuccess(subscribeResult);
    }

    @ApiOperation("预览订阅")
    @PostMapping("/previewSubscribe")
    public SingleResult<PreviewSubscribeResult> previewSubscribe(@RequestBody RewriteSubscribeRequest request) {
        UserIdentity userIdentity = authService.getCurrentUserIdentity();
        if (userIdentity == null) {
            return SingleResult.buildSuccess(null);
        }
        // 获取用户信息
        SeoShopSiteUser seoShopSiteUser = seoShopSiteUserRepository.getByOuterUserId(userIdentity.getUserId());
        if (seoShopSiteUser == null) {
            return SingleResult.buildSuccess(null);
        }
        SubscribePlanDTO subscribePlanDTO = new SubscribePlanDTO();
        subscribePlanDTO.setAppCode(AppEnum.TEXT2GO.getCode());
        subscribePlanDTO.setRedirectUrl(request.getRedirectUrl());

        Long transferredUserId = accountService.getUserIdTransferred(seoShopSiteUser.getId());
        subscribePlanDTO.setUserId(transferredUserId);
        subscribePlanDTO.setEmail(seoShopSiteUser.getEmail());
        subscribePlanDTO.setPlanId(Long.parseLong(request.getPlanId()));
        SingleResult<SubscriptionPreviewResultDTO> result = subscriptionHsfApi.previewSubscribe(subscribePlanDTO);
        if (!result.isSuccess()) {
            return SingleResult.buildFailure(result.getMessage());
        }

        PreviewSubscribeResult previewResult = new PreviewSubscribeResult();
        SubscriptionPreviewResultDTO previewDTO = result.getData();
        String currentName = previewDTO.getCurPlanName();
        String currentInterval = previewDTO.getCurrentInterval();

        int planNameCompareResult = comparePlanName(currentName, previewDTO.getNewPlanName());
        String title;
        String description = "";
        if (planNameCompareResult > 0) {
            // 现在的更大，是downgrade
            if (Objects.equals(currentInterval, previewDTO.getNewInterval())) {
                title = "Confirm Downgrade";
            } else {
                title = "Confirm Downgrade & Billing Change";
            }
            description = "You are downgrading from "
                + currentName + " " + findDerivForInterval(previewDTO.getCurrentInterval())
                + " to "
                + previewDTO.getNewPlanName() + " " + findDerivForInterval(previewDTO.getNewInterval());
        } else if (planNameCompareResult < 0) {
            if (Objects.equals(currentInterval, previewDTO.getNewInterval())) {
                title = "Confirm Upgrade";
                description = "You are upgrading from "
                    + currentName + " " + findDerivForInterval(previewDTO.getCurrentInterval())
                    + " to "
                    + previewDTO.getNewPlanName() + " " + findDerivForInterval(previewDTO.getNewInterval());
            } else {
                title = "Confirm Upgrade & Billing Change";
                description = "You are upgrading from "
                    + currentName + " " + findDerivForInterval(previewDTO.getCurrentInterval())
                    + " to "
                    + previewDTO.getNewPlanName() + " " + findDerivForInterval(previewDTO.getNewInterval());
            }
        } else {
            title = "Confirm Plan Change";
            description = "You are switching from " + findDerivForInterval(previewDTO.getCurrentInterval()) +
                " to " + findDerivForInterval(previewDTO.getNewInterval()) + " billing " + " for " + currentName;
        }
        previewResult.setTitle(title);
        previewResult.setDescription(description);

        String additionalInfo;
        String currentCycleEnd = timestampConvert(previewDTO.getCurrentPeriodEnd());
        String nextBillingDate = currentCycleEnd;

        if ("now".equals(previewDTO.getPayType())) {
            additionalInfo = String.format("Your %s %s subscription will be effective immediately.",
                previewDTO.getNewPlanName(), findDerivForInterval(previewDTO.getNewInterval()));
            if (planNameCompareResult > 0) {
                previewResult.setAdditionalInfoNote("Your available word count will be reduced.");
            }
            if (!Objects.equals(previewDTO.getCurrentInterval(), previewDTO.getNewInterval())) {
                long timestamp = ZonedDateTime.now(ZoneId.systemDefault())
                    .plusYears(1)
                    .toInstant()
                    .getEpochSecond();
                nextBillingDate = timestampConvert(timestamp);
            }
            previewResult.setConfirmMessage("I agree to process this subscription change immediately");

        } else {
            additionalInfo = "Change takes effect at the end of current billing period.";
            if (planNameCompareResult > 0) {
                previewResult.setAdditionalInfoNote("Your available word count will be reduced.");
            }
            previewResult.setConfirmMessage("I agree to schedule this subscription change");
        }
        previewResult.setDetailLines(generateDetailLines(currentName, previewDTO.getCurrentInterval()
            , previewDTO.getNewInterval()
            , currentCycleEnd, nextBillingDate
            , culPriceFromCent(previewDTO.getImmediateCharge())
            , culPriceFromCent(previewDTO.getNextAmount())
            , culPriceFromCent(previewDTO.getCurrentAmount()), planNameCompareResult

        ));
        previewResult.setAdditionalInfo(additionalInfo);
        return SingleResult.buildSuccess(previewResult);

    }

    public static List<PreviewSubscribeResult.DetailLine> generateDetailLines(String currentPlan,
        String currentInterval,
        String newInterval,
        String currentCycleEndDate, String nextBillingDate,
        String proratedDifference, String nextBillingAmount,
        String currentPrice, int planNameCompare) {
        List<PreviewSubscribeResult.DetailLine> detailLineList = new ArrayList<>();
        String currentBillingCycle = findDerivForInterval(currentInterval);
        String targetBillingCycle = findDerivForInterval(newInterval);

        // 添加当前计费周期信息
        detailLineList.add(new PreviewSubscribeResult.DetailLine(
            "Current billing cycle",
            String.format("%s ($%s/%s)", currentPlan, currentPrice, currentInterval)));

        // 格式化各个 DetailLine
        PreviewSubscribeResult.DetailLine immediateCharge = new PreviewSubscribeResult.DetailLine(
            "Immediate charge",
            String.format("$%s/%s (prorated difference)", proratedDifference, newInterval));
        PreviewSubscribeResult.DetailLine nextBillingCycle = new PreviewSubscribeResult.DetailLine(
            "Next billing cycle", nextBillingDate);
        PreviewSubscribeResult.DetailLine nextBillingAmountLine = new PreviewSubscribeResult.DetailLine(
            "Next billing amount", String.format("$%s/%s", nextBillingAmount, newInterval));
        PreviewSubscribeResult.DetailLine currentCycleEnd = new PreviewSubscribeResult.DetailLine(
            "Current cycle ends", currentCycleEndDate);

        // 根据计划比较和计费周期变更进行分支处理
        if (planNameCompare == 0) { // 同级计划
            if ("monthly".equals(currentBillingCycle) && "annual".equals(targetBillingCycle)) {
                // 月付切换到年付(同级计划)
                Collections.addAll(detailLineList, immediateCharge, nextBillingCycle, nextBillingAmountLine);
            } else if ("annual".equals(currentBillingCycle) && "monthly".equals(targetBillingCycle)) {
                // 年付切换到月付(同级计划)
                Collections.addAll(detailLineList, nextBillingCycle, nextBillingAmountLine);
            }
        } else if (planNameCompare < 0) { // 升级计划
            if ("monthly".equals(currentBillingCycle) && "annual".equals(targetBillingCycle)) {
                // 月付切换到年付(升级计划)
                Collections.addAll(detailLineList, immediateCharge, nextBillingCycle, nextBillingAmountLine);
            } else if ("annual".equals(currentBillingCycle) && "monthly".equals(targetBillingCycle)) {
                // 年付切换到月付(升级计划)
                Collections.addAll(detailLineList, currentCycleEnd, nextBillingCycle, nextBillingAmountLine);
            } else {
                Collections.addAll(detailLineList, immediateCharge, nextBillingCycle, nextBillingAmountLine);
            }
        } else { // planNameCompare > 0 降级计划
            if ("monthly".equals(currentBillingCycle) && "annual".equals(targetBillingCycle)) {
                // 月付切换到年付(降级计划)
                PreviewSubscribeResult.DetailLine nextCompleteBillingCycle = new PreviewSubscribeResult.DetailLine(
                    "Next complete billing cycle", nextBillingDate);
                Collections.addAll(detailLineList, immediateCharge, nextCompleteBillingCycle, nextBillingAmountLine);
            } else if ("annual".equals(currentBillingCycle) && "monthly".equals(targetBillingCycle)) {
                // 年付切换到月付(降级计划)
                Collections.addAll(detailLineList, currentCycleEnd, nextBillingCycle, nextBillingAmountLine);
            } else {
                Collections.addAll(detailLineList, currentCycleEnd, nextBillingCycle, nextBillingAmountLine);
            }
        }

        return detailLineList;
    }

    private static String timestampConvert(long timestamp) {
        Instant instant = Instant.ofEpochSecond(timestamp);
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMMM dd, yyyy", Locale.ENGLISH);
        return dateTime.format(formatter);
    }

    private static int comparePlanName(String current, String newPlan) {
        List<String> planList = Lists.newArrayList("Basic Plan", "Pro Plan", "Ultimate Plan");
        return Integer.compare(planList.indexOf(current), planList.indexOf(newPlan));
    }

    private static String culPriceFromCent(Long amount) {
        if (amount == null) {
            return "";
        }
        return BigDecimal.valueOf(amount).divide(new BigDecimal("100"), 2, RoundingMode.HALF_DOWN).toString();
    }

    private static String findDerivForInterval(String original) {
        if ("year".equals(original)) {
            return "annual";
        }
        if ("month".equals(original)) {
            return "monthly";
        }
        return original;
    }

    @ApiOperation("获取当前用户订阅计划及使用情况")
    @GetMapping("/getCurrentSubscribablePlan")
    public SingleResult<CurrentSubscriptionPlanVO> getCurrentSubscribablePlan() {
        String traceId = UUID.randomUUID().toString();
        log.info("[{}] Start getCurrentSubscribablePlan", traceId);
        try {
            // 获取用户身份
            UserIdentity userIdentity = authService.getCurrentUserIdentity();
            log.info("[{}] Got userIdentity: {}", traceId, userIdentity);
            if (userIdentity == null) {
                log.warn("[{}] UserIdentity is null, returning null result", traceId);
                return SingleResult.buildSuccess(null);
            }

            // 获取用户信息
            SeoShopSiteUser seoShopSiteUser = seoShopSiteUserRepository.getByOuterUserId(userIdentity.getUserId());
            log.info("[{}] Got seoShopSiteUser: {}", traceId, seoShopSiteUser);
            if (seoShopSiteUser == null) {
                log.warn("[{}] SeoShopSiteUser is null for userId: {}, returning null result", traceId,
                    userIdentity.getUserId());
                return SingleResult.buildSuccess(null);
            }

            // 提前获取transferredUserId，避免重复调用
            Long transferredUserId = accountService.getUserIdTransferred(seoShopSiteUser.getId());
            log.info("[{}] Got transferredUserId: {} for seoShopSiteUser.getId(): {}", traceId, transferredUserId,
                seoShopSiteUser.getId());

            // 构建订阅计划查询参数
            ShopifySubscribedPlanQueryDTO queryDTO = new ShopifySubscribedPlanQueryDTO();
            queryDTO.setUserId(transferredUserId);
            queryDTO.setAppCode(AppEnum.TEXT2GO.getCode());
            log.info("[{}] Built ShopifySubscribedPlanQueryDTO: {}", traceId, queryDTO);

            // 获取订阅计划信息
            SingleResult<ShopifySubscribedPlanResultDTO> subscribedPlanResult
                = shopifySubscriptionHsfApi.getSubscribedPlan(queryDTO);
            log.info("[{}] Got subscribedPlanResult: {}", traceId, subscribedPlanResult);
            if (!subscribedPlanResult.isSuccess() || subscribedPlanResult.getData() == null) {
                log.warn("[{}] SubscribedPlanResult is not successful or data is null: {}", traceId,
                    subscribedPlanResult);
                return SingleResult.buildSuccess(null);
            }

            // 转换为VO对象
            ShopifySubscribedPlanResultDTO planResult = subscribedPlanResult.getData();
            log.info("[{}] Got planResult: {}", traceId, planResult);
            CurrentSubscriptionPlanVO currentPlan = new CurrentSubscriptionPlanVO();

            // 设置基本信息
            currentPlan.setPlanId(String.valueOf(planResult.getPlanId()));
            currentPlan.setPlanName(planResult.getPlanName());
            currentPlan.setPlanDescription("Basic features for trying out Text2Go");
            boolean isActive = isActiveSubscription(planResult.getPerformStartTime(), planResult.getPerformEndTime());
            currentPlan.setSubscriptionStatus(isActive ? "ACTIVE" : "EXPIRED");
            currentPlan.setSubscriptionStartTime(planResult.getPerformStartTime());
            currentPlan.setSubscriptionEndTime(planResult.getPerformEndTime());
            currentPlan.setIsFreePlan(planResult.isFree());
            currentPlan.setPriceUsd(planResult.getPrice() != null ? planResult.getPrice().doubleValue() : 0.0);
            currentPlan.setBillingCycle(null);
            currentPlan.setAutoRenew(planResult.getIsAutoRenew());
            log.info("[{}] Set basic plan information: {}", traceId, currentPlan);

            // 查询用户试用期信息
            try {
                log.info("[{}] Starting to query trial information for userId: {}", traceId, transferredUserId);
                SingleResult<TrialDurationDTO> trialResult = text2GoSubscriptionHsfApi.subscriptionIsTrial(transferredUserId);
                if (trialResult != null && trialResult.isSuccess() && trialResult.getData() != null) {
                    TrialDurationDTO trialData = trialResult.getData();
                    log.info("[{}] Got trial information: {}", traceId, JSON.toJSONString(trialData));
                    currentPlan.setIsTrial(trialData.getIsTrial());
                    currentPlan.setRemainTrialDay(trialData.getRemainTrialDay());
                } else {
                    log.warn("[{}] Failed to get trial information or no trial found: {}", traceId,
                        trialResult != null ? JSON.toJSONString(trialResult) : "null");
                    currentPlan.setIsTrial(false);
                    currentPlan.setRemainTrialDay(0L);
                }
            } catch (Exception e) {
                log.error("[{}] Error getting trial information: {}", traceId, e.getMessage(), e);
                currentPlan.setIsTrial(false);
                currentPlan.setRemainTrialDay(0L);
            }

            // 并行查询三个特性的使用情况
            String[] featureTypes = {
                "TOTAL_WORDS_PER_MONTH",
                "WORDS_PER_REQUEST",
                "AI_DETECTION_LIMIT"
            };
            log.info("[{}] Starting to query features: {}", traceId, Arrays.toString(featureTypes));

            List<CompletableFuture<FeatureUsageDTO>> futures = Arrays.stream(featureTypes)
                .map(featureType -> CompletableFuture.supplyAsync(() -> {
                    log.info("[{}] Querying feature: {} for userId: {}", traceId, featureType, transferredUserId);
                    ShopifyFeatureQuery featureQuery = new ShopifyFeatureQuery();
                    featureQuery.setAppCode(AppEnum.TEXT2GO.getCode());
                    featureQuery.setId(transferredUserId);
                    featureQuery.setFeatureType(featureType);
                    log.info("[{}] Built ShopifyFeatureQuery for {}: {}", traceId, featureType, featureQuery);

                    try {
                        log.info(
                            "[{}] Before calling text2GoSubscriptionHsfApi.getFeatureAll for feature: {}, query: {}",
                            traceId, featureType, JSON.toJSONString(featureQuery));
                        if (text2GoSubscriptionHsfApi == null) {
                            log.error("[{}] text2GoSubscriptionHsfApi is null!", traceId);
                            return null;
                        }
                        SingleResult<ShopifyFeatureAllDTO> featureResult = text2GoSubscriptionHsfApi.getFeatureAll(
                            featureQuery);
                        log.info(
                            "[{}] After calling text2GoSubscriptionHsfApi.getFeatureAll for feature: {}, result: {}",
                            traceId, featureType, JSON.toJSONString(featureResult));

                        if (featureResult != null && featureResult.isSuccess() && featureResult.getData() != null) {
                            ShopifyFeatureAllDTO data = featureResult.getData();
                            log.info("[{}] Feature data for {}: {}", traceId, featureType, JSON.toJSONString(data));
                            FeatureUsageDTO usage = new FeatureUsageDTO();
                            usage.setFeatureType(featureType);
                            usage.setHasAuthorize(data.isHasAuthorize());
                            usage.setDepletion(data.isDepletion());
                            usage.setRemainQuota(data.getRemainQuota());
                            usage.setAllOverQuota(data.getAllOverQuota());
                            usage.setMaxQuota(data.getMaxQuota());
                            log.info("[{}] Created FeatureUsageDTO for {}: {}", traceId, featureType,
                                JSON.toJSONString(usage));
                            return usage;
                        } else {
                            log.warn("[{}] Invalid feature result for {}: {}", traceId, featureType,
                                JSON.toJSONString(featureResult));
                        }
                    } catch (Exception e) {
                        log.error("[{}] Error getting feature data for {}: {}, stack trace: {}", traceId, featureType,
                            e.getMessage(), Arrays.toString(e.getStackTrace()));
                    }
                    return null;
                }))
                .collect(Collectors.toList());

            log.info("[{}] Created {} feature futures", traceId, futures.size());

            // 等待所有特性查询完成
            List<FeatureUsageDTO> features = futures.stream()
                .map(future -> {
                    try {
                        FeatureUsageDTO result = future.get(5, TimeUnit.SECONDS);
                        log.info("[{}] Got feature result: {}", traceId, result);
                        return result;
                    } catch (TimeoutException e) {
                        log.error("[{}] Feature query timed out after 5 seconds", traceId, e);
                        return null;
                    } catch (Exception e) {
                        log.error("[{}] Failed to get feature data: {}", traceId, e.getMessage(), e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

            log.info("[{}] Collected {} valid features", traceId, features.size());

            // 查询用户邀请奖励额度
            try {
                log.info("[{}] Starting to query user feature rewards for userId: {}", traceId, transferredUserId);
                List<String> rewardFeatureTypes = Arrays.asList(featureTypes);
                SingleResult<UserFeatureRewardsDTO> rewardsResult = text2GoSubscriptionHsfApi.getUserFeatureRewards(
                    transferredUserId, rewardFeatureTypes);

                // 将奖励额度信息整合到相应的特性中
                if (rewardsResult != null && rewardsResult.isSuccess() && rewardsResult.getData() != null) {
                    UserFeatureRewardsDTO rewards = rewardsResult.getData();
                    log.info("[{}] Got user feature rewards: {}", traceId, JSON.toJSONString(rewards));

                    // 创建特性类型到奖励信息的映射
                    if (rewards.getRewardDetails() != null && !rewards.getRewardDetails().isEmpty()) {
                        Map<String, UserFeatureRewardsDTO.FeatureRewardDetail> rewardMap = rewards.getRewardDetails()
                            .stream()
                            .collect(Collectors.toMap(
                                UserFeatureRewardsDTO.FeatureRewardDetail::getFeatureType,
                                reward -> reward,
                                (r1, r2) -> r1
                            ));

                        // 将奖励信息添加到对应的特性中
                        for (FeatureUsageDTO feature : features) {
                            UserFeatureRewardsDTO.FeatureRewardDetail reward = rewardMap.get(feature.getFeatureType());
                            if (reward != null) {
                                // 将奖励额度合并到现有字段中
                                Long originalRemainQuota = feature.getRemainQuota() != null ? feature.getRemainQuota()
                                    : 0L;
                                Long rewardRemainingQuota = reward.getRemainingQuota() != null
                                    ? reward.getRemainingQuota() : 0L;

                                // 检查是否为无限额度(-1)，如果是则保持无限状态
                                if (originalRemainQuota != -1) {
                                    // 仅当不是无限额度时才合并剩余额度（原剩余额度 + 奖励剩余额度）
                                    feature.setRemainQuota(originalRemainQuota + rewardRemainingQuota);
                                } else {
                                    // 维持无限额度状态
                                    log.info("[{}] Feature {} has unlimited remainQuota (-1), keeping it unchanged",
                                        traceId, feature.getFeatureType());
                                }

                                // 同样检查总额度是否为无限
                                Long originalAllOverQuota = feature.getAllOverQuota() != null
                                    ? feature.getAllOverQuota() : 0L;
                                Long rewardTotalQuota = reward.getTotalQuota() != null ? reward.getTotalQuota() : 0L;

                                if (originalAllOverQuota != -1) {
                                    // 仅当不是无限额度时才合并总额度
                                    feature.setAllOverQuota(originalAllOverQuota + rewardTotalQuota);
                                } else {
                                    // 维持无限额度状态
                                    log.info("[{}] Feature {} has unlimited allOverQuota (-1), keeping it unchanged",
                                        traceId, feature.getFeatureType());
                                }

                                // 保留奖励额度的过期信息和属性
                                feature.setRewardExpiryTime(reward.getExpiryTime());
                                feature.setIsRewardPermanent(reward.getIsPermanent());
                                feature.setRewardAttributes(reward.getAttributes());

                                log.info(
                                    "[{}] Processed reward info for feature {}: originalRemain={}, rewardRemain={}, "
                                        + "finalRemain={}, "
                                        +
                                        "originalTotal={}, rewardTotal={}, finalTotal={}",
                                    traceId, feature.getFeatureType(),
                                    originalRemainQuota, rewardRemainingQuota, feature.getRemainQuota(),
                                    originalAllOverQuota, rewardTotalQuota, feature.getAllOverQuota());
                            } else {
                                log.info("[{}] No reward found for feature type: {}", traceId,
                                    feature.getFeatureType());
                            }
                        }
                    } else {
                        log.info("[{}] No reward details found in the response", traceId);
                    }
                } else {
                    log.warn("[{}] Failed to get user feature rewards or no rewards found: {}", traceId,
                        rewardsResult != null ? JSON.toJSONString(rewardsResult) : "null");
                }
            } catch (Exception e) {
                log.error("[{}] Error getting user feature rewards: {}", traceId, e.getMessage(), e);
            }

            currentPlan.setFeatures(features);
            log.info("[{}] Returning final result with {} features", traceId, features.size());
            return SingleResult.buildSuccess(currentPlan);

        } catch (Exception e) {
            log.error("[{}] Failed to get current subscription plan: {}", traceId, e.getMessage(), e);
            return SingleResult.buildFailure(RewriteErrorCode.SYSTEM_ERROR.getCode(),
                RewriteErrorCode.SYSTEM_ERROR.getMessage());
        } finally {
            log.info("[{}] End getCurrentSubscribablePlan", traceId);
        }
    }

    /**
     * 判断订阅是否处于活跃状态
     */
    private boolean isActiveSubscription(Date startTime, Date endTime) {
        if (startTime == null || endTime == null) {
            return false;
        }
        Date now = new Date();
        return now.after(startTime) && now.before(endTime);
    }

    /**
     * 将Text2Go特性类型转换为SEO特性类型
     */
    private SeoFeatureType convertToSeoFeatureType(Text2GoFeatureType featureType) {
        return SeoFeatureType.valueOf(featureType.name());
    }

    public static void main(String[] args) throws StripeException {
        Stripe.apiKey = SwitchConfig.stripeApiKey;
        Subscription subscription = Subscription.retrieve("sub_1R2OhvBUuFYymNc8CK62lact",
            SubscriptionRetrieveParams.builder()
                .addAllExpand(Lists.newArrayList("discounts", "discounts.coupon"))
                .build()
            , RequestOptions.builder().build());
        // 计算折扣价格
        Plan plan = subscription.getItems().getData().get(0).getPlan();
        BigDecimal nextAmountBigDecimal = BigDecimal.valueOf(plan.getAmount());
        for (Discount discount : CollectionUtils.emptyIfNull(subscription.getDiscountObjects())) {
            BigDecimal percentOff = discount.getCoupon().getPercentOff();
            if (percentOff != null) {
                BigDecimal discountAmount = nextAmountBigDecimal.multiply(percentOff).divide(BigDecimal.valueOf(100),
                    RoundingMode.HALF_DOWN);
                nextAmountBigDecimal = nextAmountBigDecimal.subtract(discountAmount);
            }
            Long amountOff = discount.getCoupon().getAmountOff();
            if (amountOff != null) {
                nextAmountBigDecimal = nextAmountBigDecimal.subtract(BigDecimal.valueOf(amountOff));
            }

        }
        System.out.println(nextAmountBigDecimal);
    }

    @GetMapping("session")
    public SingleResult<SessionData> sessionData(@RequestParam("sessionId") String sessionId) {
        Stripe.apiKey = SwitchConfig.stripeApiKey;
        try {
            // 替换为你的 Checkout Session ID

            // 1. 获取 Checkout Session
            Session session = Session.retrieve(sessionId);

            // 3. 获取关联的订阅 ID
            String subscriptionId = session.getSubscription();

            // 4. 使用订阅 ID 获取订阅详情
            Subscription subscription = Subscription.retrieve(subscriptionId, SubscriptionRetrieveParams.builder()
                    .addAllExpand(Lists.newArrayList("discounts", "discounts.coupon"))
                    .build()
                , RequestOptions.builder().build());

            // 5. 获取计划详情
            Plan plan = subscription.getItems().getData().get(0).getPlan();

            // 6. 提取数据并填充到 SubscriptionData 对象
            String planName = plan.getNickname(); // 计划名称，如 "Pro Plan"
            String billingPeriod = plan.getInterval().equals("month") ? "mo" : "yr"; // 计费周期
            Long nextBillingTimestamp = subscription.getCurrentPeriodEnd(); // 下一个计费时间戳

            // 格式化日期为 YYYY-MM-DD
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String nextBillingDate = sdf.format(new Date(nextBillingTimestamp * 1000)); // 转换为毫秒

            // 7. 创建 SubscriptionData 对象
            SessionData subscriptionData = new SessionData();
            subscriptionData.setNextBillingDate(nextBillingDate);
            subscriptionData.setPlanName(planName);
            subscriptionData.setBillingPeriod(billingPeriod);
            subscriptionData.setCreated(session.getCreated());

            // 计算折扣价格
            BigDecimal nextAmountBigDecimal = BigDecimal.valueOf(plan.getAmount());
            for (Discount discount : CollectionUtils.emptyIfNull(subscription.getDiscountObjects())) {
                BigDecimal percentOff = discount.getCoupon().getPercentOff();
                if (percentOff != null) {
                    BigDecimal discountAmount = nextAmountBigDecimal.multiply(percentOff).divide(
                        BigDecimal.valueOf(100), RoundingMode.HALF_DOWN);
                    nextAmountBigDecimal = nextAmountBigDecimal.subtract(discountAmount);
                }
                Long amountOff = discount.getCoupon().getAmountOff();
                if (amountOff != null) {
                    nextAmountBigDecimal = nextAmountBigDecimal.subtract(BigDecimal.valueOf(amountOff));
                }

            }
            subscriptionData.setPrice(
                nextAmountBigDecimal.divide(new BigDecimal("100.0"), 2, RoundingMode.DOWN).toString());
            // 8. 输出结果(或使用对象进行其他操作)
            return SingleResult.buildSuccess(subscriptionData);

        } catch (Exception e) {
            log.error("Error retrieving session", e);
            return SingleResult.buildFailure(e.getMessage());
        }

    }

    @GetMapping("stripePortalSession")
    public SingleResult<String> stripePortalSession(@RequestParam(required = false) String returnUrl) {
        UserIdentity userIdentity = authService.getCurrentUserIdentity();
        SeoShopSiteUser seoShopSiteUser = seoShopSiteUserRepository.getByOuterUserId(userIdentity.getUserId());
        if (seoShopSiteUser == null) {
            return SingleResult.buildFailure(RewriteErrorCode.USER_NOT_FOUND.getCode(),
                RewriteErrorCode.USER_NOT_FOUND.getMessage());
        }
        Long transferredUserId = accountService.getUserIdTransferred(seoShopSiteUser.getId());
        StripePortalQuery stripePortalQuery = new StripePortalQuery();
        stripePortalQuery.setAppCode(AppEnum.TEXT2GO.getCode());
        stripePortalQuery.setUserId(transferredUserId);
        stripePortalQuery.setReturnUrl(returnUrl);

        SingleResult<StripePortalResult> portalSession = subscriptionHsfApi.createPortalSession(stripePortalQuery);
        return SingleResult.buildSuccess(portalSession.getData().getUrl());
    }

    @GetMapping("/getSelectedPlanInfo")
    public SingleResult<SelectedPlanInfoVO> getSelectedPlanInfo(
        @ApiParam(value = "计划ID") @RequestParam(required = false) Long planId,
        @ApiParam(value = "折扣码") @RequestParam(required = false) String discountCode) {

        UserIdentity userIdentity = authService.getCurrentUserIdentity();
        if (userIdentity == null) {
            log.warn("getSelectedPlanInfo: userIdentity is null");
            return SingleResult.buildFailure(RewriteErrorCode.USER_NOT_FOUND.getCode(),
                RewriteErrorCode.USER_NOT_FOUND.getMessage());
        }

        SeoShopSiteUser seoShopSiteUser = seoShopSiteUserRepository.getByOuterUserId(userIdentity.getUserId());
        if (seoShopSiteUser == null) {
            log.warn("getSelectedPlanInfo: seoShopSiteUser is null for userId: {}", userIdentity.getUserId());
            return SingleResult.buildFailure(RewriteErrorCode.USER_NOT_FOUND.getCode(),
                RewriteErrorCode.USER_NOT_FOUND.getMessage());
        }

        Long currentUserId = accountService.getUserIdTransferred(seoShopSiteUser.getId());
        log.info("getSelectedPlanInfo, currentUserId={}, discountCode={}, planId={}", currentUserId, discountCode, planId);

        if (planId == null || planId.equals(0L)) {
            log.warn("getSelectedPlanInfo: planId is empty");
            return SingleResult.buildFailure("planId is empty");
        }

        try {
            // 获取计划详情
            Long registerTime = getRegisterTime(currentUserId);
            SelectedPlanInfoQuery selectedPlanInfoQuery = SelectedPlanInfoQuery.builder()
                .userId(currentUserId)
                .appCode(AppEnum.TEXT2GO.getCode())
                .planId(planId)
                .userRegistrationTime(registerTime)
                .build();

            log.info("getSelectedPlanInfo, calling subscriptionHsfApi with query: {}", selectedPlanInfoQuery);
            SingleResult<SelectedPlanInfoDTO> selectedPlanInfoResult = subscriptionHsfApi.getSelectedPlanInfo(
                selectedPlanInfoQuery);

            if (!selectedPlanInfoResult.isSuccess()) {
                log.warn("getSelectedPlanInfo: API call failed with message: {}", selectedPlanInfoResult.getMessage());
                return SingleResult.buildFailure(selectedPlanInfoResult.getMessage());
            }

            // 转换结果并应用折扣
            return ModelConvertUtils.convertSingResult(selectedPlanInfoResult, dto -> {
                List<CycleFeeDetailVO> cycleFeeDetails = ModelConvertUtils.copyListByReflect(
                    dto.getCycleFeeDetails(), CycleFeeDetailVO::new);
                List<CardVO> cards = ModelConvertUtils.copyListByReflect(dto.getCardList(),
                    card -> new CardVO().setCardId(card.getEncryptedCardToken())
                );

                SelectedPlanInfoVO result = ModelConverter.convert(dto)
                    .setCycleFeeDetails(cycleFeeDetails)
                    .setCards(cards)
                    .setAppLogo(Text2GoPayConst.APP_LOGO);

                // 如果有折扣码，应用折扣
                if (StringUtils.isNotBlank(discountCode)) {
                    try {
                        Stripe.apiKey = SwitchConfig.stripeApiKey;
                        PromotionCode promotionCode = PromotionCode.retrieve(discountCode);

                        if (promotionCode.getActive() && promotionCode.getCoupon() != null) {
                            // 获取原价
                            BigDecimal originalPrice = null;
                            if (cycleFeeDetails != null && !cycleFeeDetails.isEmpty() &&
                                cycleFeeDetails.get(0).getCycleFee() != null) {
                                originalPrice = cycleFeeDetails.get(0).getCycleFee();
                                result.setOriginPlanPrice(originalPrice);
                            }

                            // 计算折扣价格
                            if (originalPrice != null) {
                                BigDecimal discountedPrice = originalPrice;

                                // 处理百分比折扣
                                if (promotionCode.getCoupon().getPercentOff() != null) {
                                    // 简化类型处理: 直接转换为字符串再解析为BigDecimal
                                    BigDecimal percentOff;
                                    try {
                                        // 尝试直接转为BigDecimal
                                        percentOff = new BigDecimal(promotionCode.getCoupon().getPercentOff().toString());
                                        percentOff = percentOff.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_DOWN);

                                        BigDecimal discountAmount = originalPrice.multiply(percentOff);
                                        discountedPrice = originalPrice.subtract(discountAmount);

                                        log.info("应用百分比折扣: 原价={}, 折扣={}%, 折后价={}",
                                            originalPrice, percentOff.multiply(BigDecimal.valueOf(100)), discountedPrice);
                                    } catch (Exception e) {
                                        log.warn("处理百分比折扣失败: {}", e.getMessage());
                                    }
                                }

                                // 处理固定金额折扣
                                if (promotionCode.getCoupon().getAmountOff() != null) {
                                    // 简化类型处理
                                    try {
                                        // 直接转为BigDecimal并转换单位(分→元)
                                        BigDecimal amountOff = new BigDecimal(promotionCode.getCoupon().getAmountOff().toString())
                                            .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_DOWN);

                                        discountedPrice = discountedPrice.subtract(amountOff);
                                        if (discountedPrice.compareTo(BigDecimal.ZERO) < 0) {
                                            discountedPrice = BigDecimal.ZERO;
                                        }

                                        log.info("应用固定金额折扣: 原价={}, 折扣金额={}, 折后价={}",
                                            originalPrice, amountOff, discountedPrice);
                                    } catch (Exception e) {
                                        log.warn("处理固定金额折扣失败: {}", e.getMessage());
                                    }
                                }

                                // 设置折扣价格
                                result.setDiscountPlanPrice(discountedPrice);

                                // 更新cycleFeeDetails的价格，以便UI显示折扣后的价格
                                if (cycleFeeDetails != null && !cycleFeeDetails.isEmpty()) {
                                    cycleFeeDetails.get(0).setCycleFee(discountedPrice);
                                }
                            }
                        } else {
                            log.info("Promotion code is not active or does not have a coupon: {}", discountCode);
                        }
                    } catch (StripeException e) {
                        log.warn("Failed to retrieve promotion code from Stripe: {}", e.getMessage());
                    }
                }

                // 处理所有价格字段的四舍五入
                if (result.getCurrentPayFee() != null) {
                    result.setCurrentPayFee(result.getCurrentPayFee().setScale(2, RoundingMode.DOWN));
                }
                if (result.getOriginPlanPrice() != null) {
                    result.setOriginPlanPrice(result.getOriginPlanPrice().setScale(2, RoundingMode.DOWN));
                }
                if (result.getDiscountPlanPrice() != null) {
                    result.setDiscountPlanPrice(result.getDiscountPlanPrice().setScale(2, RoundingMode.DOWN));
                }
                if (cycleFeeDetails != null && !cycleFeeDetails.isEmpty()) {
                    for (CycleFeeDetailVO detail : cycleFeeDetails) {
                        if (detail.getCycleFee() != null) {
                            detail.setCycleFee(detail.getCycleFee().setScale(2, RoundingMode.DOWN));
                        }
                    }
                }

                return result;
            });
        } catch (Exception e) {
            log.error("getSelectedPlanInfo: Exception occurred", e);
            return SingleResult.buildFailure(RewriteErrorCode.SYSTEM_ERROR.getCode(),
                RewriteErrorCode.SYSTEM_ERROR.getMessage());
        }
    }

    private Long getRegisterTime(Long currentUserId) {
        SingleResult<UserDTO> userDTOSingleResult = userQueryHsfApi.queryUserDTO(currentUserId,
            SubscribeSeoConst.APP_CODE);
        Date targetDate = Optional.ofNullable(userDTOSingleResult)
            .filter(SingleResult::isSuccess)
            .map(SingleResult::getData)
            .map(user -> {
                Date registerTime = user.getRegisterTime();
                return registerTime == null ? user.getGmtCreate() : registerTime;
            })
            .orElseGet(Date::new);
        return targetDate.getTime();
    }

    @PostMapping("/claimGift")
    public SingleResult<ClaimGiftResponse> claimGift(@RequestBody ClaimGiftRequest request) {
        if (!Lists.newArrayList(UserInvitationSourceType.DISCORD_WELCOME.name()).contains(request.getLoginReferral())) {
            return SingleResult.buildFailure("failed,  this type of benefit do not exists");
        }

        UserIdentity userIdentity = authService.getCurrentUserIdentity();
        SeoShopSiteUser seoShopSiteUser = seoShopSiteUserRepository.getByOuterUserId(userIdentity.getUserId());
        Long transferredUserId = accountService.getUserIdTransferred(seoShopSiteUser.getId());

        SourceRewardDTO sourceRewardDTO = new SourceRewardDTO();
        List<SourceRewardDTO.RewardItem> rewardItemList = new ArrayList<>();
        rewardItemList.add(new SourceRewardDTO.RewardItem(Text2GoFeatureType.AI_DETECTION_LIMIT.name(), 3));
        rewardItemList.add(new SourceRewardDTO.RewardItem(Text2GoFeatureType.TOTAL_WORDS_PER_MONTH.name(), 500));

        sourceRewardDTO.setRewardItems(rewardItemList);
        sourceRewardDTO.setUserId(transferredUserId);
        SingleResult<Boolean> booleanSingleResult = userInvitationService.processSourceReward(sourceRewardDTO, UserInvitationSourceType.DISCORD_WELCOME);
        if (booleanSingleResult == null || booleanSingleResult.getData() == null) {
            return SingleResult.buildFailure("You have already applied for this benefit.");
        }
        ClaimGiftResponse response = new ClaimGiftResponse();
        response.setType(request.getLoginReferral());
        if (Boolean.TRUE.equals(booleanSingleResult.getData())) {
            return SingleResult.buildSuccess(response);
        } else {
            return SingleResult.buildFailure("You have already applied for this benefit.");
        }
    }

}
