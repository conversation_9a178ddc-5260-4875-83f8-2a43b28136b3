package com.alibaba.copilot.app.service.rewrite.test;

import com.github.pemistahl.lingua.api.Language;
import com.github.pemistahl.lingua.api.LanguageDetector;
import com.github.pemistahl.lingua.api.LanguageDetectorBuilder;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 批量语言检测测试类
 * 测试多种语言的样本文本
 */
public class BatchLanguageDetectionTest {

    public static void main(String[] args) {
        System.out.println("初始化语言检测器...");
        LanguageDetector detector = LanguageDetectorBuilder.fromAllLanguages()
                .build();
        System.out.println("语言检测器初始化完成\n");
        
        // 准备多语言样本
        Map<String, String> samples = new LinkedHashMap<>();
        samples.put("中文", "这是一段中文文本，用于测试语言检测功能。");
        samples.put("英文", "This is an English text for testing language detection.");
        samples.put("日文", "これは言語検出をテストするための日本語のテキストです。");
        samples.put("韩文", "이것은 언어 감지를 테스트하기 위한 한국어 텍스트입니다.");
        samples.put("法文", "Ceci est un texte français pour tester la détection de langue.");
        samples.put("德文", "Dies ist ein deutscher Text zum Testen der Spracherkennung.");
        samples.put("西班牙文", "Este es un texto en español para probar la detección de idiomas.");
        samples.put("俄文", "Это русский текст для проверки определения языка.");
        samples.put("阿拉伯文", "هذا نص عربي لاختبار اكتشاف اللغة.");
        samples.put("葡萄牙文", "Este é um texto em português para testar a detecção de idioma.");
        samples.put("意大利文", "Questo è un testo italiano per testare il rilevamento della lingua.");
        samples.put("荷兰文", "Dit is een Nederlandse tekst om taaldetectie te testen.");
        samples.put("越南文", "Đây là văn bản tiếng Việt để kiểm tra tính năng phát hiện ngôn ngữ.");
        samples.put("泰文", "นี่คือข้อความภาษาไทยสำหรับการทดสอบการตรวจจับภาษา");
        
        // 批量测试
        System.out.println("开始批量测试...\n");
        System.out.printf("%-15s %-15s %-15s %-10s%n", "预期语言", "检测语言", "ISO代码", "置信度");
        System.out.println("----------------------------------------------------------");
        
        for (Map.Entry<String, String> entry : samples.entrySet()) {
            String expectedLanguage = entry.getKey();
            String text = entry.getValue();
            
            // 检测语言
            Language detectedLanguage = detector.detectLanguageOf(text);
            Map<Language, Double> confidenceValues = detector.computeLanguageConfidenceValues(text);
            Double confidence = confidenceValues.get(detectedLanguage);
            
            System.out.printf("%-15s %-15s %-15s %.4f%n", 
                    expectedLanguage, 
                    detectedLanguage.toString(), 
                    detectedLanguage.getIsoCode639_1().toString(),
                    confidence);
        }
        
        System.out.println("\n批量测试完成");
    }
}
