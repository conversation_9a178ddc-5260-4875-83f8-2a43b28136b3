package com.alibaba.copilot.app.service.rewrite.vo;

import com.alibaba.copilot.app.domain.rewrite.entity.RewriteEventLog;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class LogQueryResponse {
    /**
     * 日志列表
     */
    private List<RewriteEventLog> logs;

    /**
     * 总记录数
     */
    private Long total;

    /**
     * 当前页码
     */
    private Integer pageNum;

    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 总页数
     */
    private Integer totalPages;
} 