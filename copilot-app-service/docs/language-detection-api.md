# Language Detection API Documentation

## Overview

The Language Detection API provides functionality to automatically detect the language of input text using the Lingua library. This endpoint is part of the Text Translator Controller and follows the same authentication and usage patterns as the translation service.

## Endpoint

**POST** `/text2go/api/textTranslator/detectLanguage`

## Authentication

- **Authenticated Users**: Can use the service without restrictions
- **Anonymous Users**: Can use the service once for free, then must login to continue

## Request

### Headers
```
Content-Type: application/json
```

### Request Body
```json
{
  "text": "string"
}
```

#### Parameters

| Parameter | Type   | Required | Description                    |
|-----------|--------|----------|--------------------------------|
| text      | string | Yes      | The text to detect language for |

**Note**: The `userId` field is automatically set by the controller based on authentication status.

## Response

### Success Response
```json
{
  "success": true,
  "data": {
    "detectedLanguage": "en",
    "detectedLanguageName": "ENGLISH",
    "confidenceValues": {
      "ENGLISH": 0.9999,
      "FRENCH": 0.0001,
      "SPANISH": 0.0000
    },
    "characters": 42,
    "recordId": 12345
  },
  "message": null
}
```

#### Response Fields

| Field                | Type              | Description                                           |
|---------------------|-------------------|-------------------------------------------------------|
| detectedLanguage    | string            | ISO 639-1 language code (e.g., "en", "zh", "es")   |
| detectedLanguageName| string            | Full language name (e.g., "ENGLISH", "CHINESE")     |
| confidenceValues    | Map<String,Double>| Confidence scores for each language (sorted desc)    |
| characters          | integer           | Number of characters in the input text               |
| recordId            | long              | Database record ID for this detection operation      |

### Error Response
```json
{
  "success": false,
  "data": null,
  "message": "Error message describing the issue"
}
```

## Example Usage

### Example 1: English Text Detection
```bash
curl -X POST "http://localhost:8080/text2go/api/textTranslator/detectLanguage" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "Hello, this is a test message in English. How are you today?"
  }'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "detectedLanguage": "en",
    "detectedLanguageName": "ENGLISH",
    "confidenceValues": {
      "ENGLISH": 0.9999,
      "FRENCH": 0.0001
    },
    "characters": 58,
    "recordId": 12345
  }
}
```

### Example 2: Chinese Text Detection
```bash
curl -X POST "http://localhost:8080/text2go/api/textTranslator/detectLanguage" \
  -H "Content-Type: application/json" \
  -d '{
    "text": "你好，这是一个中文测试消息。今天天气怎么样？"
  }'
```

**Response:**
```json
{
  "success": true,
  "data": {
    "detectedLanguage": "zh",
    "detectedLanguageName": "CHINESE",
    "confidenceValues": {
      "CHINESE": 0.9998,
      "JAPANESE": 0.0002
    },
    "characters": 21,
    "recordId": 12346
  }
}
```

## Error Cases

### Empty or Null Text
```json
{
  "success": false,
  "message": "Text cannot be empty"
}
```

### Anonymous User Limit Exceeded
```json
{
  "success": false,
  "message": "User has used this feature, please login"
}
```

## Supported Languages

The API uses the Lingua library which supports detection of 75+ languages including:

- English (en)
- Chinese (zh)
- Spanish (es)
- French (fr)
- German (de)
- Japanese (ja)
- Korean (ko)
- Portuguese (pt)
- Russian (ru)
- Arabic (ar)
- And many more...

## Rate Limiting

- **Anonymous Users**: 1 free detection per session
- **Authenticated Users**: No specific rate limits (subject to general API limits)

## Notes

1. **Minimum Text Length**: While there's no strict minimum, longer texts generally provide more accurate detection results.

2. **Mixed Languages**: The API will detect the predominant language in mixed-language texts.

3. **Record Keeping**: All detection operations are logged with a unique record ID for tracking and analytics.

4. **Performance**: Detection typically completes within 100-500ms depending on text length.

5. **Confidence Scores**: The confidence values are sorted in descending order, with the highest confidence language being the detected result.

## Integration with Translation Service

This language detection endpoint is designed to work seamlessly with the existing translation service. The detected language can be used as the source language for translation requests.
