# proxy conf
include                     user.conf;

worker_rlimit_nofile        100000;

error_log                   "pipe:/opt/taobao/install/cronolog/sbin/cronolog logs/cronolog/%Y/%m/%Y-%m-%d-error_log" warn;
pid                         logs/tengine-proxy.pid;

events {
    use                     epoll;
    worker_connections      20480;
}

include dso.conf;
#include tmd_main.conf;

http {
    include                 mime.types;
    default_type            application/octet-stream;

    root                    htdocs;

    sendfile                on;
    tcp_nopush              on;

    server_tokens           off;

    keepalive_timeout       0;

    client_header_timeout   1m;
    send_timeout            1m;
    client_max_body_size    5m;
    client_body_temp_path   data/client_body;

    index                   index.html index.htm;

    log_format              proxyformat    "$remote_addr $request_time_usec $http_x_readtime [$time_local] \"$request_method http://$host$request_uri\" $status $body_bytes_sent \"$http_referer\" \"$upstream_addr\" \"$http_user_agent\" \"$cookie_unb\" \"$cookie_cookie2\" \"$eagleeye_traceid\"";

    access_log              "pipe:/opt/taobao/install/cronolog/sbin/cronolog logs/cronolog/%Y/%m/%Y-%m-%d-taobao-access_log" proxyformat;
    log_not_found           off;

    gzip                    on;
    gzip_http_version       1.0;
    gzip_comp_level         6;
    gzip_min_length         1024;
    gzip_proxied            any;
    gzip_vary               on;
    gzip_disable            msie6;
    gzip_buffers            96 8k;
    gzip_types              text/xml text/plain text/css application/javascript application/x-javascript application/rss+xml application/json;

    # taobao trans
    trans_cookie_name       _lang;
    trans_cookie_trans_value zh_CN:TB-GBK;
    trans_cookie_nottrans_value zh_CN:GBK;
    trans_ip_file           ip.dat;
    trans_code_file         sm2tr.txt;
    trans_content_type      application/xhtml+xml text/plain text/css text/xml text/javascript;
    trans_accept_language_trans zh-HK zh-TW zh-MO zh-Hant;
    trans_accept_language_notrans zh-CN zh-SG zh-Hans;

    eagleeye_traceid_var    $eagleeye_traceid;
    eagleeye_traceid_arg    tb_eagleeye_traceid;

    proxy_set_header        Host $host;
    proxy_set_header        X-Real-IP $remote_addr;
    proxy_set_header        Web-Server-Type nginx;
    proxy_set_header        WL-Proxy-Client-IP $remote_addr;
    proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header        EagleEye-TraceId $eagleeye_traceid;
    proxy_redirect          off;
    proxy_buffers           128 8k;
    proxy_temp_path         data/proxy;
    proxy_intercept_errors  on;

    include services.conf;
    # include cell_main.conf;

    # fight mhtml/utf-7 bug
    hat_content             "\r\n";
    hat_types               text/html text/css;

    # waf, fight hashdos attack
    #waf_max_post_params              1000;
    #waf_max_args                     1000;
    #waf_max_cookies                  1000;
    #waf_post_delimiter_maxlen        70;

    # detector
    #tesla           on;
    #tsl_inject_tail on;
    #tsl_med         on;
    #tsl_med_cookie  _med;
    #tsl_med_jspath  med.js;
    #user_agent_detector on;
    #include detector.conf;

    variables_hash_max_size     1024;
    variables_hash_bucket_size  64;

    server {
        listen              80;
        server_name         api.edgeshop.ai pre-api.edgeshop.ai;

        # if you want to use tmd, you must uncomment tmd main & http & loc conf
        #include tmd_loc.conf;

        # if you want to enable cell logic, you must uncomment following conf,
        # $app: the name of app, eg. 'detail'
        #
        #set                $app                 appname;
        #include            cell_server.conf;

        # detector
        #include detector_srv.conf;


        # fastcgi pattern
        #location  ~* ^(.+\.php)(.*)$ {
        #    set $fastcgi_file $1;
        #    set $fastcgi_path $2;
        #    fastcgi_pass        127.0.0.1:9000;
        #    fastcgi_index       index.php;
        #    include             fastcgi.conf;
        #    fastcgi_hide_header X-Powered-By;
        #    fastcgi_intercept_errors on;
        #    fastcgi_param       SCRIPT_FILENAME $document_root$fastcgi_file;
        #    fastcgi_param       PATH_INFO   $fastcgi_path;
        #    fastcgi_param       SCRIPT_URI http://$server_name$uri;
        #    fastcgi_param       HTTP_REFERER $http_referer;
        #    fastcgi_param       HTTP_HOST $http_host;
        #    fastcgi_param       HTTP_ACCEPT $http_accept;
        #    fastcgi_temp_path   data/fastcgi;
        #    if ($fastcgi_script_name ~ \..*\/.*\.php) {
        #            return 403;
        #    }
        #}

        location / {
            proxy_set_header Host pre-api.edgeshop.ai;
            proxy_pass   http://127.0.0.1:7001;

            # if you want to enable cell logic, you must change your proxy_pass conf to following
            #
            # proxy_pass $ups;
        }

        location ^~ /edm/edgeshop/ {
            proxy_set_header Host  pre-copilot-edm.alibaba-inc.com;
            proxy_pass https://pre-copilot-edm.alibaba-inc.com;
        }

        location ^~ /app/ {
            add_header Content-Security-Policy "frame-ancestors https://shopify-dev.myshopify.com https://admin.shopify.com";
            proxy_pass https://pre-website.edgeshop.ai/app/;
            proxy_set_header Host pre-website.edgeshop.ai;
        }

        set $cors '';
        if ($http_origin ~* "http://localhost:3000") {
            set $cors $http_origin;
        }
        if ($http_origin ~* "https://www.alpha-rank.com") {
            set $cors $http_origin;
        }
        if ($http_origin ~* "https://www.edgeshop.ai") {
            set $cors $http_origin;
        }

        location = /home/<USER>/submit {
            add_header 'Access-Control-Allow-Origin' $cors;
            add_header 'Access-Control-Allow-Credentials' 'true';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'Cookie,Authorization,DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
            if ($request_method = 'OPTIONS') {
                    add_header 'Access-Control-Allow-Origin' $cors;
                    add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
                    add_header 'Access-Control-Allow-Credentials' 'true';
                    add_header 'Access-Control-Allow-Headers' 'Cookie,Authorization,DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';

                    add_header 'Access-Control-Max-Age' 1728000;
                    add_header 'Content-Type' 'text/plain; charset=utf-8';
                    add_header 'Content-Length' 0;
                    return 204;
            }
            proxy_set_header  Host pre-commission.ai.alibaba-inc.com;
            proxy_pass        https://pre-commission.ai.alibaba-inc.com/commission/v1/official/form_post;
        }

        error_page 404 https://pre-website.edgeshop.ai/app/aidc-innovation/SEO_Copilot/errorpage/page404?_outShopify=true;
        error_page 500 https://pre-website.edgeshop.ai/app/aidc-innovation/SEO_Copilot/errorpage/page500?_outShopify=true;
        error_page 501 https://pre-website.edgeshop.ai/app/aidc-innovation/SEO_Copilot/errorpage/page500?_outShopify=true;
        error_page 502 https://pre-website.edgeshop.ai/app/aidc-innovation/SEO_Copilot/errorpage/page500?_outShopify=true;
        error_page 503 https://pre-website.edgeshop.ai/app/aidc-innovation/SEO_Copilot/errorpage/page500?_outShopify=true;
        error_page 506 https://pre-website.edgeshop.ai/app/aidc-innovation/SEO_Copilot/errorpage/page500?_outShopify=true;
    }

    server {
        listen              80;
        server_name         wp.edgeshop.ai;

        location / {
            proxy_set_header Host aib-wordpress.alibaba-inc.com;
            proxy_pass https://aib-wordpress.alibaba-inc.com;
        }
    }

    server {
        listen              80;
        server_name         pre-pay.edgeshop.ai;

        location = / {
            proxy_pass   http://127.0.0.1:7001/;
        }

        location = /checkout {
            proxy_pass   http://127.0.0.1:7001/;
        }

        location = /checkout/result {
            proxy_pass   http://127.0.0.1:7001/;
        }

        location = /status.taobao {
            proxy_pass   http://127.0.0.1:7001/;
        }

        location = /favicon.ico {
            proxy_pass   http://127.0.0.1:7001/favicon.ico;
        }
    }

    server {
        listen              80;
        server_name         pre-blog.alpha-rank.com;

        location = / {
            proxy_set_header Host pre-blog.alpha-rank.com;
            proxy_pass   http://127.0.0.1:7001;
        }

        location / {
            # 如果 URL 不在允许的路径中，则返回 404
            if ($request_uri !~ ^/(dashboard|welcome|black|docs|alpharank|web/api|404|errorPage|blog|insight|analytics|integration|pricing|subscriptionCenter|setting|appSmith|semrush/api|blogEvaluate|crawl/api|analyze/api)) {
                return 404;
            }
            proxy_set_header Host pre-blog.alpha-rank.com;
            proxy_pass   http://127.0.0.1:7001;
        }

        error_page 404 https://pre-blog.alpha-rank.com/404;
        error_page 500 https://pre-blog.alpha-rank.com/errorPage;
        error_page 501 https://pre-blog.alpha-rank.com/errorPage;
        error_page 502 https://pre-blog.alpha-rank.com/errorPage;
        error_page 503 https://pre-blog.alpha-rank.com/errorPage;
        error_page 506 https://pre-blog.alpha-rank.com/errorPage;
    }

    server {
        listen              80;
        server_name         status.taobao.com;

        tmd off;

        location            = /nginx_status {
            stub_status     on;
        }
    }


    server {
        listen 80;
        server_name pre-api.text2go.ai;

        location = /text2go/api/textGenerator/generate {
            proxy_set_header Host pre-api.edgeshop.ai;
            proxy_pass   http://127.0.0.1:7001;
            proxy_buffering off;
            proxy_cache off;
            proxy_request_buffering off;
            add_header X-Accel-Buffering no;   # 有些客户端需要
        }

        location = /text2go/api/textGenerator/tryGenerate {
            proxy_set_header Host pre-api.edgeshop.ai;
            proxy_pass   http://127.0.0.1:7001;
            proxy_buffering off;
            proxy_cache off;
            proxy_request_buffering off;
            add_header X-Accel-Buffering no;   # 有些客户端需要
        }

        location / {
            proxy_set_header Host pre-api.edgeshop.ai;
            proxy_pass   http://127.0.0.1:7001;
        }

    }

    include apps/*.conf;
}
