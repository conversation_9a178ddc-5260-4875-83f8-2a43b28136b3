# 基于基础镜像
FROM hub.docker.alibaba-inc.com/aone-base/copilot-app_text2go:20250213164628

############# staging #################
# 设置打开jpda 调试端口。如果需要则打开下面的注释内容
ENV JPDA_ENABLE=1

# 设置spring profile或者自定义的jvm参数。如果需要则打开下面的注释内容
ENV SERVICE_OPTS=-Dspring.profiles.active=staging
ENV NGINX_CONFIG_FILE=nginx-proxy-staging.conf

# 将应用nginx脚本复制到镜像中
COPY environment/common/cai/ /home/<USER>/cai/

# 将构建出的主包复制到指定镜像目录中
COPY ${APP_NAME}.tgz /home/<USER>/${APP_NAME}/target/${APP_NAME}.tgz

# 创建sls机器标识
RUN mkdir -p /etc/ilogtail/
RUN echo -e "$APP_NAME-staging" > /etc/ilogtail/user_defined_id
RUN mkdir -p /etc/ilogtail/users
RUN touch /etc/ilogtail/users/1105571179876820


# COPY 防御工程项目到 /home/<USER>/security_shield 目录
COPY --from=reg.docker.alibaba-inc.com/alisecurity/security_shield:1.2 /home/<USER>/security_shield /home/<USER>/security_shield

# 修改启动脚本使得使工程启动时调用防御工程
RUN sh /home/<USER>/security_shield/cli.sh vuln_defense jdwp