package com.alibaba.copilot.app.infrastructure.rewirte.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "rewrite_document")
public class RewriteDocumentDO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * Database Column Remarks:
     *   主键
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     */
    private Date gmtModified;

    private String outerUserId;

    private String source;

    private String title;
    
    /**
     * 文档类型
     */
    private String type;

    private Boolean deleted;
}
