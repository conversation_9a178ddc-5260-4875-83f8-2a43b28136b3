package com.alibaba.copilot.app.infrastructure.base.gateway;


import com.alibaba.copilot.app.client.langfuse.LangfuseGateway;
import com.taobao.common.keycenter.security.Cryptograph;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

@Configuration
public class LangfuseGatewayGen {
    @Resource
    private Cryptograph cryptograph;
    @Value("${key.center.app.key}")
    private String keyCenterAppKey;

//    private static final String ingestionUrl = "https://us.cloud.langfuse.com/api/public/ingestion";
    private static final String ingestionUrl = "https://langfuse.alibaba-inc.com/api/public/ingestion";


    @Bean
    public LangfuseGateway langfuseGateway(@Value("${langfuse.username}") String username,
                                           @Value("${langfuse.password.keycenter}") String password) {
        String passwordDecrypt = cryptograph.decrypt(password, keyCenterAppKey);
        return new LangfuseGateway(ingestionUrl, username, passwordDecrypt);
    }

}

