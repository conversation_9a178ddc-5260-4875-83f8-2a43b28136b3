package com.alibaba.copilot.app.infrastructure.base.switchs;

import com.alibaba.copilot.app.client.seocopilot.dto.ShopifyProxyConfig;
import com.alibaba.copilot.app.client.seocopilot.dto.WordpressConfig;
import com.alibaba.copilot.app.domain.n8n.model.N8nFlowConf;
import com.alibaba.copilot.app.domain.seocopilot.constant.SeoConstant;
import com.alibaba.copilot.app.infrastructure.base.bean.BizPayInfo;
import com.alibaba.copilot.app.infrastructure.base.utils.EnvUtils;
import com.alibaba.copilot.app.infrastructure.seocopilot.vo.SubsciptionShareCodeVO;
import com.alibaba.copilot.enabler.client.user.constants.AppEnum;
import com.google.common.collect.Lists;
import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;

import java.util.*;

/**
 * Switch开关配置
 */
public class SwitchConfig {

    @AppSwitch(des = "环境：daily，pre，online", level = Switch.Level.p1)
    public static String env = EnvUtils.ONLINE;

    @AppSwitch(des = "睡眠时间 单位毫秒", level = Switch.Level.p1)
    public static long threadSleepMillis = 0l;

    @AppSwitch(des = "日志seo任务分组任务数", level = Switch.Level.p1)
    public static int sysLogAsyncTaskGroupCount = 100;

    @AppSwitch(des = "日志seo任务分组执行间隔 单位秒", level = Switch.Level.p1)
    public static int sysLogAsyncTaskGroupIntervalSec = 3600;

    @AppSwitch(des = "店铺级商品关键词任务分组任务数", level = Switch.Level.p1)
    public static int shopProductKeywordAsyncTaskGroupCount = 10;

    @AppSwitch(des = "店铺级商品关键词任务分组执行间隔 单位秒", level = Switch.Level.p1)
    public static int shopProductKeywordAsyncTaskGroupIntervalSec = 60;

    @AppSwitch(des = "店铺级商品关键词任务 覆盖更新", level = Switch.Level.p1)
    public static Boolean shopProductKeywordUpdate = false;

    @AppSwitch(des = "睡眠时间 单位毫秒", level = Switch.Level.p1)
    public static long scanThreadSleepMillis = 2000l;

    @AppSwitch(des = "Google 关键词同步分组任务数", level = Switch.Level.p1)
    public static int googleAsyncTaskGroupCount = 50;

    @AppSwitch(des = "Google 关键词同步执行间隔 单位秒", level = Switch.Level.p1)
    public static int googleAsyncTaskGroupIntervalSec = 1800;

    @AppSwitch(des = "Semrush Topic同步分组任务数", level = Switch.Level.p1)
    public static int semrushTopicAsyncTaskGroupCount = 50;

    @AppSwitch(des = "Semrush Topic同步执行间隔 单位秒", level = Switch.Level.p1)
    public static int semrushTopicAsyncTaskGroupIntervalSec = 1800;

    @AppSwitch(des = "全托管seo任务分组任务数", level = Switch.Level.p1)
    public static int fullAsyncTaskGroupCount = 100;

    @AppSwitch(des = "全托管seo任务分组执行间隔 单位秒", level = Switch.Level.p1)
    public static int fullAsyncTaskGroupIntervalSec = 3600;

    @AppSwitch(des = "非全托管seo任务分组任务数", level = Switch.Level.p1)
    public static int unFullAsyncTaskGroupCount = 100;

    @AppSwitch(des = "非全托管seo任务分组执行间隔 单位秒", level = Switch.Level.p1)
    public static int unFullAsyncTaskGroupIntervalSec = 3600;

    @AppSwitch(des = "哪个名称的套餐当前是推荐的套餐", level = Switch.Level.p1)
    public static String seoRecommendPlanName = "Advanced";

    @AppSwitch(des = "alphaRankAppName", level = Switch.Level.p1)
    public static String alphaRankAppName = "";

    @AppSwitch(des = "alphaRankClientId", level = Switch.Level.p1)
    public static String alphaRankClientId = "";

    @AppSwitch(des = "stripeApiKey", level = Switch.Level.p1)
    public static String stripeApiKey = "sk_test_51PEB93BUuFYymNc8YYpUAZoTzCdhjqh3iOUYiRuG7t1sjVjoWNloajSiLVddNLqBfg8hlis2eenhkDDTmZzPRSmj00N4YsiQtQ";

    @AppSwitch(des = "pricingRedirectUrl", level = Switch.Level.p1)
    public static String pricingRedirectUrl = "https://blog.alpha-rank.com/pricing";

    @AppSwitch(des = "JWT_SECRET_KEY", level = Switch.Level.p1)
    public static String JWT_SECRET_KEY = "6e0f950b8f2dd6fe58be537fc8d67e3ada842f47ba38bce933bc99d9515fcaf7";

    @AppSwitch(des = "EMAIL_MOCK_FOR_AUTH", level = Switch.Level.p1)
    public static String EMAIL_MOCK_FOR_AUTH = "";

    @AppSwitch(des = "planId2LookupKey", level = Switch.Level.p1)
    public static Map<String, String> planId2LookupKey = new HashMap<String, String>() {
        {
            put("SEO_COPILOT_SITE#57", "SEO_COPILOT_SITE#20240530#0");
            put("SEO_COPILOT_SITE#58", "SEO_COPILOT_SITE#20240530#3199");
            put("SEO_COPILOT_SITE#59", "SEO_COPILOT_SITE#20240530#35988");
            put("SEO_COPILOT_SITE#60", "SEO_COPILOT_SITE#20240530#5599");
            put("SEO_COPILOT_SITE#61", "SEO_COPILOT_SITE#20240530#62988");
        }
    };

    @AppSwitch(des = "t2gPlanId2LookupKey", level = Switch.Level.p1)
    public static Map<String, String> t2gPlanId2LookupKey = new HashMap<String, String>() {
        {
            put("69", "T2G#20250220#0");
            put("70", "T2G#20250220#1199");
            put("71", "T2G#20250220#5748");
            put("72", "T2G#20250220#1499");
            put("73", "T2G#20250220#7188");
            put("74", "T2G#20250220#4999");
            put("75", "T2G#20250220#23988");

        }
    };

    @AppSwitch(des = "独立博客试用期", level = Switch.Level.p1)
    public static long trialPeriodDays = 1l;

    @AppSwitch(des = "alphaRank_appName_clientId", level = Switch.Level.p1)
    public static Map<String, String> alphaRank_appname_clientid = new HashMap<String, String>() {
        {
            //clientid,appName
            put("7859644b7d5891f276dbbe315dedc011", "alpharank-1");
            put("614907440cd5c1b5c1a934f8364efd15", "alpharank_pre");
        }
    };

    @AppSwitch(des = "是否校验密码保护开关（主要用于测试同学）", level = Switch.Level.p1)
    public static boolean isNeedCheckShopSecret = true;

    @AppSwitch(des = "账号打通切换开关", level = Switch.Level.p1)
    public static boolean enableAccountInterceptorSwitcher = true;

    @AppSwitch(des = "Copyleaks的webhook回调Url", level = Switch.Level.p1)
    public static String copyleaksWebhookUrl = "https://9aa1-42-120-74-206.ngrok-free.app/callback";

    @AppSwitch(des = "是否开启Copyleaks检验", level = Switch.Level.p1)
    public static Boolean enablePlagiarism = false;

    @AppSwitch(des = "异步任务command异常后是否忽略失败", level = Switch.Level.p1)
    public static Boolean ignoreFail = true;

    @AppSwitch(des = "全托管全局开关", level = Switch.Level.p1)
    public static Boolean fullyManagedSwitch = false;

    @AppSwitch(des = "全托管白名单,在名单中的用户在全托管开关关掉时可以使用全托管", level = Switch.Level.p1)
    public static ArrayList<Long> fullyManagedWhiteList = new ArrayList<Long>() {
        {
            add(999L);
        }
    };

    @AppSwitch(des = "GPTs配置：DsCopilot请求BindKey的url", level = Switch.Level.p1)
    public static String gptsRequestBindKeyUrlForDsCopilot = "https://pre-api.dscopilot.ai/api/account-bind/request-bind-key?redirect=";

    @AppSwitch(des = "GPTs配置：请求BindKey的重定向url", level = Switch.Level.p1)
    public static String gptsRequestBindKeyRedirectUrl = "https://pre-api.edgeshop.ai/api/api-integration/bind-biz-account?bindSessionId=";

    @AppSwitch(des = "是否校验 App Block开启状态", level = Switch.Level.p1)
    public static Boolean idNeedCheckAppEmbedBlockSwitch = true;

    @AppSwitch(des = "3期算法调用vipServer", level = Switch.Level.p1)
    public static String algorithmVipServer = "aigc.tpp.taobao.com.vipserver_pre";

    @AppSwitch(des = "3期算法调用appId", level = Switch.Level.p1)
    public static String algorithmAppId = "40264";

    @AppSwitch(des = "是否启用解绑操作，默认false", level = Switch.Level.p1)
    public static Boolean isOpenUnbindGscInterface = false;

    @AppSwitch(des = "全量Check Collection、Product的店铺", level = Switch.Level.p1)
    public static List<Long> checkAllShopList = new ArrayList<>();

    @AppSwitch(des = "诊断间隔时间，默认14天", level = Switch.Level.p1)
    public static Map<Long, Integer> checkGapDays = new HashMap<Long, Integer>() {
        {
            put(000L, 1);
        }
    };

    @AppSwitch(des = "blog多轮交互的风格", level = Switch.Level.p1)
    public static ArrayList<String> blogMultiturnGuideStyles = new ArrayList<String>() {
        {
            add("Expert");
            add("Daring");
            add("Playful");
            add("Sophisticated");
            add("Persuasive");
            add("Supportive");
            add("Custom");
            add("Trendy");
        }
    };

    @AppSwitch(des = "blog多轮交互的类目", level = Switch.Level.p1)
    public static ArrayList<String> blogMultiturnGuideCategory = new ArrayList<String>() {
        {
            add("Business Insights");
            add("Industry Trends");
            add("Marketing Tips");
            add("Sales Promotions");
            add("Customer Stories");
//            add("Product Introduction");
        }
    };

    @AppSwitch(des = "rateLimiterSwitch，默认false不开启限流", level = Switch.Level.p1)
    public static Boolean rateLimiterSwitch = false;

    @AppSwitch(des = "降级为直接调用google进行评估网页质量", level = Switch.Level.p1)
    public static Boolean downgradeToDirectGoogleEvaluation = false;

    @AppSwitch(des = "最大重试次数", level = Switch.Level.p1)
    public static Integer maxRetryTimes = 3;

    @AppSwitch(des = "Blog最低分数要求", level = Switch.Level.p1)
    public static Integer blogLowestScore = 90;

    @AppSwitch(des = "主营行业", level = Switch.Level.p1)
    public static List<String> canSelectMainIndustries = new ArrayList<String>() {
        {
            add("Animals & Pet Supplies");
            add("Appareal & Accessories");
            add("Arts & Entertainment");
            add("Baby & Toddier");
            add("Business & Industrial");
            add("Cameras & Optics");
            add("Electronics");
            add("Food, Beverages & Tobacco");
            add("Furniture");
            add("Hardware");
            add("Health & Beauty");
            add("Home & Garden");
            add("Luggage & Bags");
            add("Mature");
            add("Media");
            add("Office Supplies");
            add("Religious & Ceremorial");
            add("Software");
            add("Sporting Goods");
            add("Toys & Games");
            add("Vehicles & Parts");
            add("Gift Cards");
        }
    };

    @AppSwitch(des = "目标人群", level = Switch.Level.p1)
    public static List<String> canSelectTargetCustomers = new ArrayList<String>() {
        {
            add("Niche Market Enthusiasts");
            add("Value Seekers");
            add("Quality-Conscious Consumers");
            add("Local Community Supporters");
            add("Ethical Consumers");
            add("Tech-Savvy Shoppers");
            add("Gift Buyers");
            add("Trend Followers");
            add("Busy Professionals");
            add("Loyal Brand Advocates");
        }
    };

    @AppSwitch(des = "转化目标", level = Switch.Level.p1)
    public static List<String> canSelectConversionGoals = new ArrayList<String>() {
        {
            add("Sales Revenue");
            add("Lead Conversion");
            add("Organic Traffic");
            add("Brand Awareness");
            add("Promotional Offers");
        }
    };

    @AppSwitch(des = "品牌风格", level = Switch.Level.p1)
    public static List<String> canSelectBrandStyles = new ArrayList<String>() {
        {
            add("{\"brandStyle\":\"Expert\",\"descriptions\":[\"Suitable for business, finance, academic, or technical blogs.\"]}");
            add("{\"brandStyle\":\"Custom\",\"descriptions\":[\"Suitable for Lifestyle, personal, and general interest blogs.\"]}");
            add("{\"brandStyle\":\"Daring\",\"descriptions\":[\"Suitable for self-help, motivational, and personal development blogs.\"]}");
            add("{\"brandStyle\":\"Playful\",\"descriptions\":[\"Suitable for entertainment, humor, and some travel and lifestyle blogs.\"]}");
            add("{\"brandStyle\":\"Persuasive\",\"descriptions\":[\"Suitable for marketing, sales, and business blogs.\"]}");
            add("{\"brandStyle\":\"Sophisticated\",\"descriptions\":[\"Suitable for personal journals, philosophical, and some cultural blogs.\"]}");
            add("{\"brandStyle\":\"Supportive\",\"descriptions\":[\"Suitable for health, wellness, and community support blogs.\"]}");
            add("{\"brandStyle\":\"Trendy\",\"descriptions\":[\"Suitable for fashion, arts, and contemporary culture blogs.\"]}");
        }
    };

    @AppSwitch(des = "折扣码和对应折扣信息", level = Switch.Level.p2)
    public static Map<String, SubsciptionShareCodeVO> shareCodeSubscriptionConfig = new HashMap<String, SubsciptionShareCodeVO>() {
        {
            put("Advanced_MONTH_ZiXuan100", buildSubsciptionShareCodeVO());
        }
    };

    /**
     * 构建 SubsciptionShareCode 折扣信息
     */
    private static SubsciptionShareCodeVO buildSubsciptionShareCodeVO() {
        SubsciptionShareCodeVO subsciptionShareCodeVO = new SubsciptionShareCodeVO();
        subsciptionShareCodeVO.setSharePrice(9.9);
        subsciptionShareCodeVO.setCanShareCount(5);
        subsciptionShareCodeVO.setOverdueTime("2024-05-29 00:00:00");
        return subsciptionShareCodeVO;
    }

    @AppSwitch(des = "算法生成灰度比例", level = Switch.Level.p1)
    public static Map<String, Integer> algorithm_generate_grayScale_switch = new HashMap<String, Integer>() {
        {
            //模块,比例
            put("outline", 0);
        }
    };


    @AppSwitch(des = "顺序消息的webhookTopic集合", level = Switch.Level.p1)
    public static ArrayList<String> webhookTopicOrderMessage = new ArrayList<String>() {
        {
            add("products/create");
            add("products/update");
            add("collections/create");
            add("collections/update");
        }
    };

    @AppSwitch(des = "Shopify Asset API版本", level = Switch.Level.p1)
    public static String shopifyAssetAPIVersion = "2023-07";

    @AppSwitch(des = "Shopify Batch Blog-Shop Id ", level = Switch.Level.p1)
    public static Long shopifyBatchBlogShopId = 75L;

    @AppSwitch(des = "Wordpress Batch Blog-config ", level = Switch.Level.p1)
    public static WordpressConfig wordpressBatchBlogConfigs = buildWordpressConfig();

    /**
     * wordpress 配置信息
     *
     * @return
     */
    private static WordpressConfig buildWordpressConfig() {
        WordpressConfig wordpressConfig = new WordpressConfig();
        wordpressConfig.setHost("https://wp.edgeshop.ai");
        wordpressConfig.setAccessToken("Basic d29yZHByZXNzLWNoYW5namlhbmc6Y1JzQSBySWltIG5GdjMgNVpnSyBHWVljIEprY20=");
        wordpressConfig.setAuthors(Lists.newArrayList(1, 2, 3, 4, 5, 6, 7, 8));
        wordpressConfig.setWebsiteCategoryNo(new HashMap<String, Integer>() {{
            put("alpha-rank", 5);
            put("edgeshop", 38);
            put("dscopilot", 87);
        }});

        wordpressConfig.setWebsiteDomain(new HashMap<String, String>() {{
            put("shopify-store", "https://www.smarts-buy.com/");
            put("alpha-rank", "https://www.alpha-rank.com/");
            put("edgeshop", "https://www.edgeshop.ai/");
            put("dscopilot", "https://www.dscopilot.ai/");
        }});
        return wordpressConfig;
    }

    @AppSwitch(des = "对接支付的业务方信息配置", level = Switch.Level.p2)
    public static Map<String, BizPayInfo> appPayInfoConfigs = new HashMap<String, BizPayInfo>() {
        {
            put(AppEnum.DS_COPILOT.getCode(), buildDSPayInfoConfig());
        }
    };

    /**
     * 构建DSCopilot支付信息
     */
    private static BizPayInfo buildDSPayInfoConfig() {
        BizPayInfo bizPayInfo = new BizPayInfo();
        bizPayInfo.setAppCode(AppEnum.DS_COPILOT.getCode());
        bizPayInfo.setProductName("DSCopilot");
        bizPayInfo.setAppLogo("https://img.alicdn.com/imgextra/i3/O1CN01CT41sI1rAjKvF0jvE_!!6000000005591-2-tps-299-301.png");
        bizPayInfo.setUseRightNowRelativeUrl("#/?reload=updateUserInfo");
        bizPayInfo.setPayRedirectUrl("https://pay.edgeshop.ai/checkout/result");
        bizPayInfo.setAppDescription("Everything you need to find AliExpress product information, real-time order management and rich AI-assisted capabilities to expertly manage your shopify business");
        return bizPayInfo;
    }

    @AppSwitch(des = "官网对应域名", level = Switch.Level.p2)
    public static Map<String, String> websiteDomainMaps = new HashMap<String, String>() {
        {
            put("shopify-store", "https://www.smarts-buy.com/");
            put("alpha-rank", "https://www.alpha-rank.com/");
            put("edgeshop", "https://www.edgeshop.ai/");
            put("dscopilot", "https://www.dscopilot.ai/");
        }
    };

    @AppSwitch(des = "sermrush获取关键词数量", level = Switch.Level.p2)
    public static Integer SITE_ANALYSIS_SEMRUSH_KEYWORD_LIMIT = 100;

    @AppSwitch(des = "sermrush获取关键词数量(未订阅)", level = Switch.Level.p2)
    public static Integer SITE_ANALYSIS_SEMRUSH_KEYWORD_NO_BENEFIT_LIMIT = 3;


    @AppSwitch(des = "sermrush获取Topic数量", level = Switch.Level.p2)
    public static Integer SITE_ANALYSIS_SEMRUSH_TOPIC_LIMIT = 10;


    @AppSwitch(des = "sermrush获取竞对数量", level = Switch.Level.p2)
    public static Integer SITE_ANALYSIS_SEMRUSH_COMPRTITOR_LIMIT = 3;

    @AppSwitch(des = "延时消费时间 单位毫秒", level = Switch.Level.p1)
    public static long subscriptionMessageConsumerDelayTime = 1000L;

    @AppSwitch(des = "uic注册消息delayTime", level = Switch.Level.p1)
    public static long uicMeatqDelayTime = 3000L;

    @AppSwitch(des = "alpharank独立网站用户是否有权限对订阅进行降级和取消用户白名单", level = Switch.Level.p2)
    public static Set<Long> subscriptionDemoteWhiteList = new HashSet<Long>() {
        {
            add(1119L);
        }
    };

    @AppSwitch(des = "n8n流程配置项", level = Switch.Level.p2)
    public static List<N8nFlowConf> n8nFlowConfig = new ArrayList<N8nFlowConf>() {
        {
            add(new N8nFlowConf().setAppCode(SeoConstant.APP_CODE).setSceneCode("test").setWebhook("https://alpharank.app.n8n.cloud/webhook-test/40ad41be-65ea-463d-80d9-8b869f20a945"));
        }
    };

    @AppSwitch(des = "Semrush接口请求来源", level = Switch.Level.p1)
    public static Map<String, String> sourcePlatform = new HashMap<String, String>() {
        {
            // 算法
            put("algorithm_keyword", "jwVEBGMCZoRjOrPVyWjRnQnLkWjNYrKdepIJumuIkhRbwcYAhSperIAZOxxOdRWR");
            // 关键词工具
            put("keyword_tool", "jwVEBGMCZoRjOrPVyWjRnQnLkWjNYrKdepIJumuIkhRbwcYAhSperIAZOxsdfwag");
        }
    };

    @AppSwitch(des = "Wordpress 用增站配置", level = Switch.Level.p1)
    public static Map<String, WordpressConfig> userWordpressConfigMaps = new HashMap<String, WordpressConfig>() {
        {
            //
            put("seoapp-google.com", buildSeoAppConfig());
            put("wp.edgeshop.ai", buildEdgeShopConfig());
        }
    };

//    @AppSwitch(des = "Wordpress AE站配置", level = Switch.Level.p1)
//    public static Map<String, WordpressConfig> userGrowthWordpressConfigMaps = WordpressSiteConfig.userGrowthWordpressConfigMaps();


    /**
     * wordpress 配置信息
     *
     * @return
     */
    private static WordpressConfig buildSeoAppConfig() {
        WordpressConfig wordpressConfig = new WordpressConfig();
        wordpressConfig.setHost("https://seoapp-google.com");
        wordpressConfig.setAccessToken("Basic ********************************************************************");
        return wordpressConfig;
    }

    private static WordpressConfig buildEdgeShopConfig() {
        WordpressConfig wordpressConfig = new WordpressConfig();
        wordpressConfig.setHost("https://wp.edgeshop.ai");
        wordpressConfig.setAccessToken("Basic d29yZHByZXNzLWNoYW5namlhbmc6Y1JzQSBySWltIG5GdjMgNVpnSyBHWVljIEprY20=");
        return wordpressConfig;
    }


    @AppSwitch(des = "Blog插图使用商品图片的白名单", level = Switch.Level.p1)
    public static List<Long> blogImageWhiteList = new ArrayList<Long>() {
        {
            add(1L);
        }
    };

    @AppSwitch(des = "域名切换消息延时消费时间 单位毫秒", level = Switch.Level.p1)
    public static long domainChangedConsumerDelayTime = 1000L;

    @AppSwitch(des = "站点解析是否走算法通过URL召回关键词兜底", level = Switch.Level.p1)
    public static Boolean useUrl2Keywords = true;

    @AppSwitch(des = "调用shopify是否使用proxy", level = Switch.Level.p1)
    public static Boolean useShopifyClientWithProxy = true;

    @AppSwitch(des = "Shopify Proxy配置", level = Switch.Level.p1)
    public static List<ShopifyProxyConfig> shopifyProxyConfigs = new ArrayList<ShopifyProxyConfig>() {
        {
            add(ShopifyProxyConfig.builder()
                    .hostname("d78f3d368eeb7a08.fkz.as.ipidea.online")
                    .port(2336)
                    .username("ouzhencong-zone-custom-region-sg")
                    .password("hello_1234")
                    .build()
            );
        }
    };

    @AppSwitch(des = "主营行业SEO指导书链接", level = Switch.Level.p1)
    public static Map<String, String> mainIndustrySeoGuideBooks = new HashMap<String, String>() {
        {
            put("General Guidelines", "https://t-selection-algorithms-image.oss-ap-southeast-1.aliyuncs.com/alpha-rank/guidebook/2024%20AlphaRank%20Content%20Guidelines.pdf");
        }
    };

    @AppSwitch(des = "新人引导是否使用semrush获取数据", level = Switch.Level.p1)
    public static Boolean userGuideUseSemrush = true;

    @AppSwitch(des = "爬虫接口URL", level = Switch.Level.p1)
    public static String CRAWL_HTML_URL = "http://**************:8006/seo/download_url";

    @AppSwitch(des = "爬虫超时时间", level = Switch.Level.p1)
    public static Integer CRAWL_TIMEOUT = 10;

    @AppSwitch(des = "免登接口用seoShopId", level = Switch.Level.p1)
    public static Long PASSWORDLESS_SEO_SHOP_ID = 2041l;

    @AppSwitch(des = "多语言管理白名单", level = Switch.Level.p1)
    public static List<Long> multiLanguageWhitelist = new ArrayList<Long>() {
        {
            add(1L);
        }
    };

    @AppSwitch(des = "榜单类对比类白名单", level = Switch.Level.p1)
    public static List<String> rankAndComparisonWhitelist = new ArrayList<String>();


    @AppSwitch(des = "爬虫请求Token", level = Switch.Level.p1)
    public static List<String> crawlAccessToken = new ArrayList<String>() {
        {
            // token
            add("jwVEBGMCZoRjOrPVyWjRnQnLkWjNYrKdepIJumuIkhRbwcYAhSperIAZOxxOdRWR");
        }
    };

    @AppSwitch(des = "是否拒绝爬虫请求", level = Switch.Level.p1)
    public static Boolean refuseCrawlRequest = false;

    @AppSwitch(des = "用增站是否支持图片风格", level = Switch.Level.p1)
    public static Boolean supportImageStyle = false;

    @AppSwitch(des = "黑五数据刷新", level = Switch.Level.p1)
    public static Boolean canFlushBlackFridayData = false;

    @AppSwitch(des = "是否使用 claude", level = Switch.Level.p1)
    public static Boolean claude = false;

    @AppSwitch(des = "Product/Collection优化走Prompt还是Tpp", level = Switch.Level.p1)
    public static Boolean prompt = false;

    @AppSwitch(des = "爬虫服务-站点最大爬虫页面数量", level = Switch.Level.p1)
    public static Integer maxCrawlPageNum = 5;

    @AppSwitch(des = "爬虫服务-每日最大爬虫站点数量", level = Switch.Level.p1)
    public static Integer maxCrawlSiteNum = 10;

    @AppSwitch(des = "爬虫服务-是否进行页面类型识别", level = Switch.Level.p1)
    public static Boolean isNeedPageType = false;

    @AppSwitch(des = "Blog Creator API 是否开启", level = Switch.Level.p1)
    public static Boolean blogCreatorApiEnabled = true;

    @AppSwitch(des = "text2go 首页促销信息", level = Switch.Level.p1)
    public static String t2gPromoInfo = "{}";

    @AppSwitch(des = "Google Ads API 开发者令牌", level = Switch.Level.p1)
    public static String googleAdsDeveloperToken = "pdNg3RYgRYFtbrufZOzCqQ";

    @AppSwitch(des = "Google Ads API 客户端ID", level = Switch.Level.p1)
    public static String googleAdsClientId = "529807280940-2d04pjedn7nr2ei5tr8qeje1je895lgl.apps.googleusercontent.com";

    @AppSwitch(des = "Google Ads API 客户端密钥", level = Switch.Level.p1)
    public static String googleAdsClientSecret = "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";

    @AppSwitch(des = "Google Ads API 刷新令牌", level = Switch.Level.p1)
    public static String googleAdsRefreshToken = "1//04VgJ19_mpQ10CgYIARAAGAQSNwF-L9IrvyNjULUlWVEGjXC2JyyUhbX_CgHhISnnzggUMSHj5CPSqCXisftIdDO8I8-FZhytOmY";

    @AppSwitch(des = "Google Ads MCC管理账户ID", level = Switch.Level.p1)
    public static String googleAdsCustomerId = "456-841-6203";

    @AppSwitch(des = "Google Ads 数据API验证密钥", level = Switch.Level.p1)
    public static String googleAdsApiKey = "t2g_ads_9c7f35e8d1a6b4";

    @AppSwitch(des = "templateAllowEmail", level = Switch.Level.p2)
    public static List<String> rewriteTemplateAllowEmail = Lists.newArrayList("<EMAIL>", "<EMAIL>");
}
