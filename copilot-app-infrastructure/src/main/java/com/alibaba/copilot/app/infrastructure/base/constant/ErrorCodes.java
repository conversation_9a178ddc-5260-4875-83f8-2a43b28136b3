package com.alibaba.copilot.app.infrastructure.base.constant;

import com.alibaba.copilot.boot.basic.exception.ErrorCode;

/**
 * 错误码
 */
public class ErrorCodes extends ErrorCode {
    /**
     * SEO Copilot
     */
    public static final ErrorCode NO_PRODUCT_EXIST = new ErrorCode("SEO0021", "no product exists!");
    public static final ErrorCode NULL_SHOP_ID = new ErrorCode("SEO0001", "shop id is null!");
    public static final ErrorCode NULL_GOOGLE_CODE = new ErrorCode("SEO0002", "google code is null!");
    public static final ErrorCode NULL_GOOGLE_TOKEN = new ErrorCode("SEO0003", "google response is null!");
    public static final ErrorCode NULL_GOOGLE_ACCESS_TOKEN = new ErrorCode("SEO0004", "google access token is null!");
    public static final ErrorCode NULL_GOOGLE_REFRESH_TOKEN = new ErrorCode("SEO0005", "google refresh token is null!");
    public static final ErrorCode NULL_GOOGLE_TOKEN_TYPE = new ErrorCode("SEO0006", "google token type is null!");
    public static final ErrorCode NULL_GOOGLE_TOKEN_EXPIRE = new ErrorCode("SEO0007", "google token expire time is null!");
    public static final ErrorCode SHOP_NOT_EXIST = new ErrorCode("SEO0008", "shop not exist!");
    public static final ErrorCode USER_NOT_EXIST = new ErrorCode("USER_NOT_EXIST", "user not exist!");
    public static final ErrorCode NULL_GSC_QUERY_RESPONSE = new ErrorCode("SEO0008", "google search console query method response is null!");
    public static final ErrorCode NULL_SHOPIFY_SHOP_THEME = new ErrorCode("SEO0009", "shopify shop themes is null!");
    public static final ErrorCode NULL_SHOPIFY_SHOP_MAIN_THEME = new ErrorCode("SEO0009", "shopify shop main theme is null!");
    public static final ErrorCode NULL_LIGHTHOUSE_ERROR = new ErrorCode("SEO0010", "lighthouse response error!");
    public static final ErrorCode NULL_GSC_INDEX_RESPONSE = new ErrorCode("SEO0011", "google search console index method response is null!");
    public static final ErrorCode NULL_SHOP_HOMEPAGE = new ErrorCode("SEO0012", "shop homepage is null!");
    public static final ErrorCode NULL_SHOP_DOMAIN = new ErrorCode("SEO0013", "shopDomain id is null!");
    public static final ErrorCode SHOPIFY_SHOP_NOT_EXIST = new ErrorCode("SEO0015", "shopify shop not exist!");
    public static final ErrorCode NOT_NULL_WEB_TYPE = new ErrorCode("SEO0016", "web page is null!");
    public static final ErrorCode PLAN_NOT_SUPPORT_OPERATE = new ErrorCode("SEO0014", "Please upgrade your subscription plan!");
    public static final ErrorCode RUN_OUT_OF_QUOTA = new ErrorCode("SEO0022", "Your quota runs out, please try it later!");
    // PD 给出的新文案，经PD确认，无论是recheck/optimize报错同一个文案
    public static final ErrorCode SOME_TASK_RUNNING = new ErrorCode("WARNING_SEO0023", "Content is currently in the process of optimization. Please attempt access at a later time.");
    public static final ErrorCode CLOSE_FULL_LOADING_STATUS = new ErrorCode("SEO0024", "shop has task is running!");
    public static final ErrorCode GSC_UNBING_EXCEPTION = new ErrorCode("SEO0026", "Your store currently has diagnostic tasks in progress, please try again later!");

    public static final ErrorCode SEO_SCAN_EXCEPTION = new ErrorCode("SEO_SCAN_EXCEPTION", "SEO_SCAN_EXCEPTION", "");
    public static final ErrorCode SEO_CHECK_EXCEPTION = new ErrorCode("SEO_CHECK_EXCEPTION", "SEO_CHECK_EXCEPTION", "");
    public static final ErrorCode SEO_OPTIMIZE_EXCEPTION = new ErrorCode("SEO_OPTIMIZE_EXCEPTION", "SEO_OPTIMIZE_EXCEPTION", "");
    public static final ErrorCode THEME_ROLLBACK_ERROR = new ErrorCode("SEO0030", "theme rollback error,please try again later!");
    public static final ErrorCode SHOPIFY_ARTICLE_NOT_EXIST = new ErrorCode("SHOPIFY_ARTICLE_NOT_EXIST", "the article has been deleted in Shopify.");
    public static final ErrorCode ARTICLE_NOT_EXIST = new ErrorCode("ARTICLE_NOT_EXIST", "the article has been deleted.");
    /**
     * 错误码不能变
     */
    public static final ErrorCode APP_BLOCK_NOT_OPEN_ERROR = new ErrorCode("SEO0035", "please enable AlphaRank!");

    public static final ErrorCode THEME_ROLLBACK_RUNNING_TASK_EXCEPTION = new ErrorCode("SEO0036", "Your store currently has diagnostic tasks in progress, please try again later!");

    /**
     * 命中风控
     */
    public static final ErrorCode CONTENT_SECURITY_RISK = new ErrorCode("CONTENT_SECURITY_RISK", "Oops! It seems there's an issue with generating. Kindly try again later.");
    /**
     * 风控服务异常: 仅用于服务异常告警，业务自动降级，当风控通过处理
     */
    public static final ErrorCode CONTENT_SECURITY_FAIL = new ErrorCode("CONTENT_SECURITY_FAIL", "content security service invoke fail.");

    public static final ErrorCode PRODUCT_DELETED = new ErrorCode("SEO0040", "This product has been deleted.");
    public static final ErrorCode COLLECTION_DELETED = new ErrorCode("SEO0040", "This collection has been deleted.");
    public static final ErrorCode INVALID_PARAMETERS = new ErrorCode("SEO0041", "Invalid parameters.");
    public static final ErrorCode REQUEST_SHOPIFY_ERROR = new ErrorCode("SEO0042", "Request shopify error.");
    public static final ErrorCode NO_COLLECTION_EXIST = new ErrorCode("SEO0021", "no collection exists!");

    public static final ErrorCode NO_OPTIMIZED_KEYWORDS_GET = new ErrorCode("NO_OPTIMIZED_KEYWORDS_GET", "no optimized keywords get.");

    public static final ErrorCode IMAGE_SHOPIFY_EXCEPTION = new ErrorCode("IMAGE_SHOPIFY_EXCEPTION", "process image, occur shopify exception.");

    public static final ErrorCode BLOG_ROLLBACK_ERROR = new ErrorCode("BLOG_ROLLBACK_ERROR", "article reset failed.");

    public static final ErrorCode BLOG_SHOPIFY_DELETED = new ErrorCode("BLOG_SHOPIFY_DELETED", "article has been deleted on shopify.");

    /**
     * 重试异常
     */
    public static final ErrorCode RETRY_ERROR = new ErrorCode("SEO0051", "retrying failed!");

    /**
     * Blog Generate Prompt为空异常
     */
    public static final ErrorCode BLOG_GEN_PROMPT_BLANK_EXP = new ErrorCode("SEO0061", "blog generate prompt is blank!");

    /**
     * 用户token已过期
     */
    public static final ErrorCode USER_TOKEN_HAD_EXPIRED = new ErrorCode("USER_TOKEN_HAD_EXPIRED", "user token had expired!");

    /**
     * 用户输入url不合法
     */
    public static final ErrorCode USER_INPUT_URL_INVALID = new ErrorCode("USER_INPUT_URL_INVALID", "The URL is invalid, please re-enter it!");

    /**
     * 通过Domain解析关键词失败
     */
    public static final ErrorCode DOMAIN_KEYWORD_ERROR = new ErrorCode("URL_KEYWORD_ERROR", "Failed to resolve keywords through domain name resolution!");

    /**
     * 没有权限添加竞对
     */
    public static final ErrorCode NO_PERMISSION_ADD_COMPETITOR = new ErrorCode("NO_PERMISSION_ADD_COMPETITOR", "You don't have permission to add competitors!");

    /**
     * 竞对数量达到上限
     */
    public static final ErrorCode COMPETITOR_NUM_REACH_LIMIT = new ErrorCode("COMPETITOR_NUM_REACH_LIMIT", "The number of competitors has reached the limit!");

    /**
     * GSC未绑定成功
     */
    public static final ErrorCode GSC_UNSUCCESSFUL_BINDING = new ErrorCode("SEO0120", "GSC Unsuccessful Binding");
    /**
     * GA未绑定成功
     */
    public static final ErrorCode GA_UNSUCCESSFUL_BINDING = new ErrorCode("SEO0121", "GA Unsuccessful Binding");

    /**
     * Blog title重复
     */
    public static final ErrorCode BLOG_TITLE_DUPLICATE = new ErrorCode("BLOG_TITLE_DUPLICATE", "Blog title duplicate");

    /**
     * Blog title生成失败
     */
    public static final ErrorCode BLOG_TITLE_GEN_FAIL = new ErrorCode("BLOG_TITLE_GEN_FAIL", "Blog title generate fail");

    /**
     * Blog outline生成失败
     */
    public static final ErrorCode BLOG_OUTLINE_GEN_FAIL = new ErrorCode("BLOG_OUTLINE_GEN_FAIL", "Blog outline generate fail");

    /**
     * GSC 未绑定
     */
    public static final ErrorCode GSC_UNBOUND = new ErrorCode("GSC_UNBOUND", "gsc is not bound");

    /**
     * GSC 绑定无效
     */
    public static final ErrorCode GSC_INVALID = new ErrorCode("GSC_INVALID", "gsc binding is invalid");

    /**
     * GSC 未认证
     */
    public static final ErrorCode GSC_UNAUTHORIZED = new ErrorCode("GSC_UNAUTHORIZED", "gsc unauthorized");

    /**
     * GSC 权限不足
     */
    public static final ErrorCode GSC_INSUFFICIENT_PERMISSION = new ErrorCode("GSC_INSUFFICIENT_PERMISSION", "gsc Insufficient permissions");

    /**
     * sitemap提交未知异常
     */
    public static final ErrorCode SITEMAP_SUBMIT_UNKNOWN_EXCEPTION = new ErrorCode("SITEMAP_SUBMIT_UNKNOWN_EXCEPTION", "gsc submit sitemap unknown exception");


    public static final ErrorCode PRODUCT_ROLLBACK_ERROR = new ErrorCode("PRODUCT_ROLLBACK_ERROR", "product reset failed.");

    public static final ErrorCode PRODUCT_SHOPIFY_DELETED = new ErrorCode("PRODUCT_SHOPIFY_DELETED", "product has been deleted on shopify.");

    public static final ErrorCode COLLECTION_ROLLBACK_ERROR = new ErrorCode("COLLECTION_ROLLBACK_ERROR", "collection reset failed.");

    public static final ErrorCode COLLECTION_SHOPIFY_DELETED = new ErrorCode("COLLECTION_SHOPIFY_DELETED", "collection has been deleted on shopify.");


    /**
     * 发布wordpress
     */
    public static final ErrorCode PUBLISH_UPLOAD_IMAGE_ERROR = new ErrorCode("PUBLISH_UPLOAD_IMAGE_ERROR", "Some issues occurred. Please try again later.");
    public static final ErrorCode PUBLISH_CREATE_POSTS_ERROR = new ErrorCode("PUBLISH_CREATE_POSTS_ERROR", "Some issues occurred. Please try again later.");
    public static final ErrorCode GET_CATEGORY_ERROR = new ErrorCode("GET_CATEGORY_ERROR", "Some issues occurred. Please try again later.");

    public static final ErrorCode PUBLISH_AUTH_ERROR = new ErrorCode("PUBLISH_AUTH_ERROR", "Access not granted. Please go to the Integration page to complete the authorization according to the guidance document.");

    public static final ErrorCode PUBLISH_REPEAT_PUBLISH = new ErrorCode("PUBLISH_REPEAT_PUBLISH", "The blog is in the process of being published. Please don't resubmit for publication.");
    public static final ErrorCode PUBLISH_PUBLISHED_ERROR = new ErrorCode("PUBLISH_PUBLISHED_ERROR", "The blog has been published. Please refresh the page to view it.");

    public static final ErrorCode PUBLISH_NOT_SUPPORT_PLATFORM_ERROR = new ErrorCode("PUBLISH_NOT_SUPPORT_PLATFORM_ERROR", "Publishing platform error.");

    public static final ErrorCode PUBLISH_STATUS_ERROR = new ErrorCode("PUBLISH_STATUS_ERROR", "Article status error.");

    public static final ErrorCode SHOP_LANG_NOT_EQUAL_ARTICLE_LANG = new ErrorCode("SHOP_LANG_NOT_EQUAL_ARTICLE_LANG", "The article language does not match the site language.");

    public static final ErrorCode SHOPIFY_TRANSLATABLE_RESOURCES_BY_IDS_ERROR = new ErrorCode("SHOPIFY_TRANSLATABLE_RESOURCES_BY_IDS_ERROR", "Failed to obtain resource information according to GraphQL.");

    public static final ErrorCode SHOPIFY_TRANSLATIONS_REGISTER_ERROR = new ErrorCode("SHOPIFY_TRANSLATIONS_REGISTER_ERROR", "Multilingual registration failed.");

    public static final ErrorCode SHOPIFY_ARTICLE_PUBLISH_ERROR = new ErrorCode("SHOPIFY_ARTICLE_PUBLISH_ERROR", "Article publishing failed.");

    /**
     * 博客删除
     */
    public static final ErrorCode BLOG_DELETE_MULTI_LANG = new ErrorCode("BLOG_DELETE_MULTI_LANG", "The current article has multiple language versions and does not support deletion.");

    public static final ErrorCode BLOG_PUBLISHED_DELETED_ERROR = new ErrorCode("BLOG_PUBLISHED_DELETED_ERROR", "The current article has been published and cannot be deleted.");

    /**
     * 获取Shopify OAuth授权链接失败
     */
    public static final ErrorCode GET_SHOPIFY_OAUTH_URL_ERROR = new ErrorCode("GET_SHOPIFY_OAUTH_URL_ERROR", "get shopify oauth url error.");

    /**
     * 翻译失败
     */
    public static final ErrorCode BLOG_TRANSLATE_ERROR = new ErrorCode("BLOG_TRANSLATE_ERROR", "Article translate error.");

    /**
     * 店铺未设置对应语种
     */
    public static final ErrorCode SHOP_NOT_SET_LANG_ERROR = new ErrorCode("SHOP_NOT_SET_LANG_ERROR", "The shop has not set up or published the corresponding language.");

    public static final ErrorCode URL_EXTRACT_ERROR = new ErrorCode("URL_EXTRACT_ERROR", "Oops, we failed to analyze the URL. Please recheck it.");

    public static final ErrorCode URL_EXTRACT_INFO_SHORT = new ErrorCode("URL_EXTRACT_INFO_SHORT", "The information retrieved from the URL is too limited. Please provide additional descriptive details to help us better generate content.");

    public static final ErrorCode PRODUCT_URL_PARSING_FAILED = new ErrorCode("PRODUCT_URL_PARSING_FAILED", "The link lacks sufficient information for keyword extraction.");

    /**
     * 输入参数不合法
     */
    public static final ErrorCode ILLEGAL_INPUT_PARAMETERS = new ErrorCode("ILLEGAL_INPUT_PARAMETERS", "Illegal input parameters");

    /**
     * 关键词
     */
    public static final ErrorCode KEYWORD_NO_FEATIURE_USAGE = new ErrorCode("KEYWORD_NO_FEATIURE_USAGE", "Today's benefits have been used up, come back tomorrow.");

    /**
     * 关键词清洗
     */
    public static final ErrorCode TEXT_CLEAN_ERROR = new ErrorCode("TEXT_CLEAN_ERROR", "Text clean error.");

    /**
     * blogCreator鉴权未通过
     */
    public static final ErrorCode BLOG_CREATOR_AUTH_FAILED = new ErrorCode("BLOG_CREATOR_AUTH_FAILED", "Blog Creator authentication failed.");
}
