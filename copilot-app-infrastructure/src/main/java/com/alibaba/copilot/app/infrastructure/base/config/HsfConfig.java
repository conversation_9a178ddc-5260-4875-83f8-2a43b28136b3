package com.alibaba.copilot.app.infrastructure.base.config;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.copilot.enabler.client.payment.facade.AepayHsfApi;
import com.alibaba.copilot.enabler.client.subscription.facade.ShopifyCallbackHsfApi;
import com.alibaba.copilot.enabler.client.subscription.facade.ShopifySubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.facade.SubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.facade.Text2GoSubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.service.UserInvitationService;
import com.alibaba.copilot.enabler.client.user.facade.UserAppRelationHsfApi;
import com.alibaba.copilot.enabler.client.user.facade.UserQueryHsfApi;
import com.alibaba.copilot.enabler.client.user.facade.UserRegisterHsfApi;
import com.alibaba.copilot.enabler.client.user.facade.UserTokenHsfApi;
import com.alibaba.copilot.light.house.client.facade.LightHouseFacade;
import com.alibaba.intl.addressserver.service.remote.interfaces.AddressRemoteService4Ocean;
import com.alibaba.security.tenant.common.service.RequestService;
import com.aliexpress.geoip.GeoipService;
import com.google.common.collect.Lists;
import com.taobao.hsf.app.spring.util.HSFSpringConsumerBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/9/6
 */
@Configuration
public class HsfConfig {
    private static final String HSF_VERSION_MTEE3 = "1.0.0_sg_aidc_content";

    @Value("${hsf.subscription.version}")
    private String subscriptionVersion;
    @Value("${ae.unit.tag}")
    private String aeUnitTag;
    @Value("${spring.hsf.version}")
    private String springHsfVersion;

    @HSFConsumer
    private UserQueryHsfApi userQueryHsfApi;
    @HSFConsumer
    private UserAppRelationHsfApi userAppRelationHsfApi;
    @HSFConsumer
    private UserRegisterHsfApi userRegisterHsfApi;
    @HSFConsumer
    private UserTokenHsfApi userTokenHsfApi;

    /**
     * 请求copilot-light-house服务，超时时间3分钟
     */
    @HSFConsumer(clientTimeout = 180000)
    private LightHouseFacade lightHouseFacade;
//    @HSFConsumer
//    private SubscriptionHsfApi subscriptionHsfApi;
//    @HSFConsumer
//    private AepayHsfApi aepayHsfApi;

    @Bean
    public HSFSpringConsumerBean subscriptionHsfApi() {
        HSFSpringConsumerBean consumer = new HSFSpringConsumerBean();
        consumer.setInterfaceClass(SubscriptionHsfApi.class);
        consumer.setGroup("HSF");
        consumer.setVersion(subscriptionVersion);
        consumer.setClientTimeout(10 * 1000);
//        consumer.setConfigserverCenter(Lists.newArrayList(subscriptionUnit));
        return consumer;
    }

    @Bean
    public HSFSpringConsumerBean alipaymentHsfApi() {
        HSFSpringConsumerBean consumer = new HSFSpringConsumerBean();
        consumer.setInterfaceClass(AepayHsfApi.class);
        consumer.setGroup("HSF");
        consumer.setVersion(subscriptionVersion);
        consumer.setClientTimeout(10 * 1000);
//        consumer.setConfigserverCenter(Lists.newArrayList(subscriptionUnit));
        return consumer;
    }

    @Bean
    public HSFSpringConsumerBean shopifySubscriptionHsfApi() {
        HSFSpringConsumerBean consumer = new HSFSpringConsumerBean();
        consumer.setInterfaceClass(ShopifySubscriptionHsfApi.class);
        consumer.setGroup("HSF");
        consumer.setVersion(subscriptionVersion);
//        consumer.setVersion("1.0.0.puke");
        consumer.setClientTimeout(10 * 1000);
        return consumer;
    }

    @Bean
    public HSFSpringConsumerBean shopifyCallbackHsfApi() {
        HSFSpringConsumerBean consumer = new HSFSpringConsumerBean();
        consumer.setInterfaceClass(ShopifyCallbackHsfApi.class);
        consumer.setGroup("HSF");
        consumer.setVersion(subscriptionVersion);
//        consumer.setVersion("1.0.0.puke");
        consumer.setClientTimeout(10 * 1000);
        return consumer;
    }

    @Bean(name = "requestServiceForMtee3")
    public HSFSpringConsumerBean requestServiceForMtee3() {
        HSFSpringConsumerBean consumer = new HSFSpringConsumerBean();
        consumer.setInterfaceClass(RequestService.class);
        consumer.setGroup("HSF");
        consumer.setVersion(HSF_VERSION_MTEE3);
        consumer.setConfigserverCenter(Lists.newArrayList(aeUnitTag));
        consumer.setClientTimeout(3000);
        return consumer;
    }

    @Bean(name = "addressRemoteService4Ocean")
    public HSFSpringConsumerBean addressRemoteService4Ocean() {
        HSFSpringConsumerBean consumer = new HSFSpringConsumerBean();
        consumer.setInterfaceClass(AddressRemoteService4Ocean.class);
        consumer.setGroup("HSF");
        consumer.setVersion(springHsfVersion);
        consumer.setConfigserverCenter(Lists.newArrayList(aeUnitTag));
        consumer.setClientTimeout(3000);
        return consumer;
    }

    @Bean
    public HSFSpringConsumerBean geoipService() {
        HSFSpringConsumerBean consumer = new HSFSpringConsumerBean();
        consumer.setInterfaceClass(GeoipService.class);
        consumer.setGroup("HSF");
        consumer.setClientTimeout(15000);
        consumer.setVersion("1.0.0");
        consumer.setConfigserverCenter(Lists.newArrayList(aeUnitTag));
        return consumer;
    }

    @Bean
    public HSFSpringConsumerBean text2GoSubscriptionHsfApi() {
        HSFSpringConsumerBean consumer = new HSFSpringConsumerBean();
        consumer.setInterfaceClass(Text2GoSubscriptionHsfApi.class);
        consumer.setGroup("HSF");
        consumer.setVersion(subscriptionVersion);
        consumer.setClientTimeout(10 * 1000);
        return consumer;
    }

    @Bean
    public HSFSpringConsumerBean userInvitationService() {
        HSFSpringConsumerBean consumer = new HSFSpringConsumerBean();
        consumer.setInterfaceClass(UserInvitationService.class);
        consumer.setGroup("HSF");
        consumer.setVersion(subscriptionVersion);
        consumer.setClientTimeout(10 * 1000);
        return consumer;
    }
}
