package com.alibaba.copilot.app.infrastructure.base.gateway.mq;

import com.alibaba.copilot.app.domain.base.gateway.mq.MessageHandler;
import com.alibaba.copilot.app.domain.n8n.model.FlowTriggerEvent;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/05/21
 */
@Component
public class FlowTriggerMessageConsumer extends BaseMessageQueueConsumer<FlowTriggerEvent> {

    @Value("${vpc_unit_name}")
    private String unitName;
    @Resource(name = "flowTriggerMessageHandler")
    private MessageHandler<FlowTriggerEvent> messageHandler;



    @Override
    public MessageHandler<FlowTriggerEvent> getMessageHandler() {
        return messageHandler;
    }

    @Override
    protected Class<FlowTriggerEvent> getEventClass() {
        return FlowTriggerEvent.class;
    }

    @Override
    protected String getConsumerId() {
        return "CID_n8n_flow_trigger";
    }

    @Override
    protected String getTopic() {
        return "aib_flow_trigger_topic";
    }

    @Override
    protected String getTags() {
        return "*";
    }

    @Override
    protected String getUnitName() {
        return unitName;
    }
}