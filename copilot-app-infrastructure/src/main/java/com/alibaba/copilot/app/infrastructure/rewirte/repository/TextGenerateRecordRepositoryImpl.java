package com.alibaba.copilot.app.infrastructure.rewirte.repository;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.domain.rewrite.entity.TextGenerateRecord;
import com.alibaba.copilot.app.domain.rewrite.repository.TextGenerateRecordRepository;
import com.alibaba.copilot.app.infrastructure.rewirte.dataobject.TextGenerateRecordDO;
import com.alibaba.copilot.app.infrastructure.rewirte.factory.TextGenerateRecordConverter;
import com.alibaba.copilot.app.infrastructure.rewirte.mapper.TextGenerateRecordMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TextGenerateRecordRepositoryImpl implements TextGenerateRecordRepository {

    @Resource
    private TextGenerateRecordMapper textGenerateRecordMapper;

    @Override
    public boolean save(TextGenerateRecord textGenerateRecord) {
        if (textGenerateRecord == null) {
            return false;
        }

        textGenerateRecord.setGmtModified(new Date());
        TextGenerateRecordDO textGenerateRecordDO = TextGenerateRecordConverter.INSTANCE.convertB2A(textGenerateRecord);

        if (textGenerateRecordDO.getId() == null) {
            textGenerateRecordDO.setGmtCreate(new Date());
            int inserted = textGenerateRecordMapper.insert(textGenerateRecordDO);
            textGenerateRecord.setId(textGenerateRecordDO.getId());
            textGenerateRecord.setGmtCreate(textGenerateRecordDO.getGmtCreate());
            return inserted == 1;
        } else {
            return textGenerateRecordMapper.updateById(textGenerateRecordDO) == 1;
        }
    }

    @Override
    public TextGenerateRecord getById(Long id) {
        if (id == null) {
            return null;
        }
        
        TextGenerateRecordDO textGenerateRecordDO = textGenerateRecordMapper.selectById(id);
        return TextGenerateRecordConverter.INSTANCE.convertA2B(textGenerateRecordDO);
    }

    @Override
    public PageWrapper<TextGenerateRecord> getByUserIdAndCodes(String userId, List<String> codes, Integer pageNum, Integer pageSize) {
        try {
            LambdaQueryWrapper<TextGenerateRecordDO> queryWrapper = new LambdaQueryWrapper<>();
            
            // 添加用户ID条件
            queryWrapper.eq(TextGenerateRecordDO::getUserId, userId);
            
            // 如果指定了类型代码，添加条件
            if (CollectionUtils.isNotEmpty(codes)) {
                queryWrapper.in(TextGenerateRecordDO::getType, codes);
            }
            
            // 按照ID倒序排序，避免使用gmtCreate可能带来的问题
            queryWrapper.orderByDesc(TextGenerateRecordDO::getId);
            
            // 执行分页查询
            Page<TextGenerateRecordDO> page = new Page<>(pageNum, pageSize);
            Page<TextGenerateRecordDO> resultPage = textGenerateRecordMapper.selectPage(page, queryWrapper);
            
            // 手动转换结果
            List<TextGenerateRecord> records = resultPage.getRecords().stream()
                    .map(TextGenerateRecordConverter.INSTANCE::convertA2B)
                    .collect(Collectors.toList());
            
            // 封装分页结果
            return new PageWrapper<TextGenerateRecord>()
                    .setList(records)
                    .setTotal(resultPage.getTotal());
        } catch (Exception e) {
            log.error("分页查询文本生成记录异常", e);
            // 发生异常时返回空结果
            return new PageWrapper<TextGenerateRecord>()
                    .setList(new ArrayList<>())
                    .setTotal(0L);
        }
    }
} 