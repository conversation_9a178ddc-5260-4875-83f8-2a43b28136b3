package com.alibaba.copilot.app.infrastructure.asynctask.repository;

import com.alibaba.copilot.app.domain.aynctask.model.AsyncTask;
import com.alibaba.copilot.app.domain.aynctask.model.AsyncTaskStatus;
import com.alibaba.copilot.app.domain.aynctask.repository.AsyncTaskRepository;
import com.alibaba.copilot.app.infrastructure.asynctask.dataobject.AsyncTaskDO;
import com.alibaba.copilot.app.infrastructure.asynctask.factory.AsyncTaskConverter;
import com.alibaba.copilot.app.infrastructure.asynctask.mapper.AsyncTaskMapper;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-06-27
 **/
@Slf4j
@Repository
public class AsyncTaskRepositoryImpl implements AsyncTaskRepository {

    @Value("${spring.profiles.active}")
    private String env;

    @Resource
    private AsyncTaskMapper asyncTaskMapper;

    /**
     * 创建任务
     *
     * @param task
     * @return
     */
    @Monitor(name = "创建任务", level = Monitor.Level.P0, layer = Monitor.Layer.REPOSITORY)
    @Override
    public void save(AsyncTask task) {
        Assertor.assertNull(task.getId(), "taskId is not null");
        task.setGmtCreate(new Date());
        task.setGmtModified(new Date());
        AsyncTaskDO taskDO = AsyncTaskConverter.INSTANCE.convertB2A(task);
        asyncTaskMapper.insert(taskDO);
        task.setId(taskDO.getId());
    }

    @Monitor(name = "获取任务", level = Monitor.Level.P0, layer = Monitor.Layer.REPOSITORY)
    @Override
    public AsyncTask getTask(Long taskId) {
        AsyncTaskDO taskDO = asyncTaskMapper.selectById(taskId);
        return AsyncTaskConverter.INSTANCE.convertA2B(taskDO);
    }

    /**
     * 获取店铺未完成任务
     *
     * @param shopId
     * @param taskType
     * @return
     */
    @Monitor(name = "获取店铺未完成任务", level = Monitor.Level.P0, layer = Monitor.Layer.REPOSITORY)
    @Override
    public List<AsyncTask> getNotFinishedTasks(Long shopId, String taskType) {
        List<String> executableStatus = Lists.newArrayList(AsyncTaskStatus.INITIAL.name(), AsyncTaskStatus.PENDING.name(), AsyncTaskStatus.RUNNING.name(), AsyncTaskStatus.SUSPEND.name());
        LambdaQueryWrapper<AsyncTaskDO> query = Wrappers.lambdaQuery();
        query.eq(AsyncTaskDO::getEnv, env);
        query.in(AsyncTaskDO::getStatus, executableStatus);
        query.eq(AsyncTaskDO::getShopId, shopId);
        query.eq(AsyncTaskDO::getType, taskType);
        List<AsyncTaskDO> taskDOS = asyncTaskMapper.selectList(query);

        return AsyncTaskConverter.INSTANCE.convertA2B(taskDOS);
    }

    @Monitor(name = "查询执行中的任务数量", level = Monitor.Level.P0, layer = Monitor.Layer.REPOSITORY)
    @Override
    public Long countRunningTask(String taskType) {
        Assertor.assertNotBlank(taskType, "taskType is blank");

        LambdaQueryWrapper<AsyncTaskDO> query = Wrappers.lambdaQuery();
        query.eq(AsyncTaskDO::getType, taskType)
                .eq(AsyncTaskDO::getStatus, AsyncTaskStatus.RUNNING.name());
        return asyncTaskMapper.selectCount(query);
    }

    /**
     * 根据uniqueKey获取任务
     *
     * @param uniqueKey
     * @return
     */
    @Monitor(name = "根据uniqueKey获取任务", level = Monitor.Level.P0, layer = Monitor.Layer.REPOSITORY)
    @Override
    public AsyncTask getTaskByUniqueKey(String uniqueKey) {
        LambdaQueryWrapper<AsyncTaskDO> query = Wrappers.lambdaQuery();
        query.eq(AsyncTaskDO::getUniqueKey, uniqueKey);
        AsyncTaskDO taskDO = asyncTaskMapper.selectOne(query);
        return AsyncTaskConverter.INSTANCE.convertA2B(taskDO);
    }

    /**
     * 根据outerId获取任务列表
     *
     * @param outerId
     * @param taskType
     * @return
     */
    @Monitor(name = "根据outerId获取任务列表", level = Monitor.Level.P0, layer = Monitor.Layer.REPOSITORY)
    @Override
    public List<AsyncTask> getTasksByOuterId(String outerId, String taskType) {
        if (StringUtils.isBlank(outerId)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<AsyncTaskDO> query = Wrappers.lambdaQuery();
        query.eq(AsyncTaskDO::getOuterId, outerId);
        query.eq(StringUtils.isNotBlank(taskType), AsyncTaskDO::getType, taskType);
        query.orderBy(true, false, AsyncTaskDO::getGmtCreate);
        List<AsyncTaskDO> taskDOS = asyncTaskMapper.selectList(query);
        return AsyncTaskConverter.INSTANCE.convertA2B(taskDOS);
    }

    /**
     * 获取当前可执行的任务
     *
     * @param size
     * @return
     */
    @Monitor(name = "获取可执行的任务", level = Monitor.Level.P0, layer = Monitor.Layer.REPOSITORY)
    @Override
    public List<AsyncTask> getExecutableTasks(Integer size) {
        List<String> executableStatus = Lists.newArrayList(AsyncTaskStatus.INITIAL.name(), AsyncTaskStatus.SUSPEND.name());
        LambdaQueryWrapper<AsyncTaskDO> query = Wrappers.lambdaQuery();
        query.eq(AsyncTaskDO::getEnv, env);
        query.in(AsyncTaskDO::getStatus, executableStatus);
        query.le(AsyncTaskDO::getStartExecuteDate, new Date());
        query.orderByAsc(AsyncTaskDO::getPriority);

        Page<AsyncTaskDO> queryPage = new Page<>(1, size);
        Page<AsyncTaskDO> selectResult = asyncTaskMapper.selectPage(queryPage, query);
        if (CollectionUtils.isEmpty(selectResult.getRecords())) {
            return new ArrayList<>();
        }
        return AsyncTaskConverter.INSTANCE.convertA2B(selectResult.getRecords());
    }

    @Monitor(name = "获取失去心跳的任务", level = Monitor.Level.P0, layer = Monitor.Layer.REPOSITORY)
    @Override
    public List<AsyncTask> getLoseHeartbeatTasks(Integer size) {
        // 20秒没有心跳
        Date activeDate = new Date(System.currentTimeMillis() - 15 * 1000);

        LambdaQueryWrapper<AsyncTaskDO> query = Wrappers.lambdaQuery();
        query.eq(AsyncTaskDO::getEnv, env);
        query.in(AsyncTaskDO::getStatus, Lists.newArrayList(
                AsyncTaskStatus.PENDING.name(),
                AsyncTaskStatus.RUNNING.name()
        ));
        query.le(AsyncTaskDO::getHeartbeatDate, activeDate);

        Page<AsyncTaskDO> queryPage = new Page<>(1, size);
        Page<AsyncTaskDO> selectResult = asyncTaskMapper.selectPage(queryPage, query);
        return AsyncTaskConverter.INSTANCE.convertA2B(selectResult.getRecords());
    }


    /**
     * 获取待重新的任务
     *
     * @param size
     * @return
     */
    @Monitor(name = "获取待重新的任务", level = Monitor.Level.P0, layer = Monitor.Layer.REPOSITORY)
    @Override
    public List<AsyncTask> getNeedRetryTasks(Integer size) {
        LambdaQueryWrapper<AsyncTaskDO> query = Wrappers.lambdaQuery();
        query.eq(AsyncTaskDO::getEnv, env);
        query.eq(AsyncTaskDO::getStatus, AsyncTaskStatus.FAILURE.name());
        query.gt(AsyncTaskDO::getExpireDate, new Date());
        query.apply("max_retry_times > execute_count - 1");

        Page<AsyncTaskDO> queryPage = new Page<>(1, size);
        Page<AsyncTaskDO> selectResult = asyncTaskMapper.selectPage(queryPage, query);
        return AsyncTaskConverter.INSTANCE.convertA2B(selectResult.getRecords());
    }

    /**
     * 更新任务心跳
     *
     * @param taskId
     * @return
     */
    @Monitor(name = "更新任务心跳", level = Monitor.Level.P0, layer = Monitor.Layer.REPOSITORY)
    @Override
    public boolean updateTaskHeartbeatDate(Long taskId) {
        LambdaUpdateWrapper<AsyncTaskDO> update = Wrappers.lambdaUpdate();
        update.eq(AsyncTaskDO::getId, taskId);
        update.in(AsyncTaskDO::getStatus, Lists.newArrayList(
                AsyncTaskStatus.PENDING.name(),
                AsyncTaskStatus.RUNNING.name()
        ));

        update.set(AsyncTaskDO::getGmtModified, new Date());
        update.set(AsyncTaskDO::getHeartbeatDate, new Date());
        return 1 == asyncTaskMapper.update(null, update);
    }

    /**
     * 更新任务状态
     *
     * @param task
     * @param newStatus
     * @return
     */
    @Monitor(name = "更新任务状态", level = Monitor.Level.P0, layer = Monitor.Layer.REPOSITORY)
    @Override
    public boolean updateTaskStatus(AsyncTask task, AsyncTaskStatus newStatus) {
        return updateTaskStatusAndResult(task, newStatus, null);
    }

    /**
     * 更新任务状态&结果
     *
     * @param task
     * @param newStatus
     * @return
     */
    @Monitor(name = "更新任务状态&结果", level = Monitor.Level.P0, layer = Monitor.Layer.REPOSITORY)
    @Override
    public boolean updateTaskStatusAndResult(AsyncTask task, AsyncTaskStatus newStatus) {
        return updateTaskStatusAndResult(task, newStatus, task.getResult());
    }

    private boolean updateTaskStatusAndResult(AsyncTask task, AsyncTaskStatus newStatus, String result) {
        LambdaUpdateWrapper<AsyncTaskDO> update = Wrappers.lambdaUpdate();
        update.eq(AsyncTaskDO::getId, task.getId());
        update.eq(AsyncTaskDO::getStatus, task.getStatus());

        Date now = new Date();
        Integer executeCount = task.getExecuteCount();
        Date nextExecuteDate = new Date(System.currentTimeMillis() + task.getRetryInterval());

        update.set(AsyncTaskDO::getGmtModified, now);
        update.set(AsyncTaskDO::getStatus, newStatus.name());
        update.set(AsyncTaskDO::getAttributes, task.getAttributes().toString());
        if (newStatus == AsyncTaskStatus.INITIAL) {
            executeCount = 0;
            update.set(AsyncTaskDO::getExecuteCount, executeCount);
        }
        if (newStatus == AsyncTaskStatus.PENDING) {
            update.set(AsyncTaskDO::getHeartbeatDate, now);
            update.set(AsyncTaskDO::getExecuteCount, executeCount);
        }
        if (newStatus == AsyncTaskStatus.RUNNING) {
            executeCount += 1;
            update.set(AsyncTaskDO::getHeartbeatDate, now);
            update.set(AsyncTaskDO::getExecuteCount, executeCount);
        }
        if (newStatus == AsyncTaskStatus.SUSPEND) {
            update.set(AsyncTaskDO::getNextExecuteDate, nextExecuteDate);
        }
        if (result != null) {
            if (newStatus == AsyncTaskStatus.SUCCESS || newStatus == AsyncTaskStatus.FAILURE) {
                update.set(AsyncTaskDO::getResult, result);
            }
        }

        int ret = asyncTaskMapper.update(null, update);
        if (ret == 1) {
            task.setGmtModified(now);
            task.setHeartbeatDate(now);
            task.setExecuteCount(executeCount);
            task.setNextExecuteDate(nextExecuteDate);
            task.setStatus(newStatus.name());
            return true;
        }

        return false;
    }

    /**
     * 更新任务扩展属性
     *
     * @param task
     */
    @Monitor(name = "更新任务扩展属性", level = Monitor.Level.P0, layer = Monitor.Layer.REPOSITORY)
    @Override
    public synchronized boolean updateAttributes(AsyncTask task) {
        LambdaUpdateWrapper<AsyncTaskDO> update = Wrappers.lambdaUpdate();
        update.eq(AsyncTaskDO::getId, task.getId());
        update.eq(AsyncTaskDO::getStatus, task.getStatus());

        Date now = new Date();
        update.set(AsyncTaskDO::getGmtModified, now);
        update.set(AsyncTaskDO::getAttributes, task.getAttributes().toString());

        int ret = asyncTaskMapper.update(null, update);
        if (ret == 1) {
            return true;
        }

        return false;
    }
}
