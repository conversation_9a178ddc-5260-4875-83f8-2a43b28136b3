package com.alibaba.copilot.app.infrastructure.aiIistmaster.repository;

import java.util.List;

import javax.annotation.Resource;

import com.alibaba.copilot.app.infrastructure.aiIistmaster.dataobject.UserPreferenceTemplateDO;
import com.alibaba.copilot.app.domain.aiIistmaster.repository.UserPreferenceTemplateRepository;
import com.alibaba.copilot.app.infrastructure.aiIistmaster.factory.UserPreferenceTemplateToDOConverter;
import com.alibaba.copilot.app.infrastructure.aiIistmaster.mapper.UserPreferenceTemplateMapper;
import com.alibaba.copilot.app.domain.aiIistmaster.model.UserPreferenceTemplate;

import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

/**
 * @ClassName PreferenceTemplateRepositoryImpl
 * <AUTHOR>
 * @Date 2023/8/17 19:43
 */
@Component
public class UserPreferenceTemplateRepositoryImpl implements UserPreferenceTemplateRepository {
    @Resource
    UserPreferenceTemplateMapper userPreferenceTemplateMapper;

    @Override
    @Monitor(name = "[UserPreferenceTemplate] 保存用户偏好模版", level = Monitor.Level.P0, layer = Monitor.Layer.REPOSITORY)
    public Boolean save(UserPreferenceTemplate preferenceTemplate) {
        UserPreferenceTemplateDO userPreferenceTemplateDO = UserPreferenceTemplateToDOConverter.toDataObject(preferenceTemplate);
        userPreferenceTemplateMapper.insert(userPreferenceTemplateDO);
        return Boolean.TRUE;
    }

    @Override
    @Monitor(name = "[UserPreferenceTemplate] 查询用户偏好模版", level = Monitor.Level.P0, layer = Monitor.Layer.REPOSITORY)
    public List<UserPreferenceTemplate> queryByCriteria(Long userId, String appSource, String templateType) {

        QueryWrapper<UserPreferenceTemplateDO> wrapper = new QueryWrapper<>();
        wrapper.eq("user_id", userId);
        wrapper.eq("app_source", appSource);
        wrapper.eq("template_type", templateType);
        List<UserPreferenceTemplateDO> userPreferenceTemplateDOList = userPreferenceTemplateMapper.selectList(wrapper);
        List<UserPreferenceTemplate> userPreferenceTemplateList= Lists.newArrayList();
        for (UserPreferenceTemplateDO userPreferenceTemplateDo:userPreferenceTemplateDOList) {
            UserPreferenceTemplate userPreferenceTemplate = UserPreferenceTemplateToDOConverter.toDomainEntity(userPreferenceTemplateDo);
            userPreferenceTemplateList.add(userPreferenceTemplate);
        }
        return userPreferenceTemplateList;
    }
}
