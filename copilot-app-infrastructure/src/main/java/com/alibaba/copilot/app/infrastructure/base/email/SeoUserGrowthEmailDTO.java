package com.alibaba.copilot.app.infrastructure.base.email;

import com.alibaba.copilot.enabler.client.email.annotation.EmailTemplate;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

@Data
@Accessors(chain = true)
@EmailTemplate(id = "100", name = "alpharank_seller_userpromote_v2")
@Slf4j
public class SeoUserGrowthEmailDTO {
    /**
     * 用于多模板发送的场景
     */
    private String id;
    private String name;


    /**
     * 邮箱名称
     */
    private String email;

    /**
     * 模板id
     */
    private String templateId = "100";

    /**
     * 邮件发送日期
     */
    private String sendDate;

    /**
     * 邮件发送时间点
     */
    private String sendTime;

    /**
     * 诊断报表的oss链接
     */
    private String pdfUrl;

    private Integer pcIssueCount;

    private Integer mobileIssueCount;

    private Integer allIssueCount;

    private BigDecimal pcScore;

    private BigDecimal mobileScore;

    public void setEmail(String email) {
        try {
            this.email = URLEncoder.encode(email, StandardCharsets.UTF_8.toString());
        } catch (Exception e) {
            log.error("email URLEncoder failed:", e);
        }
    }
//    public void setEmail(String email) {
//        try {
//            this.email = Base64.getEncoder().encodeToString(email.getBytes());
//        } catch (Exception e) {
//            log.error("email base64Encode failed:", e);
//        }
//    }

//    public String getEmail() {
//        try {
//            return new String(Base64.getDecoder().decode(this.email));
//        } catch (Exception e) {
//            log.error("email base64Decode failed:", e);
//            return "";
//        }
//    }
}
