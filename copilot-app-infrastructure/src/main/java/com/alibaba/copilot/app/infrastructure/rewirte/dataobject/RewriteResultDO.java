package com.alibaba.copilot.app.infrastructure.rewirte.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName RewriteResultDO
 * <AUTHOR>
 * @Date 2024/10/29 11:48
 */
@TableName(value = "rewrite_result")
public class RewriteResultDO implements Serializable {
    /**
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * Database Column Remarks:
     *   主键
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   任务ID
     *
     * @mbg.generated
     */
    private String taskId;

    /**
     * Database Column Remarks:
     *   用户 id (已登录用户)
     *
     * @mbg.generated
     */
    private String userId;

    /**
     * Database Column Remarks:
     *   匿名用户标识
     *
     * @mbg.generated
     */
    private String anonymousId;

    /**
     * Database Column Remarks:
     *   原始文本
     *
     * @mbg.generated
     */
    private String originalText;

    /**
     * Database Column Remarks:
     *   改写后的文本
     *
     * @mbg.generated
     */
    private String rewrittenText;

    /**
     * Database Column Remarks:
     *   原始文本检测结果
     *
     * @mbg.generated
     */
    private String originalDetectionResult;

    /**
     * Database Column Remarks:
     *   改写后文本检测结果
     *
     * @mbg.generated
     */
    private String rewrittenDetectionResult;

    /**
     * Database Column Remarks:
     *   控制选项
     *
     * @mbg.generated
     */
    private String controlOptions;

    /**
     * Database Column Remarks:
     *   专有名词列表
     *
     * @mbg.generated
     */
    private String properNounList;

    /**
     * Database Column Remarks:
     *   状态：0-待处理，1-处理中，2-成功，3-失败
     *
     * @mbg.generated
     */
    private Integer status;

    /**
     * atabase Column Remarks:
     * 改写用户行为类型
     */
    private Integer rewriteSatisfactionBehavior;

    @Getter
    @Setter
    private String attributes;

    /**
     * 来源
     */
    private String source;

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAnonymousId() {
        return anonymousId;
    }

    public void setAnonymousId(String anonymousId) {
        this.anonymousId = anonymousId;
    }

    public String getOriginalText() {
        return originalText;
    }

    public void setOriginalText(String originalText) {
        this.originalText = originalText;
    }

    public String getRewrittenText() {
        return rewrittenText;
    }

    public void setRewrittenText(String rewrittenText) {
        this.rewrittenText = rewrittenText;
    }

    public String getOriginalDetectionResult() {
        return originalDetectionResult;
    }

    public void setOriginalDetectionResult(String originalDetectionResult) {
        this.originalDetectionResult = originalDetectionResult;
    }

    public String getRewrittenDetectionResult() {
        return rewrittenDetectionResult;
    }

    public void setRewrittenDetectionResult(String rewrittenDetectionResult) {
        this.rewrittenDetectionResult = rewrittenDetectionResult;
    }

    public String getControlOptions() {
        return controlOptions;
    }

    public void setControlOptions(String controlOptions) {
        this.controlOptions = controlOptions;
    }

    public String getProperNounList() {
        return properNounList;
    }

    public void setProperNounList(String properNounList) {
        this.properNounList = properNounList;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getRewriteSatisfactionBehavior() {
        return rewriteSatisfactionBehavior;
    }

    public void setRewriteSatisfactionBehavior(Integer rewriteSatisfactionBehavior) {
        this.rewriteSatisfactionBehavior = rewriteSatisfactionBehavior;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", taskId=").append(taskId);
        sb.append(", userId=").append(userId);
        sb.append(", anonymousId=").append(anonymousId);
        sb.append(", originalText=").append(originalText);
        sb.append(", rewrittenText=").append(rewrittenText);
        sb.append(", originalDetectionResult=").append(originalDetectionResult);
        sb.append(", rewrittenDetectionResult=").append(rewrittenDetectionResult);
        sb.append(", controlOptions=").append(controlOptions);
        sb.append(", properNounList=").append(properNounList);
        sb.append(", status=").append(status);
        sb.append(", rewriteSatisfactionBehavior=").append(rewriteSatisfactionBehavior);
        sb.append("]");
        return sb.toString();
    }
}