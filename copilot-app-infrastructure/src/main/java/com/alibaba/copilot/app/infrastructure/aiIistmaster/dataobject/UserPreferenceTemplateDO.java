package com.alibaba.copilot.app.infrastructure.aiIistmaster.dataobject;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableName;

/**
 *
 * <AUTHOR>
 */
@TableName(value = "user_preference_template")
public class UserPreferenceTemplateDO implements Serializable {
    /**
     *
     * @mbg.generated
     */
    private static final long serialVersionUID = 1L;

    /**
     * Database Column Remarks:
     *   主键
     *
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     *
     * @mbg.generated
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     *
     * @mbg.generated
     */
    private Date gmtModified;

    /**
     * Database Column Remarks:
     *   用户id
     *
     *
     * @mbg.generated
     */
    private Long userId;

    /**
     * Database Column Remarks:
     *   应用程序源
     *
     *
     * @mbg.generated
     */
    private String appSource;

    /**
     * Database Column Remarks:
     *   模版类型
     *
     *
     * @mbg.generated
     */
    private String templateType;

    /**
     * Database Column Remarks:
     *   是否软删除
     *
     *
     * @mbg.generated
     */
    private Integer isDeleted;

    /**
     * Database Column Remarks:
     *   模版详情json
     *
     *
     * @mbg.generated
     */
    private String templateValue;

    /**
     *
     * @return the value of user_preference_template.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     *
     * @param id the value for user_preference_template.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     *
     * @return the value of user_preference_template.gmt_create
     *
     * @mbg.generated
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     *
     * @param gmtCreate the value for user_preference_template.gmt_create
     *
     * @mbg.generated
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     *
     * @return the value of user_preference_template.gmt_modified
     *
     * @mbg.generated
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     *
     * @param gmtModified the value for user_preference_template.gmt_modified
     *
     * @mbg.generated
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     *
     * @return the value of user_preference_template.user_id
     *
     * @mbg.generated
     */
    public Long getUserId() {
        return userId;
    }

    /**
     *
     * @param userId the value for user_preference_template.user_id
     *
     * @mbg.generated
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     *
     * @return the value of user_preference_template.app_source
     *
     * @mbg.generated
     */
    public String getAppSource() {
        return appSource;
    }

    /**
     *
     * @param appSource the value for user_preference_template.app_source
     *
     * @mbg.generated
     */
    public void setAppSource(String appSource) {
        this.appSource = appSource;
    }

    /**
     *
     * @return the value of user_preference_template.template_type
     *
     * @mbg.generated
     */
    public String getTemplateType() {
        return templateType;
    }

    /**
     *
     * @param templateType the value for user_preference_template.template_type
     *
     * @mbg.generated
     */
    public void setTemplateType(String templateType) {
        this.templateType = templateType;
    }

    /**
     *
     * @return the value of user_preference_template.is_deleted
     *
     * @mbg.generated
     */
    public Integer getIsDeleted() {
        return isDeleted;
    }

    /**
     *
     * @param isDeleted the value for user_preference_template.is_deleted
     *
     * @mbg.generated
     */
    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     *
     * @return the value of user_preference_template.template_value
     *
     * @mbg.generated
     */
    public String getTemplateValue() {
        return templateValue;
    }

    /**
     *
     * @param templateValue the value for user_preference_template.template_value
     *
     * @mbg.generated
     */
    public void setTemplateValue(String templateValue) {
        this.templateValue = templateValue;
    }

    /**
     * @return
     *
     * @mbg.generated
     */
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append(", id=").append(id);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtModified=").append(gmtModified);
        sb.append(", userId=").append(userId);
        sb.append(", appSource=").append(appSource);
        sb.append(", templateType=").append(templateType);
        sb.append(", isDeleted=").append(isDeleted);
        sb.append(", templateValue=").append(templateValue);
        sb.append("]");
        return sb.toString();
    }
}