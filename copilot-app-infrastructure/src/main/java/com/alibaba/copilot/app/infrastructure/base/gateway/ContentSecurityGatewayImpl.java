package com.alibaba.copilot.app.infrastructure.base.gateway;

import com.alibaba.copilot.app.domain.base.gateway.ContentSecurityGateway;
import com.alibaba.copilot.app.domain.base.request.security.CheckPictureRiskRequest;
import com.alibaba.copilot.app.domain.base.request.security.CheckTextRiskRequest;
import com.alibaba.copilot.app.infrastructure.base.switchs.ContentSecuritySwitch;
import com.alibaba.copilot.boot.basic.exception.ErrorCode;
import com.alibaba.copilot.boot.basic.result.Result;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.fastjson.JSON;
import com.alibaba.security.tenant.common.model.RiskResult;
import com.alibaba.security.tenant.common.service.RequestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import static com.alibaba.copilot.app.infrastructure.base.constant.ErrorCodes.CONTENT_SECURITY_FAIL;
import static com.alibaba.copilot.app.infrastructure.base.constant.ErrorCodes.CONTENT_SECURITY_RISK;

/**
 * @desc: 内容风控
 * @author: yixiao.cx
 * @create: 2023-12-06
 **/
@Slf4j
@Service
public class ContentSecurityGatewayImpl implements ContentSecurityGateway {
    private static final String BIZ_CODE = "AI_Business";
    private static final String MTEE_EVENT_CODE = "aigc_common_compliance_control";

    private static final String CONTENT_TYPE_TEXT = "1";
    private static final String CONTENT_TYPE_PICTURE = "2";
    private static final String CONTENT_TYPE_TEXT_AND_PICTURE = "1,2";


    @Resource
    private RequestService requestServiceForMtee3;


    @Override
    public Result checkEnTextRisk(String text, String outerId) {
        CheckTextRiskRequest request = new CheckTextRiskRequest();
        request.setText(text);
        request.setOuterID(outerId);
        request.setConversationId(outerId);
        request.setLanguage(CheckTextRiskRequest.LANGUAGE_EN);
        return checkTextRisk(request);
    }

    @Override
    @Monitor(name = "文本风控", level = Monitor.Level.P3, layer = Monitor.Layer.GATEWAY)
    public Result checkTextRisk(CheckTextRiskRequest request) {
        if(! ContentSecuritySwitch.needCheckText()) {
            return Result.buildSuccess();
        }

        try {
            Map<String, Object> context = buildMteeContext(request);
            log.info("checkTextRisk entry, request={}", JSON.toJSONString(context));
            RiskResult riskResult = (RiskResult) requestServiceForMtee3.request(MTEE_EVENT_CODE, context);
            log.info("chekTextRisk finish, riskResult={}", JSON.toJSONString(riskResult));
            if (!riskResult.getResult()) {
                return Result.buildSuccess();
            }

            ErrorCode errorCode = ContentSecuritySwitch.ignoreRisk ? CONTENT_SECURITY_FAIL : CONTENT_SECURITY_RISK;
            return Result.buildFailure(errorCode);
        } catch (Exception e) {
            log.error("chekTextRisk fail", e);
            return Result.buildFailure(CONTENT_SECURITY_FAIL);
        }
    }

    @Override
    @Monitor(name = "图片风控", level = Monitor.Level.P3, layer = Monitor.Layer.GATEWAY)
    public Result checkPictureRisk(CheckPictureRiskRequest request) {
        if(! ContentSecuritySwitch.needCheckPicture()) {
            return Result.buildSuccess();
        }

        try {
            Map<String, Object> context = buildMteeContext(request);
            log.info("checkPictureRisk entry, request={}", JSON.toJSONString(context));
            RiskResult riskResult = (RiskResult) requestServiceForMtee3.request(MTEE_EVENT_CODE, context);
            log.info("checkPictureRisk finish, riskResult={}", JSON.toJSONString(riskResult));
            if (!riskResult.getResult()) {
                return Result.buildSuccess();
            }

            ErrorCode errorCode = ContentSecuritySwitch.ignoreRisk ? CONTENT_SECURITY_FAIL : CONTENT_SECURITY_RISK;
            return Result.buildFailure(errorCode);
        } catch (Exception e) {
            log.error("checkPictureRisk fail", e);
            return Result.buildFailure(CONTENT_SECURITY_FAIL);
        }
    }

    private Map<String, Object> buildMteeContext(CheckTextRiskRequest request) {
        Map<String, Object> context = initMteeContext(request.getConversationId(), CONTENT_TYPE_TEXT);
        context.put("textContent", request.getText());
        context.put("textContentMd5", request.getTextMd5());
        context.put("language", request.getLanguage());
        return context;
    }

    private Map<String, Object> buildMteeContext(CheckPictureRiskRequest request) {
        Map<String, Object> context = initMteeContext("", CONTENT_TYPE_PICTURE);
        // 单张给
        context.put("picUrlList", request.getPictureUrls());
        //context.put("picUrlMd5List", "");
        return context;
    }

    /**
     * 初始化风控请求上下文
     * @return
     */
    private Map<String, Object> initMteeContext(String conversationId, String contentType) {
        Map<String, Object> context = new HashMap<>();
        context.put("bizCode", BIZ_CODE);
        context.put("infoId", UUID.randomUUID());
        context.put("conversationId", conversationId);

        context.put("whiteFlag", 1);
        context.put("site", "AIB");

        // 一级产品名称（业务场景区分）：AlphaRank
        context.put("sceneType", "AlphaRank");
        // 二级产品名称（内容发布者身份：比如商家，消费者，AIGC等）
        context.put("publishRole", "3");
        // 三级产品名称（内容发布类型：问，答），如果送审内容是问，就写1，如果送审内容是答，就写2；如果同时包含问答，就写1，2
        context.put("publishType", "2");
        // 四级产品名称（内容类型：文本，图片，视频）: 如果送审内容是文本，写1，如果是图片，写2，如果同时包含文本&图片，写1，2
        context.put("contentType", contentType);
        // 五级产品名称（内容所属域类型：）:ads
        context.put("domainType", "ads");
        return context;
    }

}
