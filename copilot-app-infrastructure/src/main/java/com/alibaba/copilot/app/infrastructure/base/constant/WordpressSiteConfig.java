package com.alibaba.copilot.app.infrastructure.base.constant;

import com.alibaba.copilot.app.client.seocopilot.dto.WordpressConfig;

import java.util.HashMap;
import java.util.Map;

public class WordpressSiteConfig {

    public static Map<String, WordpressConfig> userGrowthWordpressConfigMaps() {

        Map<String, WordpressConfig> userGrowthWordpressConfigMaps = new HashMap<>();

        // AE站点
        userGrowthWordpressConfigMaps.put("newmediastream24.com", getUserGrowthWordpressConfig("https://newmediastream24.com", "Basic QWxpY2U6VGs5NCBka3dCIExjYnEgREtCMSBzbnRvIG1RSWc="));
        userGrowthWordpressConfigMaps.put("revvieit.com", getUserGrowthWordpressConfig("https://revvieit.com", "Basic SGltbWVsOldtVlQgN1gzOCBLcXJPIGx6dHQgQnFZZCBjZDRr"));
        userGrowthWordpressConfigMaps.put("lwl-japan.com", getUserGrowthWordpressConfig("https://lwl-japan.com", "Basic QWxpY2U6ckhUdyB1M2YwIFVna24gZm9nQSBRdnlzIE9ia3Y="));
        userGrowthWordpressConfigMaps.put("pankyshop.com", getUserGrowthWordpressConfig("https://pankyshop.com", "Basic QWxpY2U6WHh5RCA0SEJvIGxTcVggQmFwZiBqZU1SIEs0UUc="));
        userGrowthWordpressConfigMaps.put("mididay.com", getUserGrowthWordpressConfig("https://mididay.com", "Basic Q2hhcmxvdHRlOmVMTkggVlczMyAxNUdCIEZIRHAgMlNhUSBhTmMw"));
        userGrowthWordpressConfigMaps.put("assovalincap.com", getUserGrowthWordpressConfig("https://assovalincap.com", "Basic QWxpY2U6aTh4MSAxbm8yIEMzOUYgTlZsbiBVNFdaIDdHbFQ="));
        userGrowthWordpressConfigMaps.put("lionsclubs-md333.com", getUserGrowthWordpressConfig("https://lionsclubs-md333.com", "Basic SGltbWVsOmF6b2ogYnB2ZiBQNFR0IFY3MXYgS0ZFVyAwS2hO"));
        userGrowthWordpressConfigMaps.put("rxbill8.com", getUserGrowthWordpressConfig("https://rxbill8.com", "Basic QWxpY2U6M2JtQSBjQTViIGR3MzcgQTMxeCBPaExjIGRDT0c="));
        userGrowthWordpressConfigMaps.put("amoelbarroco.com", getUserGrowthWordpressConfig("https://amoelbarroco.com", "Basic SGltbWVsOjhlRWIgTjg2YyBMWGpWIGRoU1cgSlpxcCBuaHM4"));
        userGrowthWordpressConfigMaps.put("dent-r.com", getUserGrowthWordpressConfig("https://dent-r.com", "Basic QWxpY2U6SXduOCBFTncxIEJzU0ggdU16NSBHUnNiIEgwOTE="));
        userGrowthWordpressConfigMaps.put("touchmedya.com", getUserGrowthWordpressConfig("https://touchmedya.com", "Basic QWxpY2U6N2pTSCA2ODNUIHh3RU4gbVlZRyBZV21SIDJRNk8="));
        userGrowthWordpressConfigMaps.put("mac-prague.com", getUserGrowthWordpressConfig("https://mac-prague.com", "Basic Q2hhcmxvdHRlOjRoOWkgNXh5diBLZDBPIDdZSlIgWXdOUCB5UVJR"));
        userGrowthWordpressConfigMaps.put("zaminfra.com", getUserGrowthWordpressConfig("https://zaminfra.com", "Basic Q2hhcmxvdHRlOkZjYUMgZVNuayB1MWtDIGYyZGMgdXRvcyB5Nkpm"));
        userGrowthWordpressConfigMaps.put("talkbate.com", getUserGrowthWordpressConfig("https://talkbate.com", "Basic QWxpY2U6ekJPOCBqSkRJIDJ3c3UgV2o2OCByR21HIFpDNUU="));
        userGrowthWordpressConfigMaps.put("electronicmonsoon.com", getUserGrowthWordpressConfig("https://electronicmonsoon.com", "Basic QWxpY2U6UjhETiBRV0RFIGVBSmQgaWFTMiA1aVdnIGluU2s="));
        userGrowthWordpressConfigMaps.put("vinderouge.com", getUserGrowthWordpressConfig("https://vinderouge.com", "Basic QWxpY2U6aWlKOCBkaGk2IHlsQkYgM0ZDZSBpOFBYIHFJTDc="));
        userGrowthWordpressConfigMaps.put("rediscoverharmony.com", getUserGrowthWordpressConfig("https://rediscoverharmony.com", "Basic SGltbWVsOm1LWEMgNVA3QSBndUdpIG0ydnkgZFpNRSBaVFN6"));
        userGrowthWordpressConfigMaps.put("stanleebook.com", getUserGrowthWordpressConfig("https://stanleebook.com", "Basic QWxpY2U6UzRtVyBGNEF6IEp6Q3ggdWE5WCBKcDhjIGxaNEg="));
        userGrowthWordpressConfigMaps.put("steveprestwich.com", getUserGrowthWordpressConfig("https://steveprestwich.com", "Basic QWxpY2U6OGl5aSBOY1RjIDBITEMgUHpZNiA5R3JJIEl5Zk0="));
        userGrowthWordpressConfigMaps.put("provence-scents.com", getUserGrowthWordpressConfig("https://provence-scents.com", "Basic SGltbWVsOmtXc04gVXlZQiBLRGc2IEZyQVogTUpIUiA4WFZh"));
        userGrowthWordpressConfigMaps.put("allresultplus.com", getUserGrowthWordpressConfig("https://allresultplus.com", "Basic SGltbWVsOlFKSXMgcXdnWSB4M3VjIG94MHEgbzJuSiBiNzh6"));
        userGrowthWordpressConfigMaps.put("scientificjournalservice.com", getUserGrowthWordpressConfig("https://scientificjournalservice.com", "Basic QWxpY2U6SUNwNSBsemtIIHZXWjggT2pQSCBHd0Y2IGZabVg="));
        userGrowthWordpressConfigMaps.put("remiflament-photographies.com", getUserGrowthWordpressConfig("https://remiflament-photographies.com", "Basic QWxpY2U6OVFXbCBVNmVLIFNpeDYgS0NyciBRV3l5IHF4Wlk="));
        userGrowthWordpressConfigMaps.put("uncafeconseo.com", getUserGrowthWordpressConfig("https://uncafeconseo.com", "Basic Q2hhcmxvdHRlOkc5NXIgZHJ3MyBOWml2IEdGVFggZ29paCBXUEsy"));
        userGrowthWordpressConfigMaps.put("uxdsaine.com", getUserGrowthWordpressConfig("https://uxdsaine.com", "Basic QWxpY2U6NXVpNSBkNlpOIEFoblkgVHZMRCBvcUszIDRkSko="));
        userGrowthWordpressConfigMaps.put("39antenna.com", getUserGrowthWordpressConfig("https://39antenna.com", "Basic QWxpY2U6RFMxMCByOW1IIFdXeVUgdGFTeCBNMUhmIDJHckY="));
        userGrowthWordpressConfigMaps.put("thailandflzx.com", getUserGrowthWordpressConfig("https://thailandflzx.com", "Basic QWxpY2U6dlJrWCA0dXlxIGVLTEIgeWdISiBpTVFiIFBjVWM="));
        userGrowthWordpressConfigMaps.put("blogfreshradio.com", getUserGrowthWordpressConfig("https://blogfreshradio.com", "Basic Q2hhcmxvdHRlOmU5UlggUjl5UyA5MEVKIEdSU2YgTDRZOSB2eTNw"));
        userGrowthWordpressConfigMaps.put("50levitra10.com", getUserGrowthWordpressConfig("https://50levitra10.com", "Basic QWxpY2U6TzV0USBSRzhVIHVoTVIgQk5NSCBIWjcxIFdoTE8="));
        userGrowthWordpressConfigMaps.put("soft-paradise.com", getUserGrowthWordpressConfig("https://soft-paradise.com", "Basic QWxpY2U6UkZRUCBEaUx0IENnOXcgOXVEZiBvVTBGIFQ1dEc="));
        // alpharank-官网
        userGrowthWordpressConfigMaps.put("wp.edgeshop.ai", getUserGrowthWordpressConfig("https://wp.edgeshop.ai", "Basic d29yZHByZXNzLWNoYW5namlhbmc6Y1JzQSBySWltIG5GdjMgNVpnSyBHWVljIEprY20="));
        // alpharank-用增站
        userGrowthWordpressConfigMaps.put("seoapp-google.com", getUserGrowthWordpressConfig("https://seoapp-google.com", "Basic ********************************************************************"));
        return userGrowthWordpressConfigMaps;
    }

    public static WordpressConfig getUserGrowthWordpressConfig(String host, String accessToken) {
        WordpressConfig wordpressConfig = new WordpressConfig();
        wordpressConfig.setHost(host);
        wordpressConfig.setAccessToken(accessToken);
        return wordpressConfig;
    }
}
