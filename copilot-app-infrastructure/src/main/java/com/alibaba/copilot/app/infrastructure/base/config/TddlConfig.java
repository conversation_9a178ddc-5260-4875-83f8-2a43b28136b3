package com.alibaba.copilot.app.infrastructure.base.config;

import javax.sql.DataSource;

import com.alibaba.boot.tddl.builder.datasource.TDataSourceBuilder;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import java.util.Date;

@Configuration
@MapperScan(value = {"com.alibaba.copilot.app.infrastructure.*.mapper"}, sqlSessionFactoryRef = "sqlSessionFactory")
public class TddlConfig {

    @Bean(name = "dataSource", initMethod = "init", destroyMethod = "destroy")
    @Primary
    public DataSource dataSource() {
        return TDataSourceBuilder.create().appName("COPILOT_APP_APP").dynamicRule(true).sharding(false).build();
    }


    @Bean(name = "sqlSessionFactory")
    @Primary
    public SqlSessionFactory sqlSessionFactory(DataSource dataSource
            , @Qualifier("mysqlMybatisPlusInterceptor") MybatisPlusInterceptor mybatisPlusInterceptor
            , @Value("classpath:mybatis/mybatis-config.xml") Resource configLocation
            , @Qualifier("mysqlGlobalConfig") GlobalConfig globalConfig
    ) throws Exception {
        MybatisSqlSessionFactoryBean sqlSessionFactoryBean = new MybatisSqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(dataSource);
        sqlSessionFactoryBean.setConfigLocation(configLocation);
        sqlSessionFactoryBean.setGlobalConfig(globalConfig);
        // 设置 mapper xml 路径
        sqlSessionFactoryBean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath*:mapper/*Mapper.xml"));
        Interceptor[] plugins = new Interceptor[]{mybatisPlusInterceptor};
        sqlSessionFactoryBean.setPlugins(plugins);
        return sqlSessionFactoryBean.getObject();
    }

    @Bean(name = "transactionManager")
    @Primary
    public PlatformTransactionManager transactionManager(@Qualifier("dataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean("mysqlGlobalConfig")
    public GlobalConfig globalConfig() {
        GlobalConfig globalConfig = new GlobalConfig();
        // 是否控制台 print mybatis-plus 的 LOGO
        globalConfig.setBanner(true);

        //注入自定义的自动填充处理逻辑
        globalConfig.setMetaObjectHandler(new TimeMetaObjectHandler());
        GlobalConfig.DbConfig dbConfig = new GlobalConfig.DbConfig();

        // 表名下划线命名默认true
        dbConfig.setTableUnderline(true);

        // id类型,默认为数据库自增，若为分表，则id可通过@TableId在DO中设置
        dbConfig.setIdType(IdType.AUTO);
        globalConfig.setDbConfig(dbConfig);
        return globalConfig;
    }

    /**
     * 分页插件
     */
    @Bean(name = "mysqlMybatisPlusInterceptor")
    public MybatisPlusInterceptor mysqlMybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }

    /**
     * 时间字段处理
     *
     * <AUTHOR>
     */
    static class TimeMetaObjectHandler implements MetaObjectHandler {

        @Override
        public void insertFill(MetaObject metaObject) {
            this.strictInsertFill(metaObject, "gmtCreate", Date.class, new Date());
            this.fillStrategy(metaObject, "gmtModified", new Date());
        }

        @Override
        public void updateFill(MetaObject metaObject) {
            this.fillStrategy(metaObject, "gmtModified", new Date());
        }
    }
}