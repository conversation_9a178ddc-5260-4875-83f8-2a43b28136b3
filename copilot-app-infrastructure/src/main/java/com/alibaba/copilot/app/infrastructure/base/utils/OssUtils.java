package com.alibaba.copilot.app.infrastructure.base.utils;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URL;
import java.util.Date;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/7/7
 */
@Slf4j
@Component
public class OssUtils {

    private static OSS oss;

    @Resource
    public void setOss(OSS oss) {
        OssUtils.oss = oss;
    }

    /**
     * 创建文件
     *
     * @param putObjectRequest
     * @return
     */
    public static PutObjectResult putObject(PutObjectRequest putObjectRequest) {
        return oss.putObject(putObjectRequest);
    }

    public static void deleteObject(String bucketName, String objectName) {
        oss.deleteObject(bucketName, objectName);
    }

    public static boolean isObjectExist(String bucketName, String objectName) {
        return oss.doesObjectExist(bucketName, objectName);
    }

    /**
     * 生成预签名的 url
     *
     * @param
     * @return
     */
    public static URL generatePresignedUrl(String bucketName, String objectName, Date expiration) {
        return oss.generatePresignedUrl(bucketName, objectName, expiration);
    }

    /**
     * 查询文件
     *
     * @param
     * @return
     */
    public static OSSObject getObject(String bucketName, String objectName) {
        return oss.getObject(bucketName, objectName);
    }
}
