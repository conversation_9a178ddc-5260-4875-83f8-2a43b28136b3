package com.alibaba.copilot.app.infrastructure.base.utils;

import com.alibaba.copilot.boot.basic.exception.BizException;
import com.github.rholder.retry.*;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

import static com.alibaba.copilot.app.infrastructure.base.constant.ErrorCodes.RETRY_ERROR;

@Slf4j
public class ExecuteUtils {

    public static <T> T runWithRetry(Callable<T> callable, int maxRetry) {
        Retryer<T> retryer = RetryerBuilder.<T>newBuilder()
                .retryIfException()
                .withWaitStrategy(WaitStrategies.fixedWait(3, TimeUnit.SECONDS))
                .withStopStrategy(StopStrategies.stopAfterAttempt(maxRetry))
                .withRetryListener(new RetryListener() {
                    @Override
                    public <V> void onRetry(Attempt<V> attempt) {
                        if (attempt.hasException()) {
                            log.error(String.format("Failed call retry time: %d; error: %s", attempt.getAttemptNumber(), attempt.getExceptionCause()));
                        }
                    }
                })
                .build();
        try {
            return retryer.call(callable);
        } catch (RetryException e) {
            log.error("ExecuteUtils runWithRetry exp={},cause={}", e.getMessage(), e.getCause().getMessage());
            throw new BizException(RETRY_ERROR, e.getCause().getMessage(), e.getCause().getMessage());
        } catch (Exception e) {
            log.error("ExecuteUtils runWithRetry exp={}", e.getMessage(), e);
            throw new BizException(RETRY_ERROR, RETRY_ERROR.getDisplayMessage() + e.getMessage());
        }
    }

    public static void main(String[] args) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        System.out.println(sdf.format(1701928310366L));
        System.out.println(sdf.format(1701928308643L));
    }

}
