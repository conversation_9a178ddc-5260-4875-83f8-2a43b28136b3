package com.alibaba.copilot.app.infrastructure.rewirte.factory;

import com.alibaba.copilot.app.domain.rewrite.entity.TextGenerateRecord;
import com.alibaba.copilot.app.infrastructure.rewirte.dataobject.TextGenerateRecordDO;
import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class TextGenerateRecordConverter implements Converter<TextGenerateRecordDO, TextGenerateRecord> {
    public static final TextGenerateRecordConverter INSTANCE = new TextGenerateRecordConverter();

    @Override
    public TextGenerateRecord convertA2B(TextGenerateRecordDO textGenerateRecordDO) {
        if (textGenerateRecordDO == null) {
            return null;
        }
        TextGenerateRecord textGenerateRecord = new TextGenerateRecord();
        BeanUtils.copyProperties(textGenerateRecordDO, textGenerateRecord);
        
        // 处理attributes字段
        if (StringUtils.isNotBlank(textGenerateRecordDO.getAttributes())) {
            try {
                TextGenerateRecord.Attributes attributes = JSON.parseObject(textGenerateRecordDO.getAttributes(), TextGenerateRecord.Attributes.class);
                textGenerateRecord.setAttributes(attributes);
            } catch (Exception e) {
                // 解析失败则创建新的attributes
                textGenerateRecord.setAttributes(new TextGenerateRecord.Attributes());
            }
        }
        
        return textGenerateRecord;
    }

    @Override
    public TextGenerateRecordDO convertB2A(TextGenerateRecord textGenerateRecord) {
        if (textGenerateRecord == null) {
            return null;
        }
        TextGenerateRecordDO textGenerateRecordDO = new TextGenerateRecordDO();
        BeanUtils.copyProperties(textGenerateRecord, textGenerateRecordDO);
        
        // 处理attributes字段
        if (textGenerateRecord.getAttributes() != null) {
            textGenerateRecordDO.setAttributes(JSON.toJSONString(textGenerateRecord.getAttributes()));
        }
        
        return textGenerateRecordDO;
    }
    
    /**
     * 批量转换从DO到实体
     *
     * @param doList DO列表
     * @return 实体列表
     */
    public List<TextGenerateRecord> convertA2B(List<TextGenerateRecordDO> doList) {
        if (doList == null) {
            return new ArrayList<>();
        }
        return doList.stream()
                .map(this::convertA2B)
                .collect(Collectors.toList());
    }
    
    /**
     * 批量转换从实体到DO
     *
     * @param entityList 实体列表
     * @return DO列表
     */
    public List<TextGenerateRecordDO> convertB2A(List<TextGenerateRecord> entityList) {
        if (entityList == null) {
            return new ArrayList<>();
        }
        return entityList.stream()
                .map(this::convertB2A)
                .collect(Collectors.toList());
    }
}
