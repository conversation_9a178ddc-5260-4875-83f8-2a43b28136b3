package com.alibaba.copilot.app.infrastructure.base.email;

import com.alibaba.copilot.app.domain.base.email.EmailGateway;
import com.alibaba.copilot.app.infrastructure.base.constant.EmailConstant;
import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.copilot.enabler.client.email.annotation.EmailTemplate;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.global.midplatform.constants.ChannelTopicEnum;
import com.alibaba.global.midplatform.domain.EmailMsgAttachmentDO;
import com.alibaba.global.midplatform.domain.GlobalMessage;
import com.alibaba.global.midplatform.domain.GlobalSendResult;
import com.alibaba.global.midplatform.exception.NotifyPushException;
import com.alibaba.global.midplatform.starter.MessageCenterPushClient;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 2023/10/16
 */
@Slf4j
@Component
public class EmailGatewayImpl implements EmailGateway {

    @Autowired
    private MessageCenterPushClient messageCenterPushClient;

    /**
     * 发送邮件
     *
     * @param emailInfoObj 邮件对象 (需要添加{@link EmailTemplate}注解)
     */
    @Override
    public void sendEmail(String email, Object emailInfoObj) {
        log.info("sendEmail, email={}, emailInfoObj={}", email, JSON.toJSONString(emailInfoObj));
        Assertor.assertNotBlank(email, "email is blank");
        Assertor.assertNonNull(emailInfoObj, "emailInfoObj is null");

        // 查询邮箱模板配置
        Type mapType = new TypeReference<Map<String, Object>>() {
        }.getType();
        Map<String, Object> extDataParam = JSON.parseObject(JSON.toJSONString(emailInfoObj), mapType);
        String templateId;
        String templateName;
        String templateInstanceId;
        if(extDataParam.containsKey("id") && extDataParam.containsKey("name")) {
            templateId = String.valueOf(extDataParam.get("id"));
            templateName = String.valueOf(extDataParam.get("name"));
            templateInstanceId = templateName;
            extDataParam.remove("id");
            extDataParam.remove("name");
        } else {

            Class<?> objType = emailInfoObj.getClass();
            Assertor.asserts(objType.isAnnotationPresent(EmailTemplate.class), "No @EmailTemplate configured for " + objType);
            EmailTemplate emailTemplate = objType.getAnnotation(EmailTemplate.class);
            templateId = emailTemplate.id();
            templateName = emailTemplate.name();
            templateInstanceId = Optional.of(emailTemplate.instanceId())
                    .filter(StringUtils::isNotEmpty)
                    .orElse(templateName);
        }



        // 构建请求信息

        GlobalMessage globalMessage = new GlobalMessage();
        globalMessage.setRequirementId(templateId);
        globalMessage.setTemplateName(templateName);
        globalMessage.setTemplateInstanceId(templateInstanceId);
        globalMessage.setSendToUser(Lists.newArrayList(email));
        globalMessage.setExtDataParam(extDataParam);

//        EmailMsgAttachmentDO attachmentDO = new EmailMsgAttachmentDO();
        // TODO: 2023/12/11 把附件塞到email里
//        globalMessage.setAttachments(Arrays.asList(attachmentDO));
        // 默认多租户环境和多租户应用，如果是非多租户应用使用下面第二个发送方法
        sendMessage(globalMessage);
    }

    /**
     * 指定目标租户ID，并将消息投递到该租户。适用于中心化、单元化、非多租户应用。
     * send message to target channel for app without landlord.
     *
     * @param message 标准消息模型 standard message domain model
     */
    private void sendMessage(GlobalMessage message) {
        try {
            log.info("sendMessage, message={}", JSON.toJSONString(message));
            GlobalSendResult<String> globalSendResult = messageCenterPushClient.sendMessageWithTenantId(
                    EmailConstant.TENANT_ID, message, ChannelTopicEnum.EMAIL);
            if (globalSendResult.isSuccess()) {
                // 代表消息投递成功，必须打印或记录这个messageId，否则出了问题消息域无法排查
                // it means the message push successfully, This message ID must be printed or recorded, it help us to check problem.
                String messageId = globalSendResult.getResult();
                log.info("sendMessage, send success, message={}, messageId={}", JSON.toJSONString(message), messageId);
            } else {
                // 代表消息投递失败，重试或者其他异常处理
                // it means the message push failed, retry or other exception handling
                log.error("sendMessage, send error, result={}", JSON.toJSONString(globalSendResult));
            }
        } catch (NotifyPushException e) {
            // 处理异常，记录日志
            log.error("sendMessage, invoke error", e);
        }
    }
}
