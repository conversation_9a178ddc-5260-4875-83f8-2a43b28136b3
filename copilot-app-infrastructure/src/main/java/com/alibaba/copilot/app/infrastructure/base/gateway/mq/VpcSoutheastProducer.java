package com.alibaba.copilot.app.infrastructure.base.gateway.mq;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/05/21
 */
@Component
public class VpcSoutheastProducer extends BaseMessageQueueProducer {

    @Value("${producer.group}")
    private String producerGroup;

    @Value("${copilot.boot.eventbus.messages[0].producerUnitName}")
    private String unitName;


    @Value("${copilot.boot.eventbus.messages[0].producerInstanceName}")
    private String instanceName;


    @Override
    protected String getProducerGroup() {
        return producerGroup;
    }

    @Override
    protected String getUnitName() {
        return unitName;
    }

    @Override
    protected String getInstanceName() {
        return instanceName;
    }

}