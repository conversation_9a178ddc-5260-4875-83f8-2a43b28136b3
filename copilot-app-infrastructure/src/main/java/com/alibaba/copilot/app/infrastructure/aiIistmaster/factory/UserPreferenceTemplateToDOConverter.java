package com.alibaba.copilot.app.infrastructure.aiIistmaster.factory;

import java.util.Objects;

import com.alibaba.copilot.app.infrastructure.aiIistmaster.dataobject.UserPreferenceTemplateDO;
import com.alibaba.copilot.app.domain.aiIistmaster.model.TextPreferenceTemplate;
import com.alibaba.copilot.app.domain.aiIistmaster.model.UserPreferenceTemplate;
import com.alibaba.fastjson.JSON;

/**
 * @ClassName UserPreferenceTemplateConverter
 * <AUTHOR>
 * @Date 2023/8/17 20:45
 */
public class UserPreferenceTemplateToDOConverter {

    public static UserPreferenceTemplateDO toDataObject(UserPreferenceTemplate entity) {
        UserPreferenceTemplateDO userPreferenceTemplateDO = new UserPreferenceTemplateDO();
        if (Objects.isNull(entity)){
            return userPreferenceTemplateDO;
        }
        userPreferenceTemplateDO.setId(entity.getId());
        userPreferenceTemplateDO.setUserId(entity.getUserId());
        userPreferenceTemplateDO.setAppSource(entity.getAppSource());
        userPreferenceTemplateDO.setTemplateType(entity.getTemplateType());
        String templateValueJson = JSON.toJSONString(entity.getTextPreferenceTemplate());
        userPreferenceTemplateDO.setTemplateValue(templateValueJson);
        return userPreferenceTemplateDO;
    }

    public static UserPreferenceTemplate toDomainEntity(UserPreferenceTemplateDO userPreferenceTemplateDO) {
        UserPreferenceTemplate entity = new UserPreferenceTemplate();
        if (Objects.isNull(userPreferenceTemplateDO)){
            return entity;
        }
        entity.setId(userPreferenceTemplateDO.getId());
        entity.setUserId(userPreferenceTemplateDO.getUserId());
        entity.setAppSource(userPreferenceTemplateDO.getAppSource());
        entity.setTemplateType(userPreferenceTemplateDO.getTemplateType());
        TextPreferenceTemplate template = JSON.parseObject(userPreferenceTemplateDO.getTemplateValue(),TextPreferenceTemplate.class);
        entity.setTextPreferenceTemplate(template);
        return entity;
    }
}
