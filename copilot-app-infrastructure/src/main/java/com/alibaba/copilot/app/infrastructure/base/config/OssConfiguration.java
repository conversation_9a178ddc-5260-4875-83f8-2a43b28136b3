package com.alibaba.copilot.app.infrastructure.base.config;

import com.alibaba.normandy.credential.Credential;
import com.alibaba.normandy.credential.CredentialProvider;
import com.alibaba.normandy.credential.ResourceNames;
import com.alibaba.normandy.credential.param.ResourceConfigHelper;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 2023/6/25
 */
@Configuration
public class OssConfiguration {
    @Value("${algorithm_image_oss_service_bucket_name}")
    private String bucketName = "t-selection-algorithms-image";

    @Value("${algorithm_image_oss_service_endpoint}")
    private String endpoint = "oss-ap-southeast-1.aliyuncs.com";

    @Autowired
    private CredentialProvider credentialProvider;
    @Bean

    public OSS oss() {
        String rn = ResourceNames.ofAliyunOssBucketName(bucketName);
        Credential credential = credentialProvider.getCredential(rn);
        return new OSSClientBuilder().build(
                endpoint, credential.getAccessKeyId(), credential.getAccessKeySecret()
        );
    }
}
