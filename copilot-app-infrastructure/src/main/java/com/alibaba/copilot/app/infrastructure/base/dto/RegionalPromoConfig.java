package com.alibaba.copilot.app.infrastructure.base.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 地区优惠码配置
 * 用于配置不同地区对应的优惠码和优惠信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RegionalPromoConfig {
    
    /**
     * Stripe 优惠码 ID
     */
    private String discountCode;
    
    /**
     * 优惠描述（如 "70% OFF"）
     */
    private String discountDescription;
    
    /**
     * 优惠百分比
     */
    private Integer discountPercentage;
    
    /**
     * 横幅文本
     */
    private String bannerText;
    
    /**
     * 优惠名称
     */
    private String name;
    
    /**
     * 简化构造函数，只需要优惠码和描述
     */
    public RegionalPromoConfig(String discountCode, String discountDescription) {
        this.discountCode = discountCode;
        this.discountDescription = discountDescription;
    }
    
    /**
     * 构造函数，包含优惠码、描述和百分比
     */
    public RegionalPromoConfig(String discountCode, String discountDescription, Integer discountPercentage) {
        this.discountCode = discountCode;
        this.discountDescription = discountDescription;
        this.discountPercentage = discountPercentage;
    }
}
