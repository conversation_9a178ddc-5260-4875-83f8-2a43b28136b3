package com.alibaba.copilot.app.infrastructure.base.utils;

import java.util.List;
import java.util.Objects;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/05/21
 */
public class ObjectUtils {


    public static void notNull(Object object, String message) {
        if (object == null) {
            throw new IllegalArgumentException(message);
        }
    }

    public static void notBlank(String str, String message) {
        if (Objects.isNull(str) || str.trim().isEmpty()) {
            throw new IllegalArgumentException(message);
        }
    }

    public static void notEmpty(List<?> list, String message) {
        if (null == list || list.isEmpty()) {
            throw new IllegalArgumentException(message);
        }
    }

    public static void checkTrue(boolean expression, String errorMessage) {
        if (!expression) {
            throw new IllegalArgumentException(errorMessage);
        }
    }

}