package com.alibaba.copilot.app.infrastructure.base.email;

import com.alibaba.copilot.enabler.client.email.annotation.EmailTemplate;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EmailTemplate(id = "101", name = "alpharank_seller_userpromote_report")
public class SeoUserGrowthEmailReportDTO {
    /**
     * 诊断报告链接
     */
    private String pdfUrl;

    /**
     * 邮箱名称
     */
    private String email;
}
