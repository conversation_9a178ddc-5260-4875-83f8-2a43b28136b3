package com.alibaba.copilot.app.infrastructure.base.switchs;

import com.taobao.csp.switchcenter.annotation.AppSwitch;
import com.taobao.csp.switchcenter.bean.Switch;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-12-07
 **/
public class ContentSecuritySwitch {

    @AppSwitch(des = "是否启动内容安全检查(全局开关)", level = Switch.Level.p3)
    public static Boolean enable = false;

    @AppSwitch(des = "是否启动文本安全检查(全局开关)", level = Switch.Level.p3)
    public static Boolean enableText = true;

    @AppSwitch(des = "是否启动图片安全检查(全局开关)", level = Switch.Level.p3)
    public static Boolean enablePicture = true;

    @AppSwitch(des = "是否忽略风控(全局开关)", level = Switch.Level.p3)
    public static Boolean ignoreRisk = true;

    /**
     * 是否检查文本安全
     * @return
     */
    public static boolean needCheckText() {
        return enable && enableText;
    }

    /**
     * 是否检查图片安全
     * @return
     */
    public static boolean needCheckPicture() {
        return enable && enablePicture;
    }
}
