package com.alibaba.copilot.app.infrastructure.rewirte.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("text_generate_record")
public class TextGenerateRecordDO {
    private Long id;
    private Date gmtCreate;
    private Date gmtModified;
    private String userId;
    private String type;
    private String input;
    private String output;
    private Long promptTemplateId;
    // todo huangmeng 记得恢复
    private String attributes;
}
