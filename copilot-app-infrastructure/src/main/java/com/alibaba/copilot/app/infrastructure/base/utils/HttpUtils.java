package com.alibaba.copilot.app.infrastructure.base.utils;

import org.apache.commons.collections.MapUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.net.URI;
import java.util.Map;

public class HttpUtils {

    private static RestTemplate restTemplate = new RestTemplate();

    /**
     * GET请求
     *
     * @param url
     * @param headers
     * @param params
     * @param responseType
     * @param <T>
     * @return
     */
    public static <T> T get(String url, Map<String, String> headers, Map<String, String> params, Class<T> responseType) {
        HttpHeaders httpHeaders = new HttpHeaders();

        if (MapUtils.isNotEmpty(headers)) {
            headers.forEach(httpHeaders::add);
        }

        UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.fromHttpUrl(url);
        params.forEach(uriComponentsBuilder::queryParam);

        HttpEntity<?> httpEntity = new HttpEntity<>(params, httpHeaders);

        ResponseEntity<T> responseEntity = restTemplate.exchange(uriComponentsBuilder.toUriString(), HttpMethod.GET, httpEntity, responseType);
        return responseEntity.getBody();
    }

    /**
     * POST请求
     *
     * @param uri
     * @param headers
     * @param jsonBody
     * @param responseType
     * @param <T>
     * @return
     */
    public static <T> T post(URI uri, Map<String, String> headers, String jsonBody, Class<T> responseType) {

        HttpHeaders httpHeaders = new HttpHeaders();

        if (MapUtils.isNotEmpty(headers)) {
            headers.forEach(httpHeaders::add);
        }

        HttpEntity<?> httpEntity = new HttpEntity<>(jsonBody, httpHeaders);

        ResponseEntity<T> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, httpEntity, responseType);
        return responseEntity.getBody();
    }

    /**
     * PUT请求
     *
     * @param uri
     * @param headers
     * @param jsonBody
     * @param responseType
     * @param <T>
     * @return
     */
    public static <T> T put(URI uri, Map<String, String> headers, String jsonBody, Class<T> responseType) {

        HttpHeaders httpHeaders = new HttpHeaders();

        if (MapUtils.isNotEmpty(headers)) {
            headers.forEach(httpHeaders::add);
        }

        HttpEntity<?> httpEntity = new HttpEntity<>(jsonBody, httpHeaders);

        ResponseEntity<T> responseEntity = restTemplate.exchange(uri, HttpMethod.PUT, httpEntity, responseType);
        return responseEntity.getBody();
    }
}
