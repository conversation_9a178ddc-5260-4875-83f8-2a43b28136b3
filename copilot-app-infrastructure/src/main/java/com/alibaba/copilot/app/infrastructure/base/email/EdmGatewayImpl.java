package com.alibaba.copilot.app.infrastructure.base.email;

import com.alibaba.copilot.app.domain.base.email.EdmGateway;
import com.alibaba.copilot.app.domain.base.model.EdmRequest;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class EdmGatewayImpl implements EdmGateway {
    private static final RestTemplate restTemplate;

    static {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(50000);
        factory.setReadTimeout(100000);
        restTemplate = new RestTemplate(factory);
    }


    public void sendEmail(Long id, String fromEmail, String toEmail, Object params) {
        EdmRequest edmRequest = new EdmRequest()
                .setToEmail(toEmail)
                .setFromEmail(fromEmail)
                .setId(id)
                .setParams(params);
        URI uri = URI.create("https://copilot-edm.alibaba-inc.com/edm/triggerEdmActivityDynamic");
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        post(
                uri,
                headers,
                JSON.toJSONString(edmRequest),
                Object.class
        );
    }

    public void sendEmail(EdmRequest request) {
        URI uri = URI.create("https://copilot-edm.alibaba-inc.com/edm/triggerEdmActivityDynamic");
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        post(
                uri,
                headers,
                JSON.toJSONString(request),
                Object.class
        );
    }

    private static <T> T post(URI uri, Map<String, String> headers, String jsonBody, Class<T> responseType) {

        HttpHeaders httpHeaders = new HttpHeaders();

        if (MapUtils.isNotEmpty(headers)) {
            headers.forEach(httpHeaders::add);
        }

        HttpEntity<?> httpEntity = new HttpEntity<>(jsonBody, httpHeaders);

        ResponseEntity<T> responseEntity = restTemplate.exchange(uri, HttpMethod.POST, httpEntity, responseType);
        return responseEntity.getBody();
    }
}
