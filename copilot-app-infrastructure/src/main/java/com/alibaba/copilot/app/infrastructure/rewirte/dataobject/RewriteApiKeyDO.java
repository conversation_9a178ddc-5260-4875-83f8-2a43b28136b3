package com.alibaba.copilot.app.infrastructure.rewirte.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "rewrite_api_key")
public class RewriteApiKeyDO implements Serializable {
    private Long id;
    /**
     * Database Column Remarks:
     *   创建时间
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     */
    private Date gmtModified;
    private String apiKey;
    private String userId;
    private Boolean deleted;
}
