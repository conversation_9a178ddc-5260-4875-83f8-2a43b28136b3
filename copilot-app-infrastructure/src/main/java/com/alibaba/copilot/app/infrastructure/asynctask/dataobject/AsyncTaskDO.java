package com.alibaba.copilot.app.infrastructure.asynctask.dataobject;

import com.alibaba.copilot.boot.basic.data.BaseDbObject;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-09-28
 **/
@Data
@TableName(value = "async_task")
public class AsyncTaskDO extends BaseDbObject {

    /**
     * 所属环境
     */
    private String env;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * seo_shop_id
     */
    private Long shopId;

    /**
     * 应用程序源
     */
    private String appSource;

    /**
     * 外部ID
     */
    private String outerId;

    /**
     * 任务Key
     */
    private String uniqueKey;

    /**
     * 任务类型
     */
    private String type;

    /**
     * 任务优先级
     */
    private Integer priority;

    /**
     * 重试间隔(毫秒)
     */
    private Integer retryInterval;

    /**
     * 执行次数
     */
    private Integer executeCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryTimes;

    /**
     * 开始执行时间
     */
    private Date startExecuteDate;

    /**
     * 下次执行时间
     */
    private Date nextExecuteDate;

    /**
     * 过期时间
     */
    private Date expireDate;

    /**
     * 心跳时间
     */
    private Date heartbeatDate;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 已读状态
     */
    private String readStatus;

    /**
     * 任务请求
     */
    private String request;

    /**
     * 任务结果
     */
    private String result;

    /**
     * 扩展属性
     */
    private String attributes;
}
