package com.alibaba.copilot.app.infrastructure.base.repository;

import com.alibaba.copilot.app.domain.base.repository.PdfOssClient;
import com.alibaba.copilot.app.infrastructure.base.utils.OssUtils;
import com.aliyun.oss.model.PutObjectRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;

@Slf4j
@Component("PdfOssService")
public class PdfOssServiceImpl extends OssAbstractService implements PdfOssClient {
    @Value("${algorithm_image_oss_service_bucket_name}")
    private String bucketName = "t-selection-algorithms-image";

    @Override
    public String savePdfContent(String filePath, ByteArrayOutputStream pdfContentStream) {
        try {
            // 创建一个新的输入流
            ByteArrayInputStream inputStream = new ByteArrayInputStream(pdfContentStream.toByteArray());

            // 创建PutObjectRequest对象
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, filePath, inputStream);
            OssUtils.putObject(putObjectRequest);

            // 返回OSS文件的链接 https://t-selection-algorithms-image.oss-ap-southeast-1.aliyuncs.com/seo-trial-diagnose-report.pdf
            return "https://" + bucketName + "." + "oss-ap-southeast-1.aliyuncs.com" + "/" + filePath;
        } catch (Exception e) {
            log.error("Error while uploading PDF to OSS", e);
            return null;
        }
    }
}
