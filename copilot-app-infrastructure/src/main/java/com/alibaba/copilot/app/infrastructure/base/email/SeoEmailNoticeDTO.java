package com.alibaba.copilot.app.infrastructure.base.email;

import com.alibaba.copilot.enabler.client.email.annotation.EmailTemplate;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> @version 2023/10/22
 */
@Data
@Accessors(chain = true)
@EmailTemplate(id = "76", name = "alpharank_seller_alpharank")
public class SeoEmailNoticeDTO {

    /**
     * 安装天数
     */
    private String installDays;

    /**
     * 优化问题总数
     */
    private String succItemdCount;

    /************SEO SCORE*********/

    /**
     * SEO Score-PC分数
     */
    private String pcScore;

    /**
     * PC SEO Score-相比第一次提升
     */
    private String pcImproved;

    /**
     * SEO Score-Mobile分数
     */
    private String mobileScore;

    /**
     * Mobile SEO Score-相比第一次提升
     */
    private String mobileImproved;

    /************SEO INDEX*********/

    /**
     * Shop index(%)
     */
    private String index;

    /**
     * SEO Index-相比第一次提升
     */
    private String indexImproved;

    /**
     * pages 总数
     */
    private String pageCount;

    /**
     * 收录 pages 总数
     */
    private String indexPageCount;


    /************SEO RANK*********/

    /**
     * Keyword
     */
    private String keyword;

    /**
     * Keyword rank
     */
    private String rank;

    /**
     * SEO Rank-相比第一次提升
     */
    private String rankImproved;

    /**
     * clicks
     */
    private String clicks;

    /**
     * ctr
     */
    private String ctr;

    /**
     * position
     */
    private String position;

    /**
     * impressions
     */
    private String impressions;


    /**
     * 成功优化
     */
    private List<String> succOptimizeRes;

    /**
     * 手工优化
     */
    private List<String> manualOptimizeRes;

    /**
     * 后台url
     */
    private String url;

}
