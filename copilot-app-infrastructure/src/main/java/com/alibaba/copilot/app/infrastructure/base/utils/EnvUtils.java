package com.alibaba.copilot.app.infrastructure.base.utils;

import com.alibaba.copilot.app.infrastructure.base.switchs.SwitchConfig;

/**
 * 环境配置
 */
public class EnvUtils {

    public static final String DAILY = "daily";
    public static final String PRE = "pre";
    public static final String ONLINE = "online";

    public static boolean isDaily() {
        return DAILY.equals(SwitchConfig.env);
    }

    public static boolean isPre() {
        return PRE.equals(SwitchConfig.env);
    }

    public static boolean isOnline() {
        return ONLINE.equals(SwitchConfig.env);
    }

    public static String getEnv() {
        return SwitchConfig.env;
    }
}
