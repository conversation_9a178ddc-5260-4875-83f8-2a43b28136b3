package com.alibaba.copilot.app.infrastructure.base.repository;

import com.alibaba.copilot.app.domain.base.constant.FileExtEnum;
import com.alibaba.copilot.app.infrastructure.base.utils.OssUtils;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.PutObjectRequest;
import com.aliyun.oss.model.PutObjectResult;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 抽象 OSS 基础能力
 * @email <EMAIL>
 * @date 2023/7/7
 */
@Data
@Slf4j
public abstract class OssAbstractService {
    /**
     * OSS链接的默认保存时长
     */
    protected final Duration DEFAULT_SAVED_DURATION = Duration.ofDays(365);

    @Value("${algorithm_image_oss_service_bucket_name}")
    private String bucketName = "t-selection-algorithms-image";

    /**
     * 以字节数组形式上传文件
     *
     * @param fileUri  文件 URI
     * @param fileName 文件名称
     * @param bytes    字节数组
     * @param fileExt  文件扩展名
     * @return 资源路径
     */
    protected String putObject(String fileUri, String fileName, byte[] bytes, FileExtEnum fileExt) {
        ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
        return this.putObject(fileUri, fileName, inputStream, fileExt);
    }

    /**
     * 以流形式上传文件
     *
     * @param fileUri     文件 URI
     * @param fileName    文件名称
     * @param inputStream 流
     * @param fileExt     文件扩展名
     * @return 资源路径
     */
    protected String putObject(String fileUri, String fileName, InputStream inputStream, FileExtEnum fileExt) {
        return this.putObject(fileUri, fileName, inputStream, fileExt, DEFAULT_SAVED_DURATION);
    }

    /**
     * 以流形式上传文件
     *
     * @param fileUri      文件 URI
     * @param fileName     文件名称
     * @param inputStream  流
     * @param fileExt      文件扩展名
     * @param saveDuration 保存时间
     * @return
     */
    protected String putObject(String fileUri, String fileName, InputStream inputStream, FileExtEnum fileExt, Duration saveDuration) {
        String uri = "";
        String objectName = "";
        try {
            objectName = genOssFileOfExtName(fileUri, fileName, fileExt);
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectName, inputStream);
            PutObjectResult putObjectResult = OssUtils.putObject(putObjectRequest);
            if (putObjectResult != null) {
                // TODO 后续补充图片失效策略
                Date expiration = new Date(System.currentTimeMillis() + saveDuration.toMillis());
                uri = OssUtils.generatePresignedUrl(bucketName, objectName, expiration).toString();
                log.info("AlgorithmImageOssService put object uri: {}", uri);
            }
        } catch (OSSException e) {
            log.error("OssAbstractService#putObject error, bucketName is {}, objectName is {}", bucketName, objectName, e);
            throw new RuntimeException(e);
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return uri.replace("http", "https");
    }

    public void deleteObject(String objectName) {
        OssUtils.deleteObject(bucketName, objectName);
    }

    /**
     * 查询文件
     *
     * @param objectName 目标文件 key
     * @return
     */
    protected Object getObject(String objectName) {
        try {
            OSSObject ossObject = OssUtils.getObject(bucketName, objectName);
            InputStream inputStream = ossObject.getObjectContent();
            // 读取输入流并转为字符串
            BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
            StringBuilder stringBuilder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }
            String fileContent = stringBuilder.toString();
            // 关闭输入流
            inputStream.close();
            return fileContent;
        } catch (OSSException | IOException e) {
            log.error("OssAbstractService#getObject error, bucketName is {}, objectName is {}", bucketName, objectName, e);
            throw new RuntimeException(e);
        }
    }

    /**
     * 生成文件名称
     *
     * @param fileUri
     * @param fileName
     * @return
     */
    protected String genOssFileOfExtName(String fileUri, String fileName, FileExtEnum fileExt) {
        if (StringUtils.isAnyBlank(fileUri, fileName) || fileExt == null) {
            throw new RuntimeException("genOssObjectName error, the fileUri or fileName not specified");
        }
        return fileUri + fileName + fileExt.getExtStr();
    }
}
