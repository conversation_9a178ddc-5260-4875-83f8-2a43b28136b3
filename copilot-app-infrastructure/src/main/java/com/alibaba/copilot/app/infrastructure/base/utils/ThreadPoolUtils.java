package com.alibaba.copilot.app.infrastructure.base.utils;

import com.alibaba.copilot.boot.tools.thread.ThreadTracePoolExecutor;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.Getter;

import java.util.concurrent.*;

/**
 * 线程池utils
 */
@Getter
public class ThreadPoolUtils {

    private static ThreadPoolUtils instance;

    /**
     * processor专用线程池
     * （所有XXXProcessor类请使用该线程池）
     */
    private final ThreadPoolExecutor processorExecutor;

    /**
     * 通用线程池
     * （除了XXXProcessor类外均可使用）
     */
    private final ThreadPoolExecutor bizExecutor;

    private ThreadPoolUtils() {
        // processor专用线程池
        processorExecutor = new ThreadTracePoolExecutor(30, 50, 60,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("processor-%s").build(), new ThreadPoolExecutor.CallerRunsPolicy());

        // 通用线程池
        bizExecutor = new ThreadTracePoolExecutor(30, 50, 60,
                TimeUnit.SECONDS, new ArrayBlockingQueue<>(50),
                new ThreadFactoryBuilder().setNameFormat("biz-%s").build(), new ThreadPoolExecutor.CallerRunsPolicy());
    }

    public static synchronized ThreadPoolUtils getInstance() {
        if (instance == null) {
            instance = new ThreadPoolUtils();
        }
        return instance;
    }
}