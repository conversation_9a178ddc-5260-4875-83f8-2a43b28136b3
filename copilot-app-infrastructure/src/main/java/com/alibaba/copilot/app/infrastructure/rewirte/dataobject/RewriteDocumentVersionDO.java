package com.alibaba.copilot.app.infrastructure.rewirte.dataobject;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "rewrite_document_version")
public class RewriteDocumentVersionDO implements Serializable {
    /**
     * Database Column Remarks:
     *   主键
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     */
    private Date gmtCreate;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     */
    private Date gmtModified;

    private String content;

    private Long documentId;

    private String outerUserId;
    
    /**
     * Database Column Remarks:
     *   输入参数，JSON格式
     */
    private String input;
}
