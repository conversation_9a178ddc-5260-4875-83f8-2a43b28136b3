package com.alibaba.copilot.app.infrastructure.asynctask.factory;

import com.alibaba.copilot.app.domain.aynctask.model.AsyncTask;
import com.alibaba.copilot.app.domain.aynctask.model.AsyncTaskAttributes;
import com.alibaba.copilot.app.infrastructure.asynctask.dataobject.AsyncTaskDO;
import com.alibaba.copilot.boot.basic.data.Attributes;
import com.alibaba.copilot.boot.basic.factory.Converter;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-10-07
 **/

public class AsyncTaskConverter implements Converter<AsyncTaskDO, AsyncTask> {
    public static AsyncTaskConverter INSTANCE = new AsyncTaskConverter();

    @Override
    public AsyncTask convertA2B(AsyncTaskDO taskDO) {
        if (taskDO == null) {
            return null;
        }

        AsyncTask task = new AsyncTask();
        task.setId(taskDO.getId());
        task.setGmtCreate(taskDO.getGmtCreate());
        task.setGmtModified(taskDO.getGmtModified());
        task.setRequest(taskDO.getRequest());
        task.setResult(taskDO.getResult());
        task.setEnv(taskDO.getEnv());
        task.setOuterId(taskDO.getOuterId());
        task.setUniqueKey(taskDO.getUniqueKey());
        task.setType(taskDO.getType());
        task.setPriority(taskDO.getPriority());
        task.setRetryInterval(taskDO.getRetryInterval());
        task.setExecuteCount(taskDO.getExecuteCount());
        task.setMaxRetryTimes(taskDO.getMaxRetryTimes());
        task.setStartExecuteDate(taskDO.getStartExecuteDate());
        task.setNextExecuteDate(taskDO.getNextExecuteDate());
        task.setExpireDate(taskDO.getExpireDate());
        task.setHeartbeatDate(taskDO.getHeartbeatDate());
        task.setStatus(taskDO.getStatus());
        task.setAttributes(new AsyncTaskAttributes(taskDO.getAttributes()));
        task.setUserId(taskDO.getUserId());
        task.setShopId(taskDO.getShopId());
        task.setAppSource(taskDO.getAppSource());
        task.setReadStatus(taskDO.getReadStatus());
        return task;
    }

    @Override
    public AsyncTaskDO convertB2A(AsyncTask task) {
        if (task == null) {
            return null;
        }

        AsyncTaskDO taskDO = new AsyncTaskDO();
        taskDO.setId(task.getId());
        taskDO.setGmtCreate(task.getGmtCreate());
        taskDO.setGmtModified(task.getGmtModified());
        taskDO.setRequest(task.getRequest());
        taskDO.setResult(task.getResult());
        taskDO.setEnv(task.getEnv());
        taskDO.setOuterId(task.getOuterId());
        taskDO.setUniqueKey(task.getUniqueKey());
        taskDO.setType(task.getType());
        taskDO.setPriority(task.getPriority());
        taskDO.setRetryInterval(task.getRetryInterval());
        taskDO.setExecuteCount(task.getExecuteCount());
        taskDO.setMaxRetryTimes(task.getMaxRetryTimes());
        taskDO.setStartExecuteDate(task.getStartExecuteDate());
        taskDO.setNextExecuteDate(task.getNextExecuteDate());
        taskDO.setExpireDate(task.getExpireDate());
        taskDO.setHeartbeatDate(task.getHeartbeatDate());
        taskDO.setStatus(task.getStatus());
        taskDO.setAttributes(task.getAttributes().toString());
        taskDO.setUserId(task.getUserId());
        taskDO.setShopId(task.getShopId());
        taskDO.setAppSource(task.getAppSource());
        taskDO.setReadStatus(task.getReadStatus());
        taskDO.setAttributes(task.getAttributes().toString());
        return taskDO;
    }
}
