package com.alibaba.copilot.app.infrastructure.rewirte.dataobject;

import com.alibaba.copilot.app.infrastructure.base.utils.EnvUtils;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "rewrite_event_log")
public class RewriteEventLogDO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private Date gmtCreate;
    private Date gmtModified;
    private String eventType;
    private String userId;
    private String anonymousId;
    private String eventData;
    private String additionalData;
    private String utmSource;
    private String env = EnvUtils.getEnv();
    private String sourceCountry;
}
