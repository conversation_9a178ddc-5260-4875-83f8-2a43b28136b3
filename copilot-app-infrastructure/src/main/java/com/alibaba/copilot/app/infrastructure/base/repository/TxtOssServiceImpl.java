package com.alibaba.copilot.app.infrastructure.base.repository;

import com.alibaba.copilot.app.domain.base.repository.TxtOssClient;
import com.alibaba.copilot.app.infrastructure.base.utils.OssUtils;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.aliyun.oss.model.PutObjectRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/7/7
 */
@Slf4j
@Component("TxtOssService")
public class TxtOssServiceImpl extends OssAbstractService implements TxtOssClient {
    @Value("${algorithm_image_oss_service_bucket_name}")
    private String bucketName = "t-selection-algorithms-image";

    @Monitor(name = "[saveFileContent] 以字节数组形式上传 TXT 文件", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @Override
    public void saveFileContent(String filePath, String content) {
        if (StringUtils.isBlank(content)) {
            return;
        }

        ByteArrayInputStream inputStream = new ByteArrayInputStream(content.getBytes());
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, filePath, inputStream);
        OssUtils.putObject(putObjectRequest);
    }

    @Monitor(name = "[getFileContent] 查询文件", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @Override
    public String getFileContent(String objectName) {
        Object content = getObject(objectName);
        if (content == null) {
            return null;
        }
        return String.valueOf(content);
    }

    @Monitor(name = "[getFileContentMd5] 获取文件内容MD5", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @Override
    public String getFileContentMd5(String filePath) {
        String content = getFileContent(filePath);
        if (content == null) {
            return "";
        }
        return DigestUtils.md5Hex(content);
    }

    @Monitor(name = "[deleteFile] 删除文件", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @Override
    public void deleteFile(String filePath) {
        OssUtils.deleteObject(bucketName, filePath);
    }

    @Monitor(name = "[isFileExist] 检测文件是否存在", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @Override
    public boolean isFileExist(String filePath) {
        return OssUtils.isObjectExist(bucketName, filePath);
    }

    @Monitor(name = "[getFileUrl] 获取完整路径", layer = Monitor.Layer.SERVICE, level = Monitor.Level.P1)
    @Override
    public String getFileUrl(String filePath) {
        return "https://" + bucketName + ".oss-ap-southeast-1.aliyuncs.com/" + filePath;
    }
}
