package com.alibaba.copilot.app.client.seocopilot.dto;

import com.alibaba.copilot.app.client.seocopilot.response.MultipleInteractionDTO;
import com.alibaba.copilot.app.client.seocopilot.response.RelatedArticle;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class SeoBlogArticleDTO {
    /**
     * shopify article id
     */
    private Long id;

    /**
     * article标题
     */
    private String title;

    /**
     * article status
     */
    private String status;

    /**
     * blogType的Id
     */
    private Long typeId;

    /**
     * blogType的Id
     */
    private String typeName;

    /**
     * article主图链接
     */
    private String imageLink;

    /**
     * article内容摘要
     */
    private String summary;

    /**
     * range分数范围
     */
    private String range;
    /**
     * 原分数
     */
    private BigDecimal score;
    /**
     * 优化后分数
     */
    private BigDecimal optimizedScore;

    /**
     * 优化后的range分数范围
     */
    private String optimizedRange;

    /**
     * isAIGenerated 是否为AI生成文章
     */
    private Boolean isAIGenerated;

    /**
     * alpha rank article更新时间
     */
    private String modifiedDate;

    /**
     * 发布平台文章更新时间
     */
    private String publishPlatformUpdateAt;

    /**
     * 预估阅读分钟数
     */
    private Integer minutesToRead;
    /**
     * 作者
     */
    private String author;
    /**
     * 使用的关键词
     */
    private List<String> usedKeywords;
    /**
     * 是否锁定
     */
    private Boolean locked;
    /**
     * Blog多轮交互 7 要素
     */
    private MultipleInteractionDTO multipleInteractionDTO;

    /**
     * shopify seoBlogId
     */
    private Long shopifySeoBlogId;

    /**
     * language
     */
    private String language;

    /**
     * 关联其他语种的文章信息
     */
    private List<RelatedArticle> relatedArticles;
}
