package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class TopicInfoDTO {

    /**
     * topic
     */
    private String topic;


    /**
     * 原始 keyword_difficulty
     */
    private BigDecimal realDifficulty;

    /**
     * 转换单位 keyword_difficulty
     */
    private String difficulty;

    /**
     * keyword_difficulty 单位
     */
    private String difficultyUnit;

    /**
     * 原始 search_volume
     */
    private Integer realVolume;

    /**
     * 转换单位 search_volume
     */
    private String volume;

    /**
     * search_volume 单位
     */
    private String volumeUnit;

}



