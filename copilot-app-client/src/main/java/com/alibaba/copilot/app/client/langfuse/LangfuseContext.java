package com.alibaba.copilot.app.client.langfuse;

import com.alibaba.ttl.TransmittableThreadLocal;

import java.util.ArrayList;
import java.util.List;

public class LangfuseContext {
    private static final ThreadLocal<String> threadLangfuseTraceId = new TransmittableThreadLocal<>();

    private static final ThreadLocal<String> threadLangfuseObserveId = new TransmittableThreadLocal<>();

    private static final ThreadLocal<String> threadLangfusePromptId = new TransmittableThreadLocal<>();

    private static final ThreadLocal<Integer> threadLangfusePromptVersion = new TransmittableThreadLocal<>();

    private static final ThreadLocal<String> threadLangfuseEmail = new TransmittableThreadLocal<>();

    private static final ThreadLocal<List<TraceBody>> threadLangfuseTraceList = new TransmittableThreadLocal<>();
    static {
        threadLangfuseTraceList.set(new ArrayList<>());
    }

    private static final ThreadLocal<List<ObservationBody>> threadLangfuseObservationList = new TransmittableThreadLocal<>();
    static {
        threadLangfuseObservationList.set(new ArrayList<>());
    }

    private static final ThreadLocal<List<SpanBody>> threadLangfuseSpanList = new TransmittableThreadLocal<>();
    static {
        threadLangfuseSpanList.set(new ArrayList<>());
    }

    private static final ThreadLocal<List<GenerationBody>> threadLangfuseGenerationList = new TransmittableThreadLocal<>();
    static {
        threadLangfuseGenerationList.set(new ArrayList<>());
    }

    public static String getLangfuseTraceId() {
        return threadLangfuseTraceId.get();
    }

    public static void setLangfuseTraceId(String value) {
        threadLangfuseTraceId.set(value);
    }

    public static String getLangfuseObserveId() {
        return threadLangfuseObserveId.get();
    }

    public static void setLangfuseObserveId(String value) {
        threadLangfuseObserveId.set(value);
    }

    public static String getLangfusePromptId() {
        return threadLangfusePromptId.get();
    }
    public static void setLangfusePromptId(String value) {
        threadLangfusePromptId.set(value);
    }

    public static Integer getLangfusePromptVersion() {
        return threadLangfusePromptVersion.get();
    }
    public static void setLangfusePromptVersion(Integer value) {
        threadLangfusePromptVersion.set(value);
    }

    public static String getLangfuseEmail() {
        return threadLangfuseEmail.get();
    }
    public static void setLangfuseEmail(String value) {
        threadLangfuseEmail.set(value);
    }

    public static List<TraceBody> getLangfuseTraceList() {

        if (threadLangfuseTraceList.get() == null) {
            threadLangfuseTraceList.set(new ArrayList<>());
        }
        return threadLangfuseTraceList.get();
    }
    public static void setLangfuseTraceList(List<TraceBody> value) {
        threadLangfuseTraceList.set(value);
    }
    public static List<ObservationBody> getLangfuseObservationList() {

        if (threadLangfuseObservationList.get() == null) {
            threadLangfuseObservationList.set(new ArrayList<>());
        }
        return threadLangfuseObservationList.get();
    }
    public static void setLangfuseObservationList(List<ObservationBody> value) {
        threadLangfuseObservationList.set(value);
    }
    public static List<SpanBody> getLangfuseSpanList() {
        if (threadLangfuseSpanList.get() == null) {
            threadLangfuseSpanList.set(new ArrayList<>());
        }
        return threadLangfuseSpanList.get();
    }
    public static void setLangfuseSpanList(List<SpanBody> value) {
        threadLangfuseSpanList.set(value);
    }
    public static List<GenerationBody> getLangfuseGenerationList() {
        if (threadLangfuseGenerationList.get() == null) {
            threadLangfuseGenerationList.set(new ArrayList<>());
        }
        return threadLangfuseGenerationList.get();
    }
    public static void setLangfuseGenerationList(List<GenerationBody> value) {
        threadLangfuseGenerationList.set(value);
    }

    public static void remove() {
        threadLangfuseTraceId.remove();
        threadLangfuseObserveId.remove();
        threadLangfusePromptId.remove();
        threadLangfusePromptVersion.remove();
        threadLangfuseEmail.remove();
        threadLangfuseTraceList.remove();
        threadLangfuseObservationList.remove();
        threadLangfuseSpanList.remove();
        threadLangfuseGenerationList.remove();
    }
}
