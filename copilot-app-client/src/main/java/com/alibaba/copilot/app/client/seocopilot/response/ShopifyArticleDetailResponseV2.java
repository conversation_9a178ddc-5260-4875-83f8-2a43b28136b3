package com.alibaba.copilot.app.client.seocopilot.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class ShopifyArticleDetailResponseV2 {

    /**
     * article id
     */
    private Long articleId;
    /**
     * 状态
     */
    private String status;
    /**
     * 状态
     */
    private Boolean firstTimeGenerated;
    /**
     * keywords
     */
    private List<String> keywords;
    /**
     * 是否显示keywords
     */
    private Boolean showKeywords;
    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 分类
     */
    private String typeName;
    /**
     * 图片路径
     */
    private String mainPicUrl;
    /**
     * 上次优化时间
     */
    private Long lastOptimizedDate;
    /**
     * summary 列表
     */
    private List<String> summaryList;
    /**
     * blog url
     */
    private String blogUrl;
    /**
     * Content
     */
    private Content content;
    /**
     * Title
     */
    private Title title;
    /**
     * searchAppearance
     */
    private SearchAppearance searchAppearance;
    /**
     * 博客评分范围: bad/average/good
     */
    private String range;
    /**
     * (废弃)历史博客评分范围：bad/average/good
     */
    private String rangeHistory;
    /**
     * 优化后的range分数范围
     */
    private String optimizedRange;
    /**
     * 原分数
     */
    private BigDecimal score;
    /**
     * 优化后分数
     */
    private BigDecimal optimizedScore;
    /**
     * 作者
     */
    private String author;
    /**
     * article更新时间
     */
    private String modifiedDate;

    /**
     * 发布平台文章更新时间
     */
    private String publishPlatformUpdateAt;

    /**
     * 预估阅读分钟数
     */
    private Integer minutesToRead;
    /**
     * 优化后预估阅读分钟数
     */
    private Integer optimizedMinutesToRead;
    /**
     * Blog多轮交互 7 要素
     */
    private MultipleInteractionDTO multipleInteractionDTO;

    /**
     * 是否AI生成 follow列表页逻辑
     */
    private Boolean isAIGenerated;

    /**
     * shopify seoBlogId
     */
    private Long shopifySeoBlogId;

    /**
     * 关联其他语种的文章信息
     */
    private List<RelatedArticle> relatedArticles;

    /**
     * 文章语种
     */
    private String language;

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Content {
        /**
         * proposal id
         */
        private Long id;
        /**
         * 原始内容
         */
        private ContentDetail original;

        /**
         * 优化内容
         */
        private ContentDetail optimize;
        /**
         * 优化建议
         */
        private List<String> advices;

        /**
         * 剩余时间
         */
        private String leftTime;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ContentDetail {
        /**
         * 内容
         */
        private String content;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Title {
        /**
         * proposal id
         */
        private Long id;
        /**
         * 原始标题
         */
        private TitleDetail original;

        /**
         * 优化标题
         */
        private TitleDetail optimize;
        /**
         * 优化建议
         */
        private List<String> advices;

        /**
         * 剩余时间
         */
        private String leftTime;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TitleDetail {
        /**
         * 标题
         */
        private String title;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SearchAppearance {
        /**
         * proposal id
         */
        private Long id;
        /**
         * 原始内容
         */
        private SearchAppearanceDetail original;
        /**
         * 优化内容
         */
        private SearchAppearanceDetail optimize;
        /**
         * 优化建议
         */
        private List<String> advices;
        /**
         * 剩余时间
         */
        private String leftTime;
        /**
         * url是否可以编辑
         */
        private Boolean canUrlEdit;
        /**
         * 域名
         */
        private String domain;
    }


    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SearchAppearanceDetail {
        /**
         * title tag
         */
        private String titleTag;
        /**
         * description tag
         */
        private String metaDescription;
        /**
         * handle
         */
        private String url;
    }

    public static ShopifyArticleDetailResponseV2 getDefaultArticleResponse() {
        return ShopifyArticleDetailResponseV2.builder()
                .firstTimeGenerated(false)
                .keywords(new ArrayList<>())
                .showKeywords(false)
                .summaryList(new ArrayList<>())
                .content(ShopifyArticleDetailResponseV2.Content.builder()
                        .advices(new ArrayList<>())
                        .build())
                .title(ShopifyArticleDetailResponseV2.Title.builder()
                        .advices(new ArrayList<>())
                        .build())
                .searchAppearance(ShopifyArticleDetailResponseV2.SearchAppearance.builder()
                        .advices(new ArrayList<>())
                        .canUrlEdit(false)
                        .build())
                .build();
    }
}
