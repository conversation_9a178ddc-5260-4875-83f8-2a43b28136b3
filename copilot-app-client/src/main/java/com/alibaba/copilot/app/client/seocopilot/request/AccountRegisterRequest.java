package com.alibaba.copilot.app.client.seocopilot.request;

import lombok.Data;

/**
 * AccountRegisterRequest
 *
 * <AUTHOR>
 * @date 2024/7/10 2:35 下午
 */
@Data
public class AccountRegisterRequest {

    /**
     * userId
     */
    private Long userId;

    /**
     * outerUserId
     */
    private String outerUserId;

    /**
     * seoShopId
     */
    private Long seoShopId;

    /**
     * shopifyShopId
     */
    private Long shopifyShopId;

    /**
     * email
     */
    private String email;

    /**
     * siteType
     */
    private String siteType;

    /**
     * shopifyShopDomain
     */
    private String shopifyShopDomain;

    /**
     * 状态：绑定/解绑
     * 1/0
     */
    private Boolean bindStatus;

    /**
     * 登录体系 access token
     */
    private String loginAccessToken;

    /**
     * 登录体系 refresh token
     */
    private String loginRefreshToken;

    /**
     * 用户来源
     */
    private String source;
}
