package com.alibaba.copilot.app.client.seocopilot.request;

import com.alibaba.copilot.enabler.client.subscription.dto.ShopifyFeatureDTO;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class KeywordSearchRequest {
    /**
     * 关键词意图 0 - Commercial | 1 - Informational | 2 - Navigational | 3 - Transactional
     */
    private String intents;

    /**
     * 预测一级类目
     */
    private String keywordTag;

    /**
     * KD得分分类 VeryHard：61-100 | Hard：31-60 | Average：11-30 | Easy：0-10
     */
    private String kdType;

    /**
     * original_keyword 关键词模糊匹配
     */
    private String keyword;

    /**
     * 店铺订阅plan类型
     */
    private ShopifyFeatureDTO featureDTO;

    /**
     * 当前是按哪个字段来排序
     */
    private String orderWord;

    /**
     * 增序 1 减序 0
     */
    private Boolean order;

    /**
     * 查询页面
     */
    private int pageNum;

    /**
     * 每页几条
     */
    private int pageSize;
}
