package com.alibaba.copilot.app.client.seocopilot.request;

import lombok.Data;

import java.util.List;

/**
 * AI优化建议请求
 *
 * <AUTHOR>
 * @date 2023/9/26 5:41 下午
 */
@Data
public class OptimizeRequest {

    /**
     * shopify店铺Id
     */
    private Long shopifyShopId;

    /**
     * 优化级别
     * shop\module\page\blog\collection\product
     */
    private String optimizeLevel;

    /**
     * 优化模块
     * seoScore\seoIndex\seoRank
     */
    private String module;

    /**
     * blogId
     */
    private List<String> blogIds;

    /**
     * collection id
     */
    private List<String> collectionIds;

    /**
     * 商品id
     */
    private List<String> productIds;

    /**
     * 页面优化项
     */
    private List<String> homePageOptimizeItems;

    /**
     * collection优化项，例如：title、content 等
     */
    private List<String> collectionOptimizeItems;

    /**
     * 商品优化项，例如：title、content 等
     */
    private List<String> productOptimizeItems;

    /**
     * 批次号
     */
    private String batchId;

    /**
     * 诊断项Id
     */
    private List<String> recordIds;

    /**
     * 是否自动应用
     */
    private Boolean isAuto;
}
