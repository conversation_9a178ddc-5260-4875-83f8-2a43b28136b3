package com.alibaba.copilot.app.client.seocopilot.service;

import com.alibaba.copilot.app.client.seocopilot.constant.ShopifyEntityTypeEnum;
import com.alibaba.copilot.app.client.seocopilot.constant.SyncShopifyDataStatusEnum;

/**
 * Shopify数据同步service
 */
public interface ShopifyDataSyncService {

    /**
     * 提交同步shopify任务
     *
     * @param type 任务类型
     * @return 是否提交成功
     */
    boolean submitSeoAsyncTask(Long shopifyShopId, ShopifyEntityTypeEnum type);

    /**
     * 查询同步shopify任务进度
     *
     * @param type 任务类型
     * @return 状态
     */
    SyncShopifyDataStatusEnum querySyncShopifyDataStatus(Long shopifyShopId, ShopifyEntityTypeEnum type);
}


