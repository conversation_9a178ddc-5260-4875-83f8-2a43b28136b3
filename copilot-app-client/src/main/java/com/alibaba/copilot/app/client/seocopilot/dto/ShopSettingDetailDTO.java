package com.alibaba.copilot.app.client.seocopilot.dto;

import com.alibaba.copilot.app.client.seocopilot.constant.PageTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShopSettingDetailDTO {

    /**
     * seo shop id
     */
    private Long shopId;

    /**
     * shopify 店铺ID
     */
    private Long shopifyShopId;

    /**
     * shopify 店铺Domain
     */
    private String shopifyShopDomain;

    /**
     * 全托管-内容发布频率（每天）
     */
    private Integer contentPublishFrequency;

    /**
     * 店铺邮箱
     */
    private String shopEmail;

    /**
     * 是否可以发送邮件
     */
    private Boolean canSendEmail;

    /**
     * GSC绑定的邮箱
     */
    private String gscEmail;

    /**
     * GSC status
     */
    private String gscStatus;

    /**
     * 当前订阅计划名称
     */
    private String currentPlan;

    /**
     * 是否开启全托管
     */
    private Boolean fullManagedEnabled;

    /**
     * 全托管模块
     */
    private List<FullManagedModuleDTO> fullManagedModules;

    /**
     * 自动压缩图片
     */
    private List<PageTypeEnum> autoCompressImages;

    /**
     * 自动转换格式图片
     */
    private List<PageTypeEnum> autoConvertImages;

    /**
     * 店铺setting 分数
     */
    private BigDecimal settingScore;

    /**
     * 目标市场
     */
    private List<String> targetCountries;

    /**
     * 排除市场
     */
    private List<String> excludeCountries;

    /**
     * 目标人群
     */
    private List<String> targetCustomers;

    /**
     * 品牌风格
     */
    private List<BrandStyleDTO> brandStyles;

    /**
     * 转换目标
     */
    private List<String> conversionGoals;

    /**
     * 关键词列表
     */
    private List<String> keywords;

    /**
     * 主营行业
     */
    private List<String> mainIndustries;

    /**
     * 内链策略
     */
    private String internalLinkStrategy;

    /**
     * 内链
     */
    private List<InternalLinkDTO> internalLinks;

    /**
     * 竞对链接
     */
    private List<CompetitiveLinkDTO> competitiveLinks;

    /**
     * Homepage Url
     */
    private String homePageUrl;

    /**
     * start time
     */
    private Date performStartTime;

    /**
     * end time
     */
    private Date performEndTime;

    /**
     * 博客类目
     */
    private List<String> blogCategory;

    /**
     * 是否可以提交sitemap
     */
    private Boolean canSubmitSitemap;

    /**
     * GA绑定的邮箱
     */
    private String gaEmail;

    /**
     * GA status
     */
    private String gaStatus;

    /**
     * 图片风格
     */
    private List<ImageStyleDTO> imageStyle;

    /**
     * 语言设置
     */
    private List<String> languages;

    /**
     * 指定博客类目发布
     */
    private List<BlogCategoryApplyDTO> blogCategoryApply;
}



