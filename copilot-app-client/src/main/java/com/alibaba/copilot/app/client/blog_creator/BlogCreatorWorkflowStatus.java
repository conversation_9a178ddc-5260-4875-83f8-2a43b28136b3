package com.alibaba.copilot.app.client.blog_creator;

import lombok.Getter;

@Getter
public enum BlogCreatorWorkflowStatus {
    INIT("init"),
    COLLECTING_INFO("collecting_info"),
    GENERATING_OUTLINE("generating_outline"), 
    GENERATING_CONTENT("generating_content"),
    GENERATING_LINK("generating_link"),
    GENERATING_IMAGE("generating_image"),
    COMPLETED("completed"),
    FAILED("failed");

    private final String value;

    BlogCreatorWorkflowStatus(String value) {
        this.value = value;
    }

}
