package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Accessors(chain = true)
public class SeoShopMsgDTO {

    /**
     * shopify店铺ID
     */
    private Long shopifyShopId;

    /**
     * gsc email
     */
    private String gscEmail;

    /**
     * shopify店铺对外域名
     */
    private String shopDomain;

    /**
     * shopify店铺域名
     */
    private String shopifyShopDomain;

    /**
     * 店铺名
     */
    private String shopName;

    /**
     * shopify的email
     */
    private String shopEmail;


}
