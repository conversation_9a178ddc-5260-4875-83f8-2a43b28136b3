package com.alibaba.copilot.app.client.seocopilot.constant;

import lombok.Getter;

@Getter
public enum ImageStyleEnum {


    flat_illustration("Flat Illustration", "flat_illustration", "平面插画, 之前的模型", "https://img.alicdn.com/imgextra/i1/O1CN01aERWeU1PCjtuG7Y0T_!!6000000001805-0-tps-722-837.jpg"),
    // indoor_home("Indoor Home", "indoor_home", "家居风格", "https://img.alicdn.com/imgextra/i1/O1CN01KaUFSK1zpVazFbzqY_!!6000000006763-0-tps-896-1200.jpg"),
    ecommerce_booth("Ecommerce Booth", "ecommerce_booth", "电商展台", "https://img.alicdn.com/imgextra/i3/O1CN01QJtRNy1elkPd9Vnmb_!!6000000003912-0-tps-896-1200.jpg"),
    // product_photography("Product Photography", "product_photography", "产品摄影", "https://img.alicdn.com/imgextra/i3/O1CN013kJbY31Z60EXXUhNG_!!6000000003144-0-tps-896-1200.jpg"),
    outdoor_product_photography("Outdoor Product Photography", "outdoor_product_photography", "室外产品摄影", "https://img.alicdn.com/imgextra/i4/O1CN01va99S01HBoAXsrry1_!!6000000000720-0-tps-896-1200.jpg"),
    atmosphere_photography("Atmosphere Photography", "atmosphere_photography", "氛围感摄影", "https://img.alicdn.com/imgextra/i4/O1CN01oeqmIi1sen11G7Yiu_!!6000000005792-0-tps-896-1200.jpg"),
    // fruit_photography("Fruit Photography", "fruit_photography", "水果摄影", "https://img.alicdn.com/imgextra/i1/O1CN01qQ6ulw21Z8fcWrauO_!!6000000006998-0-tps-896-1200.jpg"),
    cosmetics_photography("Cosmetics Photography", "cosmetics_photography", "美妆摄影", "https://img.alicdn.com/imgextra/i4/O1CN01DVwugm1DZqUYKol1H_!!6000000000231-0-tps-896-1200.jpg"),
    // ecommerce_product_photography("Ecommerce Product Photography", "ecommerce_product_photography", "电商产品摄影", "https://img.alicdn.com/imgextra/i3/O1CN01TIpCnz1sW5UV7FvWU_!!6000000005773-0-tps-896-1200.jpg")
    portrait("Portrait","portrait","人像","https://img.alicdn.com/imgextra/i1/O1CN011AtrLE1UFYQ32AtPY_!!6000000002488-0-tps-1018-1014.jpg"),
    general_model("General Model","general_model","通用模型","https://img.alicdn.com/imgextra/i1/O1CN01Heq47A1nSoaS8M52I_!!6000000005089-0-tps-774-774.jpg")
    ;

    private String name;

    private String style;

    private String desc;

    private String demoUrl;

    ImageStyleEnum(String name, String style, String desc, String demoUrl) {
        this.name = name;
        this.name = name;
        this.style = style;
        this.desc = desc;
        this.demoUrl = demoUrl;
    }


    public static ImageStyleEnum getByStyle(String style) {
        for (ImageStyleEnum imageStyleEnum : ImageStyleEnum.values()) {
            if (imageStyleEnum.getStyle().equals(style)) {
                return imageStyleEnum;
            }
        }
        throw new IllegalArgumentException("ImageStyleEnum.getByStyle Invalid style: " + style);
    }

}
