package com.alibaba.copilot.app.client.seocopilot.constant;

import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * Shopify实体类型
 */
@Getter
public enum FullManagedModuleEnum {

    /**
     * Technology SEO优化
     */
    TECH_OPTIMIZE,

    /**
     * Blog 优化
     */
    BLOG_OPTIMIZE,

    /**
     * Blog 生成
     */
    BLOG_GENERATE,

    /**
     * 商品优化
     */
    PRODUCT_OPTIMIZE,

    /**
     * Collection 优化
     */
    COLLECTION_OPTIMIZE;

    /**
     * 全部模块
     *
     * @return
     */
    public static List<FullManagedModuleEnum> getAllModules() {
        return Lists.newArrayList(TECH_OPTIMIZE, BLOG_OPTIMIZE, BLOG_GENERATE, PRODUCT_OPTIMIZE, COLLECTION_OPTIMIZE);
    }
}
