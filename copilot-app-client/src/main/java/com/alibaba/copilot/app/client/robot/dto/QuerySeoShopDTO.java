package com.alibaba.copilot.app.client.robot.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 2023/11/13
 */
@Data
@Accessors(chain = true)
public class QuerySeoShopDTO implements Serializable {

    /**
     * shopify店铺ID
     */
    private Long shopifyShopId;

    /**
     * shopify店铺域名 (有绑定域名时, 为绑定域名)
     */
    private String shopDomain;

    /**
     * shopify店铺域名
     */
    private String shopifyShopDomain;

    /**
     * 店铺名
     */
    private String shopName;

    /**
     * 店铺名模糊查询参数
     */
    private String shopNameLike;

    /**
     * shopify的email
     */
    private String shopEmail;
}
