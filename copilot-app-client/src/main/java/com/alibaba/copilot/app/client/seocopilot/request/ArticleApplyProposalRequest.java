package com.alibaba.copilot.app.client.seocopilot.request;

import com.alibaba.copilot.app.client.seocopilot.response.ShopifyArticleMetaResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ArticleApplyProposalRequest {
    /**
     * 采纳类型：TDK 只采纳meta内容 | CONTENT 只采纳博客内容 | ALL 优化博客后，一键采纳所有内容 | ADD 新增博客后，采纳所有内容
     */
    String type;

    /**
     * ADD任务的checkRecordId
     */
    private Long checkRecordAddId;

    /**
     * Optimize任务的checkRecordId
     */
    private Long checkRecordOptimizeId;

    /**
     * TDK任务的checkRecordId
     */
    private Long checkRecordTDKId;

    /**
     * 优化后的标题
     */
    private String optimizedTitle;


    /**
     * 优化后的正文内容html
     */
    private String optimizedBody;

    /**
     * 优化后搜索引擎优化信息 title
     */
    private String optimizedTitleTag;

    /**
     * 优化后搜索引擎优化信息 description
     */
    private String optimizedDescriptionTag;

    /**
     * 店铺shopifyID
     */
    private Long shopifyShopId;

    /**
     * 文章ID
     */
    private Long articleId;
}
