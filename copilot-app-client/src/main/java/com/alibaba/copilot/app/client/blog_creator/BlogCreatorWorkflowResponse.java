package com.alibaba.copilot.app.client.blog_creator;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain=true)
public class BlogCreatorWorkflowResponse {
    /**
     * init | generating_outline | generating_content | success | fail
      */
    private String status;
    /** 大部分情况下为空，报错的时候会返回错误信息 */
    private String errorMessage;
    private String title;
    private String outline;
    private String content;
    /** 预览博客内容，wordpress链接 */
    private String previewUrl = null;
}
