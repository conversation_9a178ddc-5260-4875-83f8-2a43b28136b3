package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * KeywordRankResult
 *
 * <AUTHOR>
 * @date 2023/12/20 3:29 下午
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class KeywordRankResult {

    /**
     * 仪表盘状态（诊断中/CHECKING、诊断完成/CHECKED、优化中/OPTIMIZING、优化完成/OPTIMIZED）
     */
    private String status;

    /**
     * 预计结束时间
     */
    private BigDecimal preEndTime;

    /**
     * 预计结束时间单位
     */
    private String endTimeUnit;

    /**
     * Keyword rank
     */
    private BigDecimal rank;

    /**
     * SEO Score-上次诊断时间
     */
    private String lastCheckTime;

    /**
     * Keyword rank（第一次）
     */
    private BigDecimal firstRank;

    /**
     * Keyword rank-相比第一次提升
     */
    private BigDecimal improved;

    /**
     * 问题总数
     */
    private Integer totalWaitOptimizeRecord;

    /**
     * 完成优化的项目数量（历史原因：命名暂不修改）
     */
    private Integer totalGeneratedOptimize;

    /**
     * check record
     */
    private List<KeywordRankCheckRecord> checkRecords;
}
