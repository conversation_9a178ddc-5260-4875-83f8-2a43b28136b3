package com.alibaba.copilot.app.client.langfuse;

import java.util.Map;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class GenerationBody {
    private String traceId;
    private String name;
    private String startTime;
    private Object metadata; // 未知类型信息，使用Object类型
    private Object input; // 未知类型信息，使用Object类型
    private Object output; // 未知类型信息，使用Object类型
    private ObservationLevel level;
    private String statusMessage;
    private String parentObservationId;
    private String version;
    private String id;
    private String endTime;
    private String completionStartTime;
    private String model;
    private Map<String, Object> modelParameters; // 可变参数类型，使用Map表示
    private Object usage; // 多态类型
    private String promptName;
    private Integer promptVersion;

}
