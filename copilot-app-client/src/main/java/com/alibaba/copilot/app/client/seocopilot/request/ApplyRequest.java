package com.alibaba.copilot.app.client.seocopilot.request;

import lombok.Data;

import java.util.List;

/**
 * apply请求
 *
 * <AUTHOR>
 * @date 2023/12/22 11:20
 */
@Data
public class ApplyRequest {

    /**
     * shopify店铺Id
     */
    private Long shopifyShopId;

    /**
     * 优化模块
     * seoScore\seoIndex\seoRank
     */
    private String module;

    /**
     * blog id
     */
    private List<Long> seoBlogIds;

    /**
     * collection id
     */
    private List<Long> seoCollectionIds;

    /**
     * product id
     */
    private List<Long> seoProductIds;

    /**
     * blog诊断项Id
     */
    private List<Long> blogCheckRecordIds;

    /**
     * collection诊断项Id
     */
    private List<Long> collectionCheckRecordIds;

    /**
     * product诊断项Id
     */
    private List<Long> productCheckRecordIds;
}
