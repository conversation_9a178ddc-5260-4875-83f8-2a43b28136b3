package com.alibaba.copilot.app.client.flow;

import com.alibaba.copilot.app.client.flow.model.FlowNodeRequest;
import com.alibaba.copilot.app.client.flow.model.FlowNodeResponse;
import com.alibaba.copilot.boot.basic.result.SingleResult;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/05/17
 */
public interface FlowNodeExecuteSPI {

    /**
     * 执行业务逻辑
     *
     * @param request req
     * @return
     */
    SingleResult<FlowNodeResponse> execute(FlowNodeRequest request);

}