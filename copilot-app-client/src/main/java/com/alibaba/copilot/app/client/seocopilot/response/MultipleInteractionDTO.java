package com.alibaba.copilot.app.client.seocopilot.response;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 多轮 7 要素
 */
@Data
@Builder
public class MultipleInteractionDTO {

    /**
     * 走过多轮交互的标  multiturn
     */
    private String source;
    /**
     * keywords
     */
    private List<String> keywords;
    /**
     * style
     */
    private String blogStyle;
    /**
     * country
     */
    private String country;
    /**
     * blogCategory
     */
    private String blogCategory;
    /**
     * link
     */
    private String link;

    /**
     * title
     */
    private String title;
    /**
     * outline  整体大纲
     */
    private String outline;

}
