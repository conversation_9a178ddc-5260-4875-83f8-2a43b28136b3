package com.alibaba.copilot.app.client.seocopilot.service;

import com.alibaba.copilot.app.client.seocopilot.request.ApplyRequest;
import com.alibaba.copilot.app.client.seocopilot.request.OptimizeRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

import java.util.List;

/**
 * 首页优化服务
 *
 * <AUTHOR>
 * @date 2023/10/26 8:32 下午
 */
public interface HomePageOptimizeService {

    /**
     * ai优化
     *
     * @param optimizeRequest
     * @return
     */
    SingleResult<Long> aiOptimize(OptimizeRequest optimizeRequest);

    /**
     * 获取全托管参数
     *
     * @param shopifyShopId
     * @return
     */
    List<String> getFullManagedModuleParam(Long shopifyShopId);

    /**
     * 首页优化-plugins
     *
     * @param optimizeRequest
     * @return
     */
    SingleResult<String> doPluginsOptimize(OptimizeRequest optimizeRequest);

    /**
     * 首页优化-robot
     *
     * @param optimizeRequest
     * @return
     */
    SingleResult<String> doRobotOptimize(OptimizeRequest optimizeRequest);

    /**
     * 采纳优化
     *
     * @param optimizeRequest
     * @return
     */
    SingleResult<String> applyOptimize(OptimizeRequest optimizeRequest);

    /**
     * 采纳全部优化
     *
     * @param applyRequest
     * @return
     */
    SingleResult<String> applyAllOptimize(ApplyRequest applyRequest);
}
