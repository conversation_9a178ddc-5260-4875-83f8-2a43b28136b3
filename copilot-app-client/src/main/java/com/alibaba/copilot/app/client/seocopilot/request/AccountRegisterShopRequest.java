package com.alibaba.copilot.app.client.seocopilot.request;

import lombok.Data;

/**
 * AccountRegisterRequest
 *
 * <AUTHOR>
 * @date 2024/7/10 2:35 下午
 */
@Data
public class AccountRegisterShopRequest {

    /**
     * userId
     */
    private Long userId;

    /**
     * shopifyShopId
     */
    private Long shopifyShopId;

    /**
     * email
     */
    private String email;

    /**
     * siteType
     */
    private String siteType;

    /**
     * shop domain
     */
    private String shopDomain;

    /**
     * shopifyShopDomain
     */
    private String shopifyShopDomain;

    /**
     * shop name
     */
    private String shopName;

    /**
     * 状态：绑定/解绑
     * 1/0
     */
    private Boolean bindStatus;

    /**
     * access token
     */
    private String accessToken;

    /**
     * platform
     */
    private String platform;
}
