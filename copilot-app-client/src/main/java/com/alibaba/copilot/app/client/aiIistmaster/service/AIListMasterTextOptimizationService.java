package com.alibaba.copilot.app.client.aiIistmaster.service;

import java.util.List;

import com.alibaba.copilot.app.client.aiIistmaster.request.TextOptimizationRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

/**
 * @ClassName HomePageService
 * <AUTHOR>
 * @Date 2023/8/21 10:36
 */
public interface AIListMasterTextOptimizationService {

    /**
     * 生成标题
     *
     * @param textOptimizationRequest 文本优化请求
     * @return
     */
    SingleResult<List<String>> generateTitle(TextOptimizationRequest textOptimizationRequest);

    /**
     * 生成短卖点
     *
     * @param textOptimizationRequest 文本优化请求
     * @return
     */
    SingleResult<String> generateShortSellPoint(TextOptimizationRequest textOptimizationRequest);

    /**
     * 生成商详
     *
     * @param textOptimizationRequest 文本优化请求
     * @return
     */
    SingleResult<String> generateDescription(TextOptimizationRequest textOptimizationRequest);
}
