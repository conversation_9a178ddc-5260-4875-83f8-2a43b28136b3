package com.alibaba.copilot.app.client.langfuse;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain=true)
public class SpanBody {
    private String traceId;
    private String name;
    private String startTime;
    private Object metadata; // 未知类型信息，使用Object类型
    private Object input; // 未知类型信息，使用Object类型
    private Object output; // 未知类型信息，使用Object类型
    private ObservationLevel level;
    private String statusMessage;
    private String parentObservationId;
    private String version;
    private String id;
    private String endTime;
}
