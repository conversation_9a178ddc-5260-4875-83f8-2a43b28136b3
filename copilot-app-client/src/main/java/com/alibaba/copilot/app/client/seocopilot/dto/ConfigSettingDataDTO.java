package com.alibaba.copilot.app.client.seocopilot.dto;

import com.alibaba.copilot.app.client.seocopilot.constant.ImageStyleEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfigSettingDataDTO {

    /**
     * 主营行业
     */
    private List<String> canSelectMainIndustries;

    /**
     * 目标人群
     */
    private List<String> canSelectTargetCustomers;

    /**
     * 转化目标
     */
    private List<String> canSelectConversionGoals;

    /**
     * 品牌风格
     */
    private List<BrandStyleDTO> canSelectBrandStyles;

    /**
     * 时区信息
     */
    private List<String> allTimeZones;

    /**
     * languages
     */
    private List<String> languages;

    /**
     * 图片风格
     */
    private List<AIGenerateImageStyleDTO> imageStyles;
}



