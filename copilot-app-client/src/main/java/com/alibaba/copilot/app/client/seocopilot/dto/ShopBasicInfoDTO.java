package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Accessors(chain = true)
public class ShopBasicInfoDTO {

    /**
     * shopDomain
     */
    private String shopDomain;

    /**
     * Secret status
     * true:存在密码
     * false：不存在密码
     */
    private Boolean secretStatus;

    /**
     * 锚点URL
     */
    private String secretAnchorUrl;

    /**
     * 店铺是否已经开启全托管
     */
    private Boolean isOpenedFullyManaged;

    /**
     * 店铺是否可以开启全托管
     */
    private Boolean isCanOpenFullyManaged;

    /**
     * 店铺是否可以关闭全托管（订阅计划是否支持）
     */
    private Boolean isCanCloseFullyManaged;

    /**
     * 应该安装后是否第一次访问
     */
    private Boolean isFirstAccess;
    /**
     * 是否开启二次引导
     */
    private Boolean isSecondGuideAvailable;

    /**
     * 全托管全局开关
     */
    private Boolean fullyManagedSwitch;

}
