package com.alibaba.copilot.app.client.seocopilot.request;

import com.alibaba.copilot.app.client.seocopilot.constant.InternalLinkStrategyEnum;
import com.alibaba.copilot.app.client.seocopilot.constant.PageTypeEnum;
import com.alibaba.copilot.app.client.seocopilot.dto.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShopSettingDetailRequest {

    /**
     * seo shop id
     */
    private Long shopId;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 定时时间(0,1,2,3.....23)
     */
    private String taskExectuteTime;

    /**
     * 竞对内链
     */
    private List<CompetitiveLinkDTO> competitiveLinks;

    /**
     * 是否可以发送邮件
     */
    private Boolean canSendEmail;

    /**
     * 全托管-内容发布频率（每天）
     */
    private Integer contentPublishFrequency;

    /**
     * 自动压缩图片
     */
    private List<PageTypeEnum> autoCompressImages;

    /**
     * 自动转换格式图片
     */
    private List<PageTypeEnum> autoConvertImages;

    /**
     * 目标市场
     */
    private List<String> targetCountries;

    /**
     * 排除市场
     */
    private List<String> excludeCountries;

    /**
     * 目标人群
     */
    private List<String> targetCustomers;

    /**
     * 品牌风格
     */
    private List<BrandStyleDTO> brandStyles;

    /**
     * 转换目标
     */
    private List<String> conversionGoals;

    /**
     * 关键词列表
     */
    private List<String> keywords;

    /**
     * Blog分类
     */
    private List<String> blogCategory;

    /**
     * 主营行业
     */
    private List<String> mainIndustries;

    /**
     * 内链策略
     */
    private InternalLinkStrategyEnum internalLinkStrategy;

    /**
     * 内链
     */
    private List<InternalLinkDTO> internalLinks;

    /**
     * 全托管-托管模块
     */
    private List<FullManagedModuleDTO> fullManagedModules;

    /**
     * 竞对链接
     */
    private List<CompetitiveLinkDTO> competitiveLink;

    /**
     * 品牌颜色
     */
    private String brandColor;

    /**
     * slogan
     */
    private String slogan;

    /**
     * 卖点
     */
    private String sellingPoint;

    /**
     * 图片风格
     */
    private List<ImageStyleDTO> imageStyle;

    /**
     * 语言设置
     */
    private List<String> languages;

    /**
     * 指定Blog分类
     */
    private List<BlogCategoryApplyDTO> blogCategoryApply;
}



