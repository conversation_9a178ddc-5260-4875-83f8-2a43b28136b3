package com.alibaba.copilot.app.client.seocopilot.service;


import com.alibaba.copilot.app.client.seocopilot.constant.UserInputType;
import com.alibaba.copilot.app.client.seocopilot.dto.*;
import com.alibaba.copilot.app.client.seocopilot.request.SaveGuideInfoRequest;
import com.alibaba.copilot.app.client.seocopilot.request.SaveSiteInfoRequest;

import javax.servlet.http.HttpServletResponse;

public interface SiteAnalysisService {

    SiteAnalysisDetailDTO analysisSite(String siteOrKeyword, UserInputType type, Long shopId);

    Boolean saveSiteInfo(SaveSiteInfoRequest request, Long shopId);

    Boolean updateSiteDomain(String domain, Long shopId);

    Boolean firstAnalysisSite(Long shopId);

    Boolean tagFirstBlog(Long shopId);


    Boolean getSiteCompetitorKeyword(Long shopId);

    Boolean getSiteTopic(Long shopId);

    AnalysisSiteDetailDTO siteAnalysis(String site, Long shopId);
    AnalysisKeywordDetailDTO keywordAnalysis(String keyword, Long shopId);

    Boolean saveGuideInfo(SaveGuideInfoRequest request, Long shopId);

    String getGuideBookLink(Long shopId);

    GuidePageDetailDTO getGuidePageData(Long shopId);

    GuideNodeInfoDTO getGuideNodeInfo(Long shopId);
}
