package com.alibaba.copilot.app.client.seocopilot.dto;

import com.alibaba.copilot.app.client.seocopilot.response.MultipleInteractionDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class SeoBlogArticleHistoryDetailDTO {

    private Long articleId;

    /**
     * 当前状态
     */
    private String status;

    /**
     * 当前分数
     */
    private BigDecimal score;

    /**
     * 档案博客评分范围: bad/average/good
     */
    private String range;

    /**
     * 作者
     */
    private String author;
    /**
     * article更新时间
     */
    private String modifiedDate;
    /**
     * 预估阅读分钟数
     */
    private Integer minutesToRead;

    /**
     * Blog多轮交互 7 要素
     */
    private MultipleInteractionDTO multipleInteractionDTO;

    private Long recordId;

    private Title title;

    private Content content;

    private SearchAppearance searchAppearance;

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Content {

        /**
         * 当前内容
         */
        private ContentDetail current;

        /**
         * 历史内容
         */
        private ContentDetail history;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ContentDetail {
        /**
         * 内容
         */
        private String content;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Title {
        /**
         * 当前标题
         */
        private TitleDetail current;

        /**
         * 历史标题
         */
        private TitleDetail history;

    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TitleDetail {
        /**
         * 标题
         */
        private String title;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SearchAppearance {
        /**
         * 当前TDK
         */
        private SearchAppearanceDetail current;
        /**
         * 历史TDK
         */
        private SearchAppearanceDetail history;

    }


    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SearchAppearanceDetail {
        /**
         * title tag
         */
        private String titleTag;
        /**
         * description tag
         */
        private String metaDescription;
        /**
         * handle
         */
        private String url;
    }
}
