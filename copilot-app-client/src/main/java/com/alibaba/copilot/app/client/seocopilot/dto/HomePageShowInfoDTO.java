package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2023/9/25
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class HomePageShowInfoDTO {

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * shopify shop id
     */
    private Long shopifyShopId;

    /**
     * shop domain
     */
    private String shopDomain;

    /**
     * shopify shop domain
     */
    private String shopifyShopDomain;

    /**
     * 任务批次号
     */
    private String batchId;

    /**
     * 店铺GSC status：unbound、valid、invalid
     */
    private String gscStatus;

    /**
     * GSC绑定地址
     */
    private String gscOauthAddr;

    /**
     * 是否需要展示首次optimize文案
     */
    private Boolean isShowNoFullManagedOptimizeRemark;

    /**
     * 是否可以开启全托管
     */
    private Boolean isCanOpenFullManaged;

    /**
     * SEO Score相关数据（PC）
     */
    private SeoScoreResult pcScoreResult;

    /**
     * SEO Score相关数据（Mobile）
     */
    private SeoScoreResult mobileScoreResult;

    /**
     * Shop Index相关数据
     */
    private ShopIndexResult shopIndexResult;

    /**
     * Keyword Rank相关数据
     */
    private KeywordRankResult keywordRankResult;

    /**
     * 用户安装扫描诊断
     */
    private Boolean newCusScanChkFin;

    /**
     * 跳转列表页
     */
    private String toEntityListPage;
}
