package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/25
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ShopSecretStatusDTO {

    /**
     * shopify店铺ID
     */
    private Long shopifyShopId;

    /**
     * shop domain
     */
    private String shopDomain;

    /**
     * Secret status
     * true:存在密码
     * false：不存在密码
     */
    private Boolean secretStatus;

    /**
     * 锚点URL
     */
    private String anchorUrl;
}
