package com.alibaba.copilot.app.client.seocopilot.request;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.stream.Collectors;


@Data
public class AuthAuthorizeRequest {

    /**
     * 授权回调-hmac
     */
    public String hmac;

    /**
     * 授权回调-code
     */
    public String code;

    /**
     * 授权回调-state
     */
    public String state;

    /**
     * 授权回调-userId
     */
    public String userId;

    /**
     * 授权回调-systemDomain
     */
    public String systemDomain;

    /**
     * 授权回调-jumpFrom
     */
    public String jumpFrom;

    /**
     * 授权回调-callbackUrl
     */
    public String callbackUrl;

    public String authAction;

    /**
     * 授权回调-invite（邀请人ID）
     */
    public String invite;

    public String loginReferral;
    
    /**
     * 授权回调-utm_source
     */
    public String utm_source;

    /**
     * request转map
     *
     * @return
     */
    public Map<String, String> convertMap() {
        JSONObject json = (JSONObject) JSON.toJSON(this);
        return json.getInnerMap().keySet().stream()
                .filter(key -> json.get(key) != null)
                .collect(Collectors.toMap(key -> key, json::getString));
    }

    /**
     * 获取UTM参数字符串
     */
    public String buildUtmParamsString() {
        StringBuilder utmParams = new StringBuilder();
        if (StringUtils.isNotBlank(authAction)) {
            utmParams.append("auth_action=").append(authAction);
        }
        return utmParams.toString();
    }
}
