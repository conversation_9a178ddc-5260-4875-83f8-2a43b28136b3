package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SeoCollectionHistoryDetailDTO {

    private Long collectionId;

    /**
     * 当前状态
     */
    private String status;

    /**
     * record 记录ID
     */
    private Long recordId;

    private Title title;

    private Description description;

    private SearchAppearance searchAppearance;

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Description {

        /**
         * 当前内容
         */
        private DescriptionDetail current;

        /**
         * 历史内容
         */
        private DescriptionDetail history;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DescriptionDetail {
        /**
         * 内容
         */
        private String description;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Title {
        /**
         * 当前标题
         */
        private TitleDetail current;

        /**
         * 历史标题
         */
        private TitleDetail history;

    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TitleDetail {
        /**
         * 标题
         */
        private String title;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SearchAppearance {
        /**
         * 当前TDK
         */
        private SearchAppearanceDetail current;
        /**
         * 历史TDK
         */
        private SearchAppearanceDetail history;

    }


    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SearchAppearanceDetail {
        /**
         * title tag
         */
        private String titleTag;
        /**
         * description tag
         */
        private String metaDescription;
        /**
         * handle
         */
        private String url;
    }
}
