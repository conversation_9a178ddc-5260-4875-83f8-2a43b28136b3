package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GuidePageDetailDTO {

    /**
     * Keyword Insight
     */
    private List<KeywordInfoDTO> keywords;

    /**
     * Technical SEO
     */
    private TechnicalSeo technicalSeo;


    /**
     * Content Performance
     */
    private ContentPerformance contentPerformance;

    /**
     * Competitor Performance
     */
    private List<Competitor> competitorsPerformance;

    @Data
    public static class TechnicalSeo {
        private BigDecimal pcScore;

        private BigDecimal mobileScore;
    }

    @Data
    public static class ContentPerformance {
        private String title;

        private String body;

        private List<String> keywords;

        private List<String> industries;
    }

    @Data
    public static class Competitor {
        private String domain;
        private List<String> keywords;
    }
}



