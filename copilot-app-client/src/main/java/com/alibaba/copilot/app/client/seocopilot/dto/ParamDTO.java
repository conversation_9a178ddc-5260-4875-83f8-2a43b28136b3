package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class ParamDTO {

    private List<String> keywords = new ArrayList<>();

    private String category;

    private String categoryName;

    private String style;

    private String country;

    private String url;

    private String source;

    private String name;

    private String time;

    private List<String> tags = new ArrayList<>();

    private List<ProductDTO> products = new ArrayList<>();

    //////////////////////blog重写特殊字段//////////////////////
    private String rewriteUrl;
    private String site;
    private String status;
    //////////////////////blog重写特殊字段//////////////////////

    @Data
    public static class ProductDTO {
        private String productId;
        private String title;
        private String order;
        private String rating;
        private String description;
        private String mainPhotoUrl;
    }

}
