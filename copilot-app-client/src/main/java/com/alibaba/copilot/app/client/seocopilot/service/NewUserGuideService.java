package com.alibaba.copilot.app.client.seocopilot.service;

import com.alibaba.copilot.app.client.seocopilot.dto.SeoShopifyShopInfoDTO;
import com.alibaba.copilot.app.client.seocopilot.request.BlogArticleGenerateRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

public interface NewUserGuideService {
    /**
     * 获取shopify店铺商品、page等信息
     */
    SingleResult<SeoShopifyShopInfoDTO> getShopInfo(Long shopifyShopId);
    /**
     * 新人引导生成内容（不校验订阅状态）
     */
    SingleResult<Long> generateContent(Long shopifyShopId, BlogArticleGenerateRequest request);
    /**
     * 新人引导生成内容（不校验订阅状态）
     */
    SingleResult<String> getBlogContent(Long shopifyShopId, Long articleId,Boolean substring);
}
