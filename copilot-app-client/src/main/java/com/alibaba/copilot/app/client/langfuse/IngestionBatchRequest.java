package com.alibaba.copilot.app.client.langfuse;

import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class IngestionBatchRequest<T> {
    private List<IngestionRequest<?>> batch;
    private Object metadata;

    @Data
    @Accessors(chain = true)
    public static class IngestionRequest<T> {
        private String type;
        private T body;
        private String id;
        private String timestamp;
        private Object metadata;
    }
}

