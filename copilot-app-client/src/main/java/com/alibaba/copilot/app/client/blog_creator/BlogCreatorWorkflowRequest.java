package com.alibaba.copilot.app.client.blog_creator;

import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain=true)
public class BlogCreatorWorkflowRequest {
    private List<String> competitorUrls;
    private Map<String, String> internalLinksMap;
    private List<String> keywords;
    private String subject = null;
    private String language = "English";
    private List<String> productUrls = null;
    private String workflowType = "simple"; // simple | list | product
}
