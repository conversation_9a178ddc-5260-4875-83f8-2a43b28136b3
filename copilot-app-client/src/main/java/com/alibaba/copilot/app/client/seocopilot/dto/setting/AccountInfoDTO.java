package com.alibaba.copilot.app.client.seocopilot.dto.setting;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author: mianyun.yt
 * @Date: 2024/3/20
 */
@Data
public class AccountInfoDTO {

    private Long userId;

    private Long shopId;

    private String email;

    private String plan;

    /**
     * MONTH/YEAR
     */
    private String durationUnit;

    private Integer totalQuota;

    private Integer usedQuota;

    private Date performStartTime;

    private Date performEndTime;

    /**
     * 权限型特性信息
     */
    private List<AuthFeatureInfoDTO> authFeatureInfos;

    /**
     * 店铺域名
     */
    private String shopDomain;

    private String gscStatus;

    private String gaStatus;

    private String gscEmail;

    private String gaEmail;

    private Boolean firstBlog;

    private String publishPlatformStatus;

    private String publishPlatform;

    private String publishPlatformAccount;
}
