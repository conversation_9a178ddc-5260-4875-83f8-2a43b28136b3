package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.Data;

import java.util.List;

@Data
public class UserGrowthGenRequestParamDTO {

    /**
     * 关键词
     */
    private String keywords;

    /**
     * 生成类目
     */
    private String genCategory;

    /**
     * 风格
     */
    private String style;

    /**
     * 国家
     */
    private String country;

    /**
     * 内链
     */
    private String innerLink;

    /**
     * rewrite url
     */
    private String rewriteUrl;

    /**
     * prompt版本
     */
    private String promptVersion;

    /**
     * 是否需要爬虫
     */
    private Boolean crawl;

    /**
     * 是否并行生成内容
     */
    private Boolean isParallel;

    /**
     * 发布站点(domain)
     */
    private String site;

    /**
     * blog状态（发布状态/草稿状态）
     */
    private String status;

    /**
     * 发布类目
     */
    private String publishCategories;

    /**
     * tags
     */
    private List<String> tags;

    /**
     * 图片风格
     */
    private String imageStyle;

    /**
     * 图片数量
     */
    private Integer imageNum;
}
