package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Accessors(chain = true)
public class SeoBlogProposalDTO {
    /**
     * proposal_Id
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * seo copilot店铺ID
     */
    private Long shopId;

    /**
     * 关联诊断对象ID
     */
    private Long entityId;

    /**
     * 生成的内容
     */
//    private String optimizingContent;

    private BigDecimal blogScore;

    private BigDecimal plagiarismScore;

    private String plagiarismScanId;

    private String body;
    private String title;
    private String titleTag;
    private List<String> keywords;
    private String descriptionTag;
}
