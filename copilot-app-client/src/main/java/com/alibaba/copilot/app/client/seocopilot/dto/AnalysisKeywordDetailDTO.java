package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AnalysisKeywordDetailDTO {

    /**
     * 用户输入的关键词
     */
    private KeywordInfoDTO userKeyword;

    /**
     * 推荐关键词列表
     */
    private List<KeywordInfoDTO> recommendKeywords = new ArrayList<>();

    /**
     * 关键词推荐理由
     */
    private String recommendKeywordsReason;

    /**
     * 话题信息
     */
    private List<TopicInfoDTO> topics = new ArrayList<>();

}



