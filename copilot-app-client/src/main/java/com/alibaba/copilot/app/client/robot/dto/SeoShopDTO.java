package com.alibaba.copilot.app.client.robot.dto;

import com.alibaba.copilot.enabler.client.robot.dto.BaseDTO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @version 2023/11/13
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SeoShopDTO extends BaseDTO {

    /**
     * shopify店铺ID
     */
    private Long shopifyShopId;

    /**
     * gsc email
     */
    private String gscEmail;

    /**
     * shopify店铺域名
     */
    private String shopDomain;

    /**
     * shopify店铺域名
     */
    private String shopifyShopDomain;

    /**
     * 店铺名
     */
    private String shopName;

    /**
     * shopify的email
     */
    private String shopEmail;

    /**
     * shopify的email
     */
    private Boolean fullyManagedEnabled;

    /**
     * 能够自动优化的类型
     */
    private String autoOptimizationType;

    /**
     * 最后一次全局任务批次号
     */
    private String batchId;

    /**
     * 状态：绑定/解绑
     */
    private Boolean status;

    /**
     * 逻辑删除
     */
    private Boolean deleted;
}
