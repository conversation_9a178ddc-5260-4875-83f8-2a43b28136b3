package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SeoKeywordDTO {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * original_keyword
     */
    private String originalKeyword;

    /**
     * seo质量分
     */
    private Integer seoScore;

    /**
     * 关键词意图
     */
    private String intents;

    /**
     * 关键词平均排名
     */
    private BigDecimal position;

    /**
     * keyword difficulty
     */
    private Integer kd;

    /**
     * cpc
     */
    private BigDecimal cpc;

    /**
     * 搜索量
     */
    private Integer searchVolume;

    /**
     * 预测一级类目
     */
    private String cateLv1Desc;

    /**
     * 预测二级类目
     */
    private String cateLv2Desc;

    /**
     * 预测三级类目
     */
    private String cateLv3Desc;

    /**
     * 预测四级类目
     */
    private String cateLv4Desc;

    /**
     * 预测五级类目
     */
    private String cateLv5Desc;

    /**
     * 购买类得分
     */
    private Integer buyScore;

    /**
     * 服务类得分
     */
    private Integer serveScore;

    /**
     * 价格类得分
     */
    private Integer priceScore;

    /**
     * 意图类得分
     */
    private Integer intentScore;

    /**
     * 行业类得分
     */
    private Integer industyScore;

    /**
     * 地域类得分
     */
    private Integer areaScore;

    /**
     * 物流类得分
     */
    private Integer distributionScore;

    /**
     * KD得分
     */
    private Integer kdScore;

    /**
     * 关键词热度分
     */
    private Integer hotScore;

    /**
     * 创建日期
     */
    private String ds;
}
