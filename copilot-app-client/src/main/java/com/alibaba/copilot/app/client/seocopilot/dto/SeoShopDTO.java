package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class SeoShopDTO {
    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
//    private Date gmtCreate;

    /**
     * 修改时间
     */
//    private Date gmtModified;

    /**
     * shopify店铺ID
     */
    private Long shopifyShopId;

    /**
     * 鉴权访问token
     */
//    private String accessToken;

    /**
     * gsc鉴权访问refresh_token
     */
//    private String gscRefreshToken;

    /**
     * gsc email
     */
//    private String gscEmail;

    /**
     * shopify店铺对外域名
     */
    private String shopDomain;

    /**
     * shopify店铺域名
     */
    private String shopifyShopDomain;

    /**
     * 店铺名
     */
    private String shopName;

    /**
     * shopify的email
     */
    private String shopEmail;

    /**
     * shopify的email
     */
//    private Boolean fullyManagedEnabled;

    /**
     * 能够自动优化的类型
     */
//    private String autoOptimizationType;

    /**
     * 最后一次全局任务批次号
     */
//    private String batchId;

    /**
     * 状态：绑定/解绑
     */
//    private Boolean status;

    /**
     * 逻辑删除
     */
//    private Boolean deleted;

    /**
     * 扩展字段
     */
//    private SeoShopAttributes attributes = new SeoShopAttributes("{}");
}
