package com.alibaba.copilot.app.client.aiIistmaster.facade;

import com.alibaba.copilot.app.client.aiIistmaster.request.PreferenceTemplateSaveRequest;
import com.alibaba.copilot.app.client.aiIistmaster.response.PreferenceTemplateResponse;
import com.alibaba.copilot.boot.basic.result.MultiResult;
import com.alibaba.copilot.boot.basic.result.SingleResult;

/**
 * @ClassName UserPreferenceTemplateFacade
 * <AUTHOR>
 * @Date 2023/8/18 10:47
 */
public interface UserPreferenceTemplateFacade {

    /**
     * 保存用户偏好模版
     *
     * @param preferenceTemplateSaveRequest 保存模版请求
     * @return
     */
    SingleResult<Boolean> savePreferenceTemplate(PreferenceTemplateSaveRequest preferenceTemplateSaveRequest);

    /**
     * 基于用户｜类型选择获取偏好模版
     *
     * @param userId       用户id
     * @param appSource    应用来源
     * @param templateType 模版类型
     * @return
     */
    MultiResult<PreferenceTemplateResponse> queryPreferenceTemplate(Long userId, String appSource, String templateType);
}
