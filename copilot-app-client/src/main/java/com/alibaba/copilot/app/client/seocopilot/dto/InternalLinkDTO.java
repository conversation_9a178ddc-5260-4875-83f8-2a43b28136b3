package com.alibaba.copilot.app.client.seocopilot.dto;


import com.alibaba.copilot.app.client.seocopilot.constant.InternalLinkType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InternalLinkDTO {

    /**
     * 类型 Homepage Product Collection Promotion Other
     */
    private InternalLinkType type;

    /**
     * 内链
     */
    private List<LinkDTO> links;

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    public static class LinkDTO {

        /**
         * 实体ID
         */
        private Long entityId;

        /**
         * 内链url
         */
        private String url;
    }

}
