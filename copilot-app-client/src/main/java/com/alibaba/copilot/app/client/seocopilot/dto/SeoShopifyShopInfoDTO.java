package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SeoShopifyShopInfoDTO {
    /**
     * 商品列表
     */
    List<Product> products;
    /**
     * 页面列表
     */
    List<Page> pages;

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Product {
        /**
         * 标题
         */
        private String title;
        /**
         * 图片
         */
        private String image;
        /**
         * 价格
         */
        private BigDecimal price;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Page {
        /**
         * 标题
         */
        private String title;
        /**
         * 作者
         */
        private String author;
    }
}
