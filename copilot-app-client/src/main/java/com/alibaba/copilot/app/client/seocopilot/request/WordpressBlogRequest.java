package com.alibaba.copilot.app.client.seocopilot.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class WordpressBlogRequest {

    private String title;

    private String content;

    private List<Integer> tags;

    private String status = "publish";

    private List<Integer> categories;

    private Integer author;

    private String slug;

    @JSONField(name = "featured_media")
    private Integer featuredMedia;

    private Map<String,String> meta;
}
