package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AnalysisSiteDetailDTO {

    /**
     * doamin
     */
    private String domain;

    /**
     * 站点名称
     */
    private String siteTitle;

    /**
     * 站点描述
     */
    private String siteDescription;

    /**
     * 关键词列表
     */
    private List<KeywordInfoDTO> keywords = new ArrayList<>();

}



