package com.alibaba.copilot.app.client.robot.facade;

import com.alibaba.copilot.app.client.robot.dto.QuerySeoShopDTO;
import com.alibaba.copilot.app.client.robot.dto.SeoShopDTO;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.robot.dto.QueryResultDTO;

/**
 * 仅提供给内部开发测试&问题排查使用, 业务开发切勿调用
 *
 * <AUTHOR>
 * @version 2023/11/13
 */
public interface SeoRobotHsfApi {

    /**
     * 查询SEO的店铺信息
     *
     * @param dto 查询参数
     * @return 查询结果
     */
    SingleResult<QueryResultDTO<SeoShopDTO>> querySeoShop(QuerySeoShopDTO dto);
}
