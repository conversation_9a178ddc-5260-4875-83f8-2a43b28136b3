package com.alibaba.copilot.app.client.seocopilot.request;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OneshotCallbackRequest {
    private int code;
    private String message;
    private Data data;

    // getters and setters

    @Getter
    @Setter
    @Accessors(chain = true)
    public static class Data {
        private String uuid;
        private String uid;
        private String result;

    }
}
