package com.alibaba.copilot.app.client.seocopilot.response;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class SeoSysLogDetailResponse {

    /**
     * seo店铺Id
     */
    private Long seoShopId;

    /**
     * page已优化记录
     */
    private List<SeoSysLogDetailRecord> pageOptimizedList = new ArrayList<>();

    /**
     * page待优化记录
     */
    private List<SeoSysLogDetailRecord> pageToBeOptimizedList = new ArrayList<>();

    /**
     * page优化失败记录
     */
    private List<SeoSysLogDetailRecord> pageOptimizeFailList = new ArrayList<>();

    /**
     * page诊断记录
     */
    private List<SeoSysLogDetailRecord> pageCheckIssueList = new ArrayList<>();

    /**
     * blog已优化记录
     */
    private List<SeoSysLogDetailRecord> blogOptimizedList = new ArrayList<>();

    /**
     * blog待优化记录
     */
    private List<SeoSysLogDetailRecord> blogToBeOptimizedList = new ArrayList<>();

    /**
     * blog优化失败记录
     */
    private List<SeoSysLogDetailRecord> blogOptimizeFailList = new ArrayList<>();

    /**
     * blog诊断记录
     */
    private List<SeoSysLogDetailRecord> blogCheckIssueList = new ArrayList<>();

    /**
     * collection已优化记录
     */
    private List<SeoSysLogDetailRecord> collectionOptimizedList = new ArrayList<>();

    /**
     * collection待优化记录
     */
    private List<SeoSysLogDetailRecord> collectionToBeOptimizedList = new ArrayList<>();

    /**
     * collection优化失败记录
     */
    private List<SeoSysLogDetailRecord> collectionOptimizeFailList = new ArrayList<>();

    /**
     * collection诊断记录
     */
    private List<SeoSysLogDetailRecord> collectionCheckIssueList = new ArrayList<>();

    /**
     * product已优化记录
     */
    private List<SeoSysLogDetailRecord> productOptimizedList = new ArrayList<>();

    /**
     * product待优化记录
     */
    private List<SeoSysLogDetailRecord> productToBeOptimizedList = new ArrayList<>();

    /**
     * product优化失败记录
     */
    private List<SeoSysLogDetailRecord> productOptimizeFailList = new ArrayList<>();

    /**
     * product诊断记录
     */
    private List<SeoSysLogDetailRecord> productCheckIssueList = new ArrayList<>();

    /**
     * 图片压缩数
     */
    private Integer picCompressedCount = 0;
}
