package com.alibaba.copilot.app.client.langfuse;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

@Data
@Accessors(chain = true)
public class ObservationBody {

    private String id;
    private String traceId;
    private ObservationType type;
    private String name;
    private String startTime;
    private String endTime;
    private String completionStartTime;
    private String model;
    private Map<String, Object> modelParameters; // polymorphic model parameters, thus using Object
    private Object input; // 未知类型信息
    private String version = "1.0";
    private Object metadata; // 未知类型信息
    private Object output; // 未知类型信息
    private Object usage;
    private ObservationLevel level = ObservationLevel.DEBUG;
    private String statusMessage;
    private String parentObservationId;


}
