package com.alibaba.copilot.app.client.seocopilot.dto;

import com.alibaba.copilot.app.client.seocopilot.response.ShopifyArticleMetaResponse;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class SeoBlogArticleDetailDTO {
    /**
     * shopify article id
     */
    private Long id;

    /**
     * proposal id
     */
    private Long proposalId;

    /**
     * ADD任务的checkRecordId
     */
    private Long checkRecordAddId;

    /**
     * Optimize任务的checkRecordId
     */
    private Long checkRecordOptimizeId;

    /**
     * TDK任务的checkRecordId
     */
    private Long checkRecordTDKId;

    /**
     * checkRecord的Meta执行状态 status
     */
    private String checkRecordTDKStatus;

    /**
     * checkRecord的内容执行状态 status
     */
    private String checkRecordContentStatus;

    /**
     * checkRecord的整体执行状态 status
     */
    private String checkRecordStatus;

    /**
     * 博客是否为AI生成的 true false
     */
    private Boolean isAIGenerated;

    /**
     * 当TDK的checkRecord处于 GENERATING状态时，返回还剩下多少分钟
     */
    private BigDecimal minutesTDKLeft;

    /**
     * 当内容的checkRecord处于 GENERATING状态时，返回还剩下多少分钟
     */
    private BigDecimal minutesContentLeft;

    /**
     * 当整体checkRecord处于 GENERATING状态时，返回还剩下多少分钟
     */
    private BigDecimal minutesLeft;

    /**
     * 博客类型
     */
    private String articleType;


    /**
     * article标题
     */
    private String title;

    /**
     * article status
     */
    private String status;

    /**
     * 博客评分范围: bad/average/good
     */
    private String range;
    /**
     * 历史博客评分范围：bad/average/good
     */
    private String rangeHistory;
    /**
     * Optimize后的文章的评分范围：bad/average/good
     */
    private String optimizedRange;

    /**
     * 优化后的标题
     */
    private String optimizedTitle;

    /**
     * 搜索引擎优化信息
     */
    private List<ShopifyArticleMetaResponse> meta;

    /**
     * 优化后搜索引擎优化信息
     */
    private List<ShopifyArticleMetaResponse> optimizedMeta;


    /**
     * 搜索引擎优化建议
     */
    private List<String> optimizeMetaAdviceList;

    /**
     * 正文优化建议
     */
    private List<String> optimizeContentAdviceList;

    /**
     * 优化总结
     */
    private List<String> conclusionList;

    /**
     * 正文内容html
     */
    private String body;

    /**
     * 优化后的正文内容html
     */
    private String optimizedBody;

    /**
     * 使用的关键词
     */
    private List<String> usedKeywords;

    /**
     * Icon链接
     */
    private String iconUrl;


    /**
     * article内容摘要
     */
    private String summary;

    /**
     * 优化后文章内容缩略
     */
    private String optimizedSummary;

    /**
     * 博客链接
     */
    private String url;

    /**
     * 域名名称
     */
    private String domain;
    /**
     * 主图链接
     */
    private String imageLink;
}
