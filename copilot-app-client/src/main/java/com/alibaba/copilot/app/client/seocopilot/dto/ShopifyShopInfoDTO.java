package com.alibaba.copilot.app.client.seocopilot.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * ShopifyShopInfoDTO
 *
 * <AUTHOR>
 * @date 2024/7/26 2:47 下午
 */
@Data
public class ShopifyShopInfoDTO {

    /**
     * owner
     */
    @JSONField(name = "shop_owner")
    private String shopOwner;

    /**
     * 域名
     */
    private String domain;

    /**
     * name
     */
    private String name;

    /**
     * email
     */
    private String email;

    /**
     * myshopifyDomain
     */
    @J<PERSON><PERSON>ield(name = "myshopify_domain")
    private String myshopifyDomain;
}
