package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
public class SeoProductHistoryRecordDTO {
    /**
     * recordId
     */
    private Long recordId;
    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * title
     */
    private String title;

    /**
     * description
     */
    private String description;

    /**
     * handle
     */
    private String handle;

    /**
     * titleTag
     */
    private String titleTag;

    /**
     * descriptionTag
     */
    private String descriptionTag;
}
