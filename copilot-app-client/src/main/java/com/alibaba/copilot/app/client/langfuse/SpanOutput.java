package com.alibaba.copilot.app.client.langfuse;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain=true)
public class SpanOutput {
    private List<SpanOutputValue> messages;

    @Data
    @Accessors(chain=true)
    public static class SpanOutputValue {
        private String content;
        private Map<String, Object> additional_kwargs;
    }

    public void setContent(String content) {
        SpanOutputValue spanOutputValue = new SpanOutputValue().setContent(content);
        this.setMessages(new ArrayList<>());
        this.getMessages().add(spanOutputValue);
    }
}
