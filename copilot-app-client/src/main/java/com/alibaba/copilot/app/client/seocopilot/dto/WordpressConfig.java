package com.alibaba.copilot.app.client.seocopilot.dto;


import lombok.Data;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class WordpressConfig {

    /**
     * 主机
     */
    private String host;

    /**
     * Auth
     */
    private String accessToken;

    /**
     * 作者列表
     */
    private List<Integer> authors = new ArrayList<>();

    /**
     * 产品类目编号
     */
    private Map<String, Integer> websiteCategoryNo = new HashMap<>();

    /**
     * 产品域名（domain）
     */
    private Map<String, String> websiteDomain = new HashMap<>();
}
