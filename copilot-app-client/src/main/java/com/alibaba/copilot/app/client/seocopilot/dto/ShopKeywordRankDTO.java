package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

@Data
@Accessors(chain = true)
public class ShopKeywordRankDTO {
    /**
     * keyword
     */
    private String keyword;

    private double clicks;

    private double realClicks;

    private String clickUnit;

    private double impressions;

    private double realImpressions;

    private String impressionsUnit;

    private double ctr;

    private double realCtr;

    private String ctrUnit;

    private double position;

    private double realPosition;

    private String positionUnit;

}
