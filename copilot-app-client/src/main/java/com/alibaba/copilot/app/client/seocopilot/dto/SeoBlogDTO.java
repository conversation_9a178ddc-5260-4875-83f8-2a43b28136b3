package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class SeoBlogDTO {
    /**
     * 主键
     */
    private Long id;

    /**
     * Blog类目名称
     */
    private String title;

    /**
     * Blog类目路径
     */
    private String handle;

    /**
     * Blog类目标签
     */
    private String tags;

    /**
     * Blog类目创建时间
     */
    private String createdAt;
}
