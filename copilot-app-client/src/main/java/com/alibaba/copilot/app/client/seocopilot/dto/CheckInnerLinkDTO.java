package com.alibaba.copilot.app.client.seocopilot.dto;

import com.alibaba.copilot.app.client.seocopilot.constant.InternalLinkType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * blog生成，内链校验返回数据
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CheckInnerLinkDTO {

    /**
     * 内链是否有效(true表示有效，false表示无效)
     */
    private Boolean linkValid;

    /**
     * 内链url
     */
    private String link;

    /**
     * 内链类型
     */
    private InternalLinkType type;

    /**
     * 图片集合
     */
    private List<String> images;
}
