package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * blog多轮交互偏好设置数据
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SeoShopBlogPreferenceDTO {
    /**
     * blog 风格
     */
    private String blogStyle;
    /**
     * blog 类目
     */
    private String blogCategory;
    /**
     * blog 内链
     */
    private String blogLink;
    /**
     * blog 风格
     */
    private List<String> keywords;
    /**
     * 国家
     */
    private String Country;
}
