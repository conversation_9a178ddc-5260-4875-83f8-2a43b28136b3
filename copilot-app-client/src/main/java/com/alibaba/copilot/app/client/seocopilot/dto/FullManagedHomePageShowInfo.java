package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@NoArgsConstructor
@AllArgsConstructor
@Data
@Builder
@Accessors(chain = true)
public class FullManagedHomePageShowInfo {

    /**
     * shopify shop id
     */
    private Long shopifyShopId;

    /**
     * batchId
     */
    private String batchId;

    /**
     * 是否完成新手村
     */
    private Boolean isFinishFullManagedStep;

    /**
     * 店铺是否存在商品
     */
    private Boolean isHasProduct;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 是否可以关闭全托管
     */
    private Boolean isCanCloseFullManaged;

    /**
     * 还处于新手村过程中的返回值
     */
    private DuringFullManagedDTO duringFullManagedDTO;


    /**
     * 已经完成新手村的返回值
     */
    private FinishedFullManagedDTO finishedFullManagedDTO;


    /**
     * 还处于新手村过程中的返回值
     */
    @Data
    public static class DuringFullManagedDTO {

        /**
         * GSC 绑定状态
         */
        private String gscStatus;

        /**
         * 新手村步骤
         */
        private String fullManagedStep;

        /**
         * GSC绑定地址
         */
        private String gscOauthAddr;

        /**
         * score状态
         */
        private String scoreStatus;

        /**
         * index状态
         */
        private String indexStatus;

        /**
         * rank状态
         */
        private String rankStatus;

        /**
         * SEO Score-PC分数
         */
        private BigDecimal pcScore;

        /**
         * SEO Score-分数（第一次分数）
         */
        private BigDecimal firstPcScore;

        /**
         * SEO Score-相比第一次提升
         */
        private BigDecimal pcImproved;

        /**
         * SEO Score-Mobile分数
         */
        private BigDecimal mobileScore;

        /**
         * SEO Score-分数（第一次分数）
         */
        private BigDecimal firstMobileScore;

        /**
         * SEO Score-相比第一次提升
         */
        private BigDecimal mobileImproved;

        /**
         * Shop index(%)
         */
        private BigDecimal index;

        /**
         * SEO Index-分数（第一次Index）
         */
        private BigDecimal firstIndex;

        /**
         * SEO Index-相比第一次提升
         */
        private BigDecimal indexImproved;

        /**
         * Keyword rank
         */
        private BigDecimal rank;

        /**
         * SEO Rank（第一次Rank）
         */
        private BigDecimal firstRank;

        /**
         * SEO Rank-相比第一次提升
         */
        private BigDecimal rankImproved;

        /**
         * 检查项总数
         */
        private Integer totalCheckSize;

        /**
         * 当前检查进度
         */
        private Integer currentCheckSize;

        /**
         * 待优化总数
         */
        private Integer totalOptimizeSize;

        /**
         * 当前优化进度
         */
        private Integer currentOptimizeSize;

        /**
         * 上次更新时间
         */
        private String lastCheckTime;

        /**
         * 优化中各类问题进度
         */
        private List<OptimizingResult> optimizingRes;

        /**
         * 预计等待时间
         */
        private BigDecimal preEndTime;

        /**
         * 预计结束时间单位
         */
        private String preEndTimeUnit;

        /**
         * index 分子
         */
        private Integer molecule;

        /**
         * index 分母
         */
        private Integer denominator;

    }

    /**
     * 已经完成新手村的返回值
     */
    @Data
    public static class FinishedFullManagedDTO {

        /**
         * score状态
         */
        private String scoreStatus;

        /**
         * index状态
         */
        private String indexStatus;

        /**
         * rank状态
         */
        private String rankStatus;

        /**
         * SEO Score-PC分数
         */
        private BigDecimal pcScore;

        /**
         * SEO Score-分数（第一次分数）
         */
        private BigDecimal firstPcScore;

        /**
         * SEO Score-相比第一次提升
         */
        private BigDecimal pcImproved;

        /**
         * SEO Score-Mobile分数
         */
        private BigDecimal mobileScore;

        /**
         * SEO Score-分数（第一次分数）
         */
        private BigDecimal firstMobileScore;

        /**
         * SEO Score-相比第一次提升
         */
        private BigDecimal mobileImproved;

        /**
         * Shop index(%)
         */
        private BigDecimal index;

        /**
         * SEO Index-分数（第一次Index）
         */
        private BigDecimal firstIndex;

        /**
         * SEO Index-相比第一次提升
         */
        private BigDecimal indexImproved;

        /**
         * Keyword rank
         */
        private BigDecimal rank;

        /**
         * SEO Rank（第一次Rank）
         */
        private BigDecimal firstRank;

        /**
         * SEO Rank-相比第一次提升
         */
        private BigDecimal rankImproved;

        /**
         * 当前优化时间
         */
        private String currentOptimizeTime;

        /**
         * 当前优化时间
         */
        private String nextOptimizeTime;

        /**
         * optimize-success
         */
        private List<OptimizedResult> succOptimizeRes;

        /**
         * optimize-fail
         */
        private List<OptimizedResult> failOptimizeRes;

        /**
         * optimize-manual
         */
        private List<OptimizedResult> manualOptimizeRes;

        /**
         * 上次更新时间
         */
        private String lastCheckTime;

        /**
         * 优化成功总数
         */
        private Integer succTotal;

        /**
         * 日志表id
         */
        private Long logId;

        /**
         * index 分子
         */
        private Integer molecule;

        /**
         * index 分母
         */
        private Integer denominator;
    }

    /**
     * 优化结束后结果汇总（成功、失败、手工）
     */
    @Data
    public static class OptimizedResult {
        /**
         * 检查项名称
         */
        private String itemName;

        /**
         * desc
         */
        private List<String> itemDesc;

        /**
         * 类型
         */
        private String type;

        /**
         * 数量
         */
        private Integer total;

        /**
         * 相关数据
         */
        private List<Long> entityIds;

        /**
         * check type
         */
        private List<String> checkTypes;

        /**
         * record ids
         */
        private List<Long> recordIds;

        /**
         * anchor url
         */
        private String anchorUrl;
    }

    /**
     * 优化中结果汇集
     */
    @Data
    public static class OptimizingResult {
        /**
         * 检查项名称
         */
        private String itemName;

        /**
         * 需要优化的总数
         */
        private Integer totalSize;

        /**
         * 当前进度
         */
        private Integer currentOptimizedSize;
    }
}
