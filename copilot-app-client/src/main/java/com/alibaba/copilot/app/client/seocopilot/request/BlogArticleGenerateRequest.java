package com.alibaba.copilot.app.client.seocopilot.request;


import com.alibaba.copilot.app.client.seocopilot.constant.InternalLinkType;
import lombok.Data;

import java.util.List;

/**
 * blog优化request
 */
@Data
public class BlogArticleGenerateRequest {

    /**
     * seoShopId
     */
    private Long seoShopId;

    /**
     * article id
     */
    private Long articleId;
    /**
     * 话题
     */
    private String topic;
    /**
     * keywords
     */
    private List<String> keywords;
    /**
     * 风格
     */
    private String blogStyle;
    /**
     * 类目
     */
    private String blogCategory;
    /**
     * 内链
     */
    private String link;
    /**
     * 国家
     */
    private String country;
    /**
     * 标题
     */
    private String title;
    /**
     * 大纲
     */
    private String outline;
    /**
     * 优化模块
     * seoScore\seoIndex\seoRank
     */
    private String module;
    /**
     * 场景
     * editInfo\
     */
    private String scene;
    /**
     * 是否是新人引导阶段生成
     */
    private Boolean generatedByUserGuide;
    /**
     * 类型 ADD/OPTIMIZE
     */
    private String type;
    /**
     * 多轮交互多次请求唯一id
     */
    private String multiturnIdempotentToken;

    /**
     * 内链类型
     */
    private InternalLinkType linkType;

    /**
     * 内链关联的shopify实体id(eg:shopifyProductId)
     */
    private Long shopifyEntityId;

    /**
     * 是否使用商品图片作为Blog插图
     */
    private Boolean useEntityImages;

    /**
     * Blog封面图
     */
    private List<String> entityImageUrls;

    /**
     * language
     */
    private String language = "English";
}
