package com.alibaba.copilot.app.client.langfuse;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;

@Getter
public enum EventType {
    TRACE_CREATE("trace-create"),
    OBSERVATION_CREATE("observation-create"),
    GENERATION_CREATE("generation-create"),
    SCORE_CREATE("score-create"),
    SPAN_CREATE("span-create"),
    OBSERVATION_UPDATE("observation-update");

    //
    //    @JsonCreator // Jackson的注解，指示这是一个用于创建实例的工厂方法
    //    public static EventType forValue(String value) {
    //        for (EventType type : EventType.values()) {
    //            if (type.type.equals(value)) {
    //                return type;
    //            }
    //        }
    //        throw new IllegalArgumentException("Invalid type: " + value);
    //    }
    //
    //    @JsonValue // 指示Jackson序列化这个枚举项时应该使用什么值
    private final String type;

    EventType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return type;
    }
//
//    @JSONField(serialize = true)
//    public String getName() {return type;}
}
