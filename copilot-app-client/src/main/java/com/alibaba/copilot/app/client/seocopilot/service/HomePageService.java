package com.alibaba.copilot.app.client.seocopilot.service;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.client.seocopilot.dto.*;
import com.alibaba.copilot.app.client.seocopilot.request.FixBadUrlRequest;
import com.alibaba.copilot.app.client.seocopilot.request.SeoSysLogRequest;
import com.alibaba.copilot.app.client.seocopilot.response.SeoSysLogDetailResponse;
import com.alibaba.copilot.app.client.seocopilot.response.SeoSysLogResponse;
import com.alibaba.copilot.app.client.seocopilot.response.SeoUnsubInfoDTO;
import com.alibaba.copilot.boot.basic.result.SingleResult;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/9/25
 */
public interface HomePageService {

    /**
     * 首页展示结果
     */
    HomePageShowInfoDTO getHomePageShowInfo(Long shopifyShopId);

    /**
     * 首页展示结果
     */
    Boolean submitCheckTask(Long shopifyShopId, List<String> modules, Map<String, String> status, Boolean... exeShopIndex);

    /**
     * 店铺密码状态
     *
     * @param shopifyShopId
     * @return
     */
    ShopSecretStatusDTO getShopSecretStatus(Long shopifyShopId);

    String getThemeEditorAnchor(Long shopifyShopId);

    PageWrapper<ShopIndexUrlDTO> getShopIndexUrl(Long shopifyShopId, String batchId, Integer pageNum, Integer pageSize);

    PageWrapper<ShopKeywordRankDTO> getKeywordRank(Long shopifyShopId, Integer pageNum, Integer pageSize);

    /**
     * Shopify商品URL
     *
     * @param shopifyShopId
     * @return
     */
    String getShopifyProductsUrl(Long shopifyShopId);

    /**
     * 店铺信息
     *
     * @param shopifyShopId
     * @return
     */
    ShopBasicInfoDTO shopBasicInfo(Long shopifyShopId);


    /**
     * 获取店铺最简单的信息
     *
     * @param shopifyShopId
     * @return
     */
    SeoShopMsgDTO getShopSimpleMsg(Long shopifyShopId);


    /**
     * 写入宣传页已展示的标记
     *
     * @param shopifyShopId
     * @return
     */
    Boolean tagFirstAccess(Long shopifyShopId);

    /**
     * 写入二次引导结束标记
     *
     * @param shopifyShopId
     * @return
     */
    Boolean tagSecondGuide(Long shopifyShopId);

    /**
     * 开启全托管
     *
     * @param shopifyShopId
     * @return
     */
    SingleResult<String> openFullManaged(Long shopifyShopId);

    /**
     * 关闭全托管
     *
     * @param shopifyShopId
     * @return
     */
    Boolean closeFullManaged(Long shopifyShopId);

    FullManagedHomePageShowInfo getFullManagedHomePageShowInfo(Long shopifyShopId);

    /**
     * 首次提示文案确认提交
     *
     * @param shopifyShopId
     * @param remarkTag
     * @return
     */
    Boolean submitRemarkTag(Long shopifyShopId, String remarkTag);

    /**
     * 是否首次安装扫描
     *
     * @param shopifyShopId
     * @return
     */
    Boolean isFirstScanTag(Long shopifyShopId);

    /**
     * 查询日志列表
     *
     * @param seoSysLogRequest
     * @return
     */
    PageWrapper<SeoSysLogResponse> querySysLogs(SeoSysLogRequest seoSysLogRequest);

    /**
     * 查询日志详情
     *
     * @param seoSysLogRequest
     * @return
     */
    SeoSysLogDetailResponse querySysLogDetail(SeoSysLogRequest seoSysLogRequest);

    /**
     * 提交check和optimize
     *
     * @return
     */
    Boolean submitCheckAndOptimize(Long shopifyShopId);

    String test();

    /**
     * rollback theme
     */
    Boolean rollbackTheme(Long shopifyShopId);

    /**
     * 修复坏链
     *
     * @param urlMap
     * @param shopifyShopId
     * @return
     */
    Boolean fixBadUrlByMap(Map<String, String> urlMap, Long shopifyShopId);

    /**
     * 修复坏链
     *
     * @param urlList
     * @param targetUrl
     * @param shopifyShopId
     * @return
     */
    Boolean fixBadUrlByList(List<String> urlList, String targetUrl, Long shopifyShopId);

    /**
     * 查询退订展示信息
     *
     * @param shopifyShopId
     * @return
     */
    SeoUnsubInfoDTO queryInfoForUnsub(Long shopifyShopId);
}
