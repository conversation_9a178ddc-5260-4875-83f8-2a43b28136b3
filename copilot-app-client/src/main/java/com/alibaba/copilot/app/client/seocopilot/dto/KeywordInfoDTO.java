package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class KeywordInfoDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 原始数据 keyword_difficulty
     */
    private BigDecimal realDifficulty;

    /**
     * 转换单位 keyword_difficulty
     */
    private String difficulty;

    /**
     * keyword_difficulty 单位
     */
    private String difficultyUnit;

    /**
     * 原始 search_volume
     */
    private Integer realVolume;

    /**
     * 转换单位 search_volume
     */
    private String volume;

    /**
     * search_volume 单位
     */
    private String volumeUnit;

    /**
     * intent
     */
    private String intent;

    /**
     * 原始 position
     */
    private BigDecimal realPosition;

    /**
     * 转换单位 position
     */
    private String position;

    /**
     * position 单位
     */
    private String positionUnit;

    /**
     * 原始 traffic
     */
    private BigDecimal realTraffic;

    /**
     * 转换单位 traffic
     */
    private String traffic;

    /**
     * traffic 单位
     */
    private String trafficUnit;

    /**
     * 原始数据 qualityScore
     */
    private BigDecimal realScore;

    /**
     * 转换单位 qualityScore
     */
    private String score;

    /**
     * qualityScore 单位
     */
    private String scoreUnit;

    /**
     * trend
     */
    private List<BigDecimal> trends;

}



