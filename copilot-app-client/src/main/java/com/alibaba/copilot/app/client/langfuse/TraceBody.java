package com.alibaba.copilot.app.client.langfuse;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class TraceBody {
    private String id; // 可以是 null
    private String timestamp; // 可以是 null
    private String name; // 可以是 null
    private String userId; // 可以是 null
    private Object input; // 由于未知类型信息，设置为Object
    private Object output; // 由于未知类型信息，设置为Object
    private String sessionId; // 可以是 null
    private String release = "1.0"; // 可以是 null
    private String version = "1.0"; // 可以是 null
    private Object metadata; // 由于未知类型信息，设置为Object
    private List<String> tags;
    private Boolean isPublic = true; // 可以是 null

}
