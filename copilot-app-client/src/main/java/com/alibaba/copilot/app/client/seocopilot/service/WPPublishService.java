package com.alibaba.copilot.app.client.seocopilot.service;

import com.alibaba.copilot.app.client.seocopilot.dto.PublishPlatformBindStatusDTO;
import com.alibaba.copilot.app.client.seocopilot.response.GetCategoriesResponse;

public interface WPPublishService {

    Boolean wordpressOrgAuth(Long shopId, String username, String password);

    String wordpressComAuthAddr(Long shopId, String redirectUrl);

    Boolean unbindPublishPlatform(Long shopId);

    PublishPlatformBindStatusDTO bindPublishPlatformInfo(Long shopId);

    GetCategoriesResponse getCategories(Long shopId);

    GetCategoriesResponse getShopifyCategories(Long seoShopId);

    GetCategoriesResponse getShoplazzaCategories(Long seoShopId);

    String publishBlog(Long shopId, Long categoryId, Long articleId);

    String publishShopifyBlog(Long seoShopId, Long blogId, Long articleId);

    Boolean expectPlatformType(Long shopId, String platformType);

    void checkPublishPlatformStatus(Long shopId);
}
