package com.alibaba.copilot.app.client.seocopilot.request;

import com.alibaba.copilot.app.client.seocopilot.constant.InternalLinkType;
import com.alibaba.copilot.app.client.seocopilot.dto.SeoContentGenComparisonDTO;
import com.alibaba.copilot.app.client.seocopilot.dto.SeoContentGenProductDTO;
import lombok.Data;

import java.util.List;

/**
 * 内容生成request
 */
@Data
public class ContentGenRequest {

    /**
     * keywordId
     */
    private Long keywordId;

    /**
     * seoShopId
     */
    private Long seoShopId;

    /**
     * scene
     * Enum: KEYWORD\TOPIC\PRODUCT\PRODUCT_RANK\BRAND_COMPARISON\CATEGORY_COMPARISON
     * com.alibaba.copilot.app.domain.seocopilot.constant.SeoContentGenSceneEnum
     */
    private String scene;

    /**
     * keyword
     */
    private String keyword;

    /**
     * keyword
     */
    private List<String> keywords;

    /**
     * productUrl
     */
    private String productUrl;

    /**
     * productDetail
     */
    private String productDetail;

    /**
     * title
     */
    private String title;

    /**
     * outline
     */
    private String outline;

    /**
     * tone
     */
    private String tone;

    /**
     * country
     */
    private String country;

    /**
     * internalLink
     */
    private String internalLink;

    /**
     * retrievalText
     */
    private String retrievalText;

    /**
     * article id
     */
    private Long articleId;

    /**
     * 类目
     */
    private String blogCategory;

    /**
     * 优化模块
     * seoScore\seoIndex\seoRank
     */
    private String module;

    /**
     * 是否是新人引导阶段生成
     */
    private Boolean generatedByUserGuide;

    /**
     * 类型 ADD/OPTIMIZE
     */
    private String type;

    /**
     * 多轮交互多次请求唯一id
     */
    private String multiturnIdempotentToken;

    /**
     * 内链类型
     */
    private InternalLinkType linkType;

    /**
     * 内链关联的shopify实体id(eg:shopifyProductId)
     */
    private Long shopifyEntityId;

    /**
     * 是否使用商品图片作为Blog插图
     */
    private Boolean useEntityImages;

    /**
     * Blog封面图
     */
    private List<String> entityImageUrls;

    /**
     * 语种(默认语种-英语)
     */
    private String language = "English";

    /**
     * 榜单类商品
     */
    private List<SeoContentGenProductDTO> seoContentGenProductDTOs;

    /**
     * 对比类输入
     */
    private List<SeoContentGenComparisonDTO> seoContentGenComparisonDTOs;
}
