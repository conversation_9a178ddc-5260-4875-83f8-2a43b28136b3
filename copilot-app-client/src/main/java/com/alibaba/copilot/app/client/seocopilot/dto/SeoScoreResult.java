package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * SeoScoreResult
 *
 * <AUTHOR>
 * @date 2023/12/20 3:26 下午
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SeoScoreResult {

    /**
     * 仪表盘状态（诊断中/CHECKING、诊断完成/CHECKED、优化中/OPTIMIZING、优化完成/OPTIMIZED）
     */
    private String status;

    /**
     * 预计结束时间
     */
    private BigDecimal preEndTime;

    /**
     * 预计结束时间单位
     */
    private String endTimeUnit;

    /**
     * SEO Score-分数(本次)
     */
    private BigDecimal score;

    /**
     * SEO Score-上次诊断时间
     */
    private String lastCheckTime;

    /**
     * SEO Score-分数（第一次分数）
     */
    private BigDecimal firstScore;

    /**
     * SEO Score-相比第一次提升
     */
    private BigDecimal improved;

    /**
     * 问题总数
     */
    private Integer totalWaitOptimizeRecord;

    /**
     * 完成优化的项目数量（历史原因：命名暂不修改）
     */
    private Integer totalGeneratedOptimize;

    /**
     * check record
     */
    private List<SeoScoreCheckRecord> checkRecords;
}
