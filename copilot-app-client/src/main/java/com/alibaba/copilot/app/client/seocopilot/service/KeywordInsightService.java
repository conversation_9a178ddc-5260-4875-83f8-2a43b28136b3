package com.alibaba.copilot.app.client.seocopilot.service;


import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.client.seocopilot.dto.KeywordInsightDTO;
import com.alibaba.copilot.app.client.seocopilot.dto.SearchKeywordResponse;
import com.alibaba.copilot.app.client.seocopilot.request.AddKeywordRequest;
import com.alibaba.copilot.app.client.seocopilot.request.PreferenceRemoveRequest;
import com.alibaba.copilot.app.client.seocopilot.request.PreferenceUpdateRequest;

import java.util.List;

public interface KeywordInsightService {

    PageWrapper<KeywordInsightDTO> keywordInsight(String keyword, String kd, String intent,
                                                  String type, Long shopId, String language,
                                                  List<String> preferences,
                                                  Integer pageNum,
                                                  Integer pageSize);

    List<KeywordInsightDTO> topic(Integer limit, Long shopId, String language);


    Boolean preferencesUpdate(PreferenceUpdateRequest request, long shopId);

    SearchKeywordResponse search(String keyword, long shopId);

    Boolean add(AddKeywordRequest request, long shopId);

    Boolean preferenceRemove(PreferenceRemoveRequest request, long shopId);

    List<String> getParentKeywords(Long shopId);
}
