package com.alibaba.copilot.app.client.seocopilot.request;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class SeoBlogSearchRequest {

    /**
     * 按时间排序 0:从近到远 1:从远到近
     */
    Boolean timeOrder;

    /**
     * 博客类型ID
     */
    Long typeId;

    /**
     * 博客评分级别 all(传空)/notGood/average/good
     */
    String range;

    /**
     * 博客状态 all/generated/optimized/undo/proposed(待采纳)
     */
    String status;

    /**
     * 博客发布开始日期 2023-10-11
     */
    String dateStart;

    /**
     * 博客发布结束日期 2023-10-11
     */
    String dateEnd;

    /**
     * 文章是否为AI生成  true(只看AI生成的)/false(所有的)
     */
    Boolean IsAIGenerated;

    Integer pageNum;
    Integer pageSize;

    long scrollStart;
    String title;
    String keywords;

    /**
     * 语种
     */
    String language;
}
