package com.alibaba.copilot.app.client.seocopilot.dto;

import com.alibaba.copilot.app.client.seocopilot.constant.InternalLinkType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * blog多轮交互内链
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SeoShopBlogInnerLinkDTO {

    private Map<InternalLinkType, List<LinkData>> linkMap;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class LinkData {
        private String url;
        private String title;
        private Long shopifyEntityId;
    }
}
