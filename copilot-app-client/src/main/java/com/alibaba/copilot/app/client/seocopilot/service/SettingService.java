package com.alibaba.copilot.app.client.seocopilot.service;


import com.alibaba.copilot.app.client.seocopilot.constant.FullManagedModuleEnum;
import com.alibaba.copilot.app.client.seocopilot.dto.*;
import com.alibaba.copilot.app.client.seocopilot.dto.setting.AccountInfoDTO;
import com.alibaba.copilot.app.client.seocopilot.dto.setting.ShopInfoDTO;
import com.alibaba.copilot.app.client.seocopilot.dto.setting.WebsiteInfoDTO;
import com.alibaba.copilot.app.client.seocopilot.request.SaveWritingRequest;
import com.alibaba.copilot.app.client.seocopilot.request.ShopSettingDetailRequest;
import com.alibaba.copilot.app.client.seocopilot.response.GetCategoriesResponse;
import com.alibaba.copilot.app.client.seocopilot.response.GetWritingResponse;
import com.alibaba.copilot.boot.basic.result.SingleResult;

import java.util.List;

public interface SettingService {

    /**
     * 获取店铺设置详情
     */
    ShopSettingDetailDTO getShopSettingDetail(Long shopifyShopId);

    /**
     * 获取品牌风格
     *
     * @param shopifyShopId
     * @return
     */
    List<BrandStyleDTO> getBrandStyles(Long shopifyShopId);

    /**
     * 保存店铺设置
     */
    Boolean saveShopSettingDetail(ShopSettingDetailRequest request, Long shopifyShopId);


    /**
     * 解绑定GSC
     */
    Boolean unbindGscAccount(Long shopifyShopId);

    /**
     * 获取商品列表
     */
    List<ProductSimpleDTO> getProducts(Long shopifyShopId);

    /**
     * 获取商品列表
     */
    List<CollectionSimpleDTO> getCollections(Long shopifyShopId);

    /**
     * 获取时区列表
     */
    List<String> getAllTimeZones();

    /**
     * 获取配置化数据
     */
    ConfigSettingDataDTO getConfigSettingData();

    /**
     * 获取店铺关键词（不存在返回为空）
     */
    List<String> getShopKeywords(Long shopId);

    /**
     * 获取店铺设置的目标国家（不存在返回为空）
     */
    String getOneShopTargetCountry(Long shopId);

    /**
     * 查询全托管模块
     */
    List<FullManagedModuleDTO> getAutoOptimizationType(Long shopifyShopId);

    Boolean containFullManagedModule(Long shopifyShopId, FullManagedModuleEnum module);

    Integer getModuleOptFrequency(Long shopifyShopId, FullManagedModuleEnum module);

    /**
     * 保存全托管模块
     */
    Boolean saveAutoOptimizationType(Long shopifyShopId, List<FullManagedModuleDTO> fullManagedModules);

    /**
     * 查询店铺信息
     */
    ShopInfoDTO getShopInfo(Long shopId);

    /**
     * 更新店铺详情
     */
    Boolean updateShopInfo(ShopInfoDTO shopInfoDTO, Long shopId);

    /**
     * 查询账号信息
     */
    AccountInfoDTO getAccountInfo(Long userId, Long shopId);


    /**
     * 查询网站信息
     */
    WebsiteInfoDTO getWebsiteInfo(Long shopId);

    /**
     * 更新网站信息
     */
    Boolean updateWebsiteInfo(WebsiteInfoDTO websiteInfoDTO, Long shopId);

    /**
     * 查询竞对列表
     */
    List<CompetitiveLinkDTO> getCompetitors(Long shopId);

    /**
     * 添加竞对
     */
    SingleResult<Boolean> addCompetitor(CompetitiveLinkDTO competitor, Long shopId);

    /**
     * 删除竞对
     */
    Boolean deleteCompetitor(CompetitiveLinkDTO competitor, Long shopId);

    /**
     * 更新竞对
     */
    Boolean updateCompetitors(CompetitiveLinkDTO competitor, Long shopId);

    Long getShopId(Long userId);

    /**
     * 提交sitemap
     *
     * @param shopifyShopId
     * @return
     */
    Boolean submitSitemap(Long shopifyShopId);

    /**
     * 关闭提交sitemap
     *
     * @param shopifyShopId
     * @return
     */
    Boolean closeSubmitSitemap(Long shopifyShopId);


    /**
     * 查询shopify店铺信息
     *
     * @param shopifyShopId
     * @return
     */
    ShopifyShopInfoDTO getShopifyShopInfo(Long shopifyShopId);

    /**
     * 权限升级url
     */
    String shopifyEscalatingPrivileges(Long shopifyShopId);

    /**
     * 是否需要升级
     */
    Boolean isNeedEscalatingPrivileges(Long shopifyShopId);

    /**
     * 获取类目
     *
     * @param shopifyShopId
     * @return
     */
    GetCategoriesResponse getShopifyCategories(Long shopifyShopId);

    /**
     * 创建默认Setting
     */

    Boolean createDefaultSetting(Long shopId);

    Boolean saveWriting(Long seoShopId, SaveWritingRequest request);

    Boolean saveWriting4Shopify(Long shopifyShopId, SaveWritingRequest request);

    GetWritingResponse getWriting(Long seoShopId);

    GetWritingResponse getWriting4Shopify(Long shopifyShopId);
}
