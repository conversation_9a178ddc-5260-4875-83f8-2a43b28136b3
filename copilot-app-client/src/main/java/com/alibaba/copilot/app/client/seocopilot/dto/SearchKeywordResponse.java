package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SearchKeywordResponse {

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 原始数据 keyword_difficulty
     */
    private BigDecimal realKeywordDifficulty;

    /**
     * 转换单位 keyword_difficulty
     */
    private BigDecimal keywordDifficulty;

    /**
     * keyword_difficulty 单位
     */
    private String keywordDifficultyUnit;

    /**
     * 原始 search_volume
     */
    private Integer realSearchVolume;

    /**
     * 转换单位 search_volume
     */
    private String searchVolume;

    /**
     * search_volume 单位
     */
    private String searchVolumeUnit;

    /**
     * intent
     */
    private String intent;

    /**
     * 原始数据 qualityScore
     */
    private BigDecimal realQualityScore;

    /**
     * 转换单位 qualityScore
     */
    private BigDecimal qualityScore;

    /**
     * qualityScore 单位
     */
    private String qualityScoreUnit;

    /**
     * 权益总数
     */
    private Long totalQuata;

    /**
     * 剩余权益
     */
    private Long remainQuata;
}



