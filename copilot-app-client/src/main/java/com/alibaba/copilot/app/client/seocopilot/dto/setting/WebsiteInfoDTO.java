package com.alibaba.copilot.app.client.seocopilot.dto.setting;

import com.alibaba.copilot.app.client.seocopilot.dto.BrandStyleDTO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Author: mianyun.yt
 * @Date: 2024/3/20
 */
@Data
public class WebsiteInfoDTO {

    /**
     * shopId
     */
    private Long shopId;

    /**
     * 目标市场
     */
    private List<String> targetCountry;

    /**
     * 目标人群
     */
    private List<String> targetCustomer;

    /**
     * 转换目标
     */
    private List<String> conversionGoals;

    /**
     * 品牌风格
     */
    private List<BrandStyleDTO> brandStyles;

    /**
     * 主营行业
     */
    private List<String> mainIndustries;

    /**
     * 店铺级关键词
     */
    private List<String> shopKeyword;

    /**
     * 店铺级博客分类
     */
    private List<String> blogCategory;

    /**
     * 品牌颜色
     */
    private String brandColor;

    /**
     * slogan
     */
    private String slogan;

    /**
     * 卖点
     */
    private String sellingPoint;

    /**
     * 计算当前Setting配置分数
     */
    private BigDecimal calculateScore;

    /**
     * 语言设置
     */
    private List<String> languages;

}
