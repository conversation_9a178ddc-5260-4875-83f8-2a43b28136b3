package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 优化标题
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SeoBlogArticleOptimizedTitleDTO {
    /**
     * 优化后的标题
     */
    private String optimizeTitle;
    /**
     * 标签（暂时写死固定）
     */
    private String tag = "Based on information";

    /**
     * 是否重复
     */
    private Boolean isRepeatability;
}
