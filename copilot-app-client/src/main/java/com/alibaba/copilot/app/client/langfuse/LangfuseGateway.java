package com.alibaba.copilot.app.client.langfuse;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;

//@Service
@Slf4j
public class LangfuseGateway {

    private static final OkHttpClient client = new OkHttpClient();

//    private final String ingestionUrl = "https://us.cloud.langfuse.com/api/public/ingestion";
    private final String ingestionUrl;

//    @Value("${langfuse.username}")
    private final String username;
//    @Value("${langfuse.password}")
    private final String password;

    public LangfuseGateway(String ingestionUrl, String username, String password) {
        if (ingestionUrl == null) {
            ingestionUrl = "https://langfuse.alibaba-inc.com/api/public/ingestion";
        }
        this.ingestionUrl = ingestionUrl;
        this.username = username;
        this.password = password;
    }


    public void BatchIngestionCreateWithBody(List<TraceBody> traceBodyList, List<ObservationBody> observationBodyList, List<SpanBody> spanBodyList, List<GenerationBody> generationBodyList) {
        if (CollectionUtils.isEmpty(traceBodyList) || traceBodyList.get(0).getId() == null) {
            log.error("LangfuseGateway.BatchIngestionCreateWithBody body is null or traceId is null.");
            return;
        }
        IngestionBatchRequest<?> request = new IngestionBatchRequest<>();
        List<IngestionBatchRequest.IngestionRequest<?>> items = new ArrayList<>();
        for (TraceBody traceBody : traceBodyList) {
            IngestionBatchRequest.IngestionRequest<TraceBody> traceBodyIngestionRequest = new IngestionBatchRequest.IngestionRequest<TraceBody>()
                    .setBody(traceBody)
                    .setType(EventType.TRACE_CREATE.getType())
                    .setId(traceBody.getId())
                    .setTimestamp(traceBody.getTimestamp());
            items.add(traceBodyIngestionRequest);
        }
        if (!CollectionUtils.isEmpty(observationBodyList)) {
            for (ObservationBody observationBody : observationBodyList) {
                IngestionBatchRequest.IngestionRequest<ObservationBody> observationBodyIngestionRequest = new IngestionBatchRequest.IngestionRequest<ObservationBody>()
                        .setBody(observationBody)
                        .setType(EventType.OBSERVATION_CREATE.getType())
                        .setId(observationBody.getId())
                        .setTimestamp(observationBody.getStartTime());
                items.add(observationBodyIngestionRequest);
            }
        }
        if (!CollectionUtils.isEmpty(spanBodyList)) {
            for (SpanBody spanBody : spanBodyList) {
                IngestionBatchRequest.IngestionRequest<SpanBody> spanBodyIngestionRequest = new IngestionBatchRequest.IngestionRequest<SpanBody>()
                        .setBody(spanBody)
                        .setType(EventType.SPAN_CREATE.getType())
                        .setId(spanBody.getId())
                        .setTimestamp(spanBody.getStartTime());
                items.add(spanBodyIngestionRequest);
            }
        }
        if (!CollectionUtils.isEmpty(generationBodyList)) {
            for (GenerationBody generationBody : generationBodyList) {
                IngestionBatchRequest.IngestionRequest<GenerationBody> generationBodyIngestionRequest = new IngestionBatchRequest.IngestionRequest<GenerationBody>()
                        .setBody(generationBody)
                        .setType(EventType.GENERATION_CREATE.getType())
                        .setId(generationBody.getId())
                        .setTimestamp(generationBody.getStartTime());
                items.add(generationBodyIngestionRequest);
            }
        }
        request.setBatch(items);
        triggerLangfuseIngestionApi(request);
    }

    //BatchIngestionScoreCreate
    public void BatchIngestionScoreCreate(IngestionBatchRequest<ScoreBody> ingestionBatchRequest) {
        log.info("LangfuseGateway.BatchIngestionScoreCreate request:{}", JSON.toJSONString(ingestionBatchRequest));
        if (CollectionUtils.isEmpty(ingestionBatchRequest.getBatch())) {
            return;
        }
        ingestionBatchRequest.getBatch().forEach(w -> w.setType(EventType.SCORE_CREATE.getType()));
        triggerLangfuseIngestionApi(ingestionBatchRequest);
    }


    private String encodeCredentialsForBasicAuth() {
        String credentials = username + ":" + password;
        return Base64.getEncoder().encodeToString(credentials.getBytes(StandardCharsets.UTF_8));
    }

    private void triggerLangfuseIngestionApi(IngestionBatchRequest<?> ingestionBatchRequest) {
        MediaType mediaType = MediaType.parse("application/json; charset=utf-8");
        RequestBody body = RequestBody.create(mediaType, JSON.toJSONString(ingestionBatchRequest, SerializerFeature.DisableCircularReferenceDetect));
        Request request = new Request.Builder()
                .url(ingestionUrl)
                .header("Content-Type", "application/json")
                .header("Authorization", "Basic " + encodeCredentialsForBasicAuth())
                .post(body)
                .build();
        try (Response response = client.newCall(request).execute()) {
            log.info("triggerLangfuseIngestionApi request:{} res:{}", JSON.toJSONString(ingestionBatchRequest, SerializerFeature.DisableCircularReferenceDetect), response);
            if (!response.isSuccessful()) {
//                throw new IOException("Unexpected code: " + response);
                log.error("triggerLangfuseIngestionApi failed:{}", response);
            }
        } catch (IOException e) {
            log.error("triggerLangfuseIngestionApi failed", e);
        }
    }


}
