package com.alibaba.copilot.app.client.seocopilot.service;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.client.seocopilot.dto.SeoKeywordDTO;
import com.alibaba.copilot.app.client.seocopilot.request.KeywordSearchRequest;

import java.util.List;

public interface KeywordService {
    /**
     * 复式查询和转换
     */
    PageWrapper<SeoKeywordDTO> getSeoKeywordList(KeywordSearchRequest request);

    List<SeoKeywordDTO> getSeoKeywordTop(String keyword);

    List<SeoKeywordDTO> getSeoKeywordTopV2(String keyword, int count);
}
