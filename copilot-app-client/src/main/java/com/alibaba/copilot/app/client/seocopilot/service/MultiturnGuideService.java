package com.alibaba.copilot.app.client.seocopilot.service;

import com.alibaba.copilot.app.client.seocopilot.constant.InternalLinkType;
import com.alibaba.copilot.app.client.seocopilot.dto.*;
import com.alibaba.copilot.app.client.seocopilot.request.BlogArticleGenerateRequest;
import com.alibaba.copilot.app.client.seocopilot.request.BlogGuidePreferenceRequest;
import com.alibaba.copilot.boot.basic.result.MultiResult;
import com.alibaba.copilot.boot.basic.result.SingleResult;

import java.util.List;

public interface MultiturnGuideService {

    /**
     * 获取AE国家编码
     */
    List<SystemCountryDTO> getAeCountryCodeList();

    /**
     * 获取店铺多轮交互关键词页面偏好设置数据
     */
    SeoShopBlogPreferenceDTO getUserPreference(Long shopifyShopId);

    /**
     * 保存店铺多轮交互关键词页面偏好设置数据
     */
    void saveUserPreference(Long shopifyShopId, BlogGuidePreferenceRequest request);

    /**
     * 检查是否shopify内链
     */
    Boolean checkShopifyInnerLink(Long shopifyShopId, String link);

    /**
     * 检查product是否存在
     */
    Boolean checkShopProductExist(Long shopifyShopId);

    /**
     * 生成一次多轮交互唯一id
     *
     * @param shopifyShopId
     * @return
     */
    SingleResult<String> genMultiturnIdempotentToken(Long shopifyShopId);

    /**
     * 生成标题
     */
    MultiResult<SeoBlogArticleOptimizedTitleDTO> generateTitle(Long shopifyShopId, BlogArticleGenerateRequest request);

    /**
     * 生成标题
     */
    MultiResult<SeoBlogArticleOptimizedTitleDTO> generateTitleByKeywordAndTopic(Long shopifyShopId, BlogArticleGenerateRequest request);

    /**
     * 生成标题
     */
    SingleResult<String> generateOutline(Long shopifyShopId, BlogArticleGenerateRequest request);

    /**
     * 异步生成内容
     */
    SingleResult<Boolean> generateContent(Long shopifyShopId, BlogArticleGenerateRequest request);

    /**
     * 异步生成内容
     */
    SingleResult<String> regenerateContent(Long shopifyShopId, Long articleId, String keywords);

    /**
     * 获取博客类目
     */
    List<String> getBlogCategory(Long shopifyShopId);

    /**
     * 获取推荐的关键词
     */
    List<RecommendKeywordDTO> getRecommendKeywords(Long shopifyShopId, String userInput);

    /**
     * 检查是否可用
     */
    boolean checkCanUsedRecommendKeywords(Long shopifyShopId);

    /**
     * 构造未订阅升级套餐的result
     */
    MultiResult buildNoSubscribedUpgradePlanResult();

    Boolean titleRepeatabilityCheck(String title,Long shopifyShopId);

    SeoShopBlogInnerLinkDTO getInnerLinks(Long shopifyShopId,String title);

    CheckInnerLinkDTO checkInnerLink(Long shopifyShopId, String link, InternalLinkType type, Long shopifyEntityId);

    Boolean blogImageWhite(Long shopifyShopId);
}
