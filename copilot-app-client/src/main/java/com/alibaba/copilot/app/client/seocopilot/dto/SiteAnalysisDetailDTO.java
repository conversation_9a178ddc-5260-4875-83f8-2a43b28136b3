package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SiteAnalysisDetailDTO {

    /**
     * 用户直接输入
     */
    private String userInput;

    /**
     * 是否是url
     */
    private Boolean isUrl = false;

    /**
     * doamin
     */
    private String domain;

    /**
     * 站点名称
     */
    private String siteTitle;

    /**
     * 站点描述
     */
    private String siteDescription;

    /**
     * 关键词列表
     */
    private List<KeywordInfo> keywords = new ArrayList<>();

    /**
     * 关键词是否是配置化数据（如果true表示从semrush获取数据失败）
     */
    private Boolean isConfigKeyword = false;

    /**
     * 竞对信息
     */
    private List<CompetitorInfo> competitors = new ArrayList<>();

    @Data
    public static class KeywordInfo {
        /**
         * keyword
         */
        private String keyword;

        /**
         * keyword_difficulty
         */
        private BigDecimal keywordDifficulty;

        /**
         * 原始 search_volume
         */
        private Integer realSearchVolume;

        /**
         * 转换单位 search_volume
         */
        private String searchVolume;

        /**
         * search_volume 单位
         */
        private String searchVolumeUnit;


        /**
         * trend
         */
        private List<BigDecimal> trends;
    }

    @Data
    public static class CompetitorInfo {

        /**
         * domain
         */
        private String competitorDomain;

        /**
         * keyword
         */
        private List<String> keywords;
    }

}



