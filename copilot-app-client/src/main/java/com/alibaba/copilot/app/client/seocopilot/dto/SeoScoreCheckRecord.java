package com.alibaba.copilot.app.client.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * SeoScoreCheckRecord
 *
 * <AUTHOR>
 * @date 2023/12/20 3:26 下午
 */
@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SeoScoreCheckRecord {

    /**
     * 被诊断的实体ID
     */
    private Long entityId;

    /**
     * 被诊断的实体类型，参看: CheckEntityType
     */
    private String entityType;

    /**
     * 检查项名称
     */
    private String itemName;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态（诊断项目的状态）（问题未优化/WAIT_OPTIMIZE、优化建议生成中/GENERATING_PROPOSAL、优化建议已生成/GENERATED_PROPOSAL、问题已优化/FINISH_OPTIMIZE）
     */
    private String status;

    /**
     * 是否可以自动优化
     */
    private Boolean isAutoOptimization;

    /**
     * 手工优化建议
     */
    private String manualOptimizeSuggestion;

    /**
     * 该类问题总数
     */
    private Integer total;

    /**
     * 完成优化的数量
     */
    private Integer optimizedTotal;

    /**
     * 问题权重
     */
    private BigDecimal weight;

    /**
     * taskId
     */
    private Long taskId;

    /**
     * checkType
     */
    private String checkType;

    /**
     * recordIds
     */
    private List<Long> recordIds;
}
