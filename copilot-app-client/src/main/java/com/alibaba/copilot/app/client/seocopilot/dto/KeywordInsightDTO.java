package com.alibaba.copilot.app.client.seocopilot.dto;

import com.alibaba.copilot.app.client.seocopilot.constant.ShopKeywordPreference;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class KeywordInsightDTO {

    /**
     * id
     */
    private Long id;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 原始数据 keyword_difficulty
     */
    private BigDecimal realKeywordDifficulty;

    /**
     * 转换单位 keyword_difficulty
     */
    private BigDecimal keywordDifficulty;

    /**
     * keyword_difficulty 单位
     */
    private String keywordDifficultyUnit;

    /**
     * 原始 search_volume
     */
    private Integer realSearchVolume;

    /**
     * 转换单位 search_volume
     */
    private String searchVolume;

    /**
     * search_volume 单位
     */
    private String searchVolumeUnit;

    /**
     * intent
     */
    private String intent;

    /**
     * 原始 position
     */
    private BigDecimal realPosition;

    /**
     * 转换单位 position
     */
    private String position;

    /**
     * position 单位
     */
    private String positionUnit;

    /**
     * 原始 traffic
     */
    private BigDecimal realTraffic;

    /**
     * 转换单位 traffic
     */
    private BigDecimal traffic;

    /**
     * traffic 单位
     */
    private String trafficUnit;

    /**
     * category
     */
    private String category;

    /**
     * 原始数据 qualityScore
     */
    private BigDecimal realQualityScore;

    /**
     * 转换单位 qualityScore
     */
    private BigDecimal qualityScore;

    /**
     * qualityScore 单位
     */
    private String qualityScoreUnit;

    /**
     * original keyword
     */
    private String originalKeyword;

    /**
     * 最近更新时间
     */
    private Date gmtModified;

    /**
     * preference
     */
    private ShopKeywordPreference preference;
}



