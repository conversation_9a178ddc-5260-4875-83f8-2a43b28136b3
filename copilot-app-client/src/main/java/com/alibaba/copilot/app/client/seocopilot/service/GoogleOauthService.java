package com.alibaba.copilot.app.client.seocopilot.service;

import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Date 2023/9/25
 */
public interface GoogleOauthService {

    /**
     * Google authCallback
     *
     * @return
     */
    Boolean callback(Long shopifyShopId, String code, HttpServletResponse response, String seoCopilotUrl);

    /**
     * Google ga Callback
     *
     * @return
     */
    Boolean gaCallback(Long shopId, String code, HttpServletResponse response, String seoCopilotUrl);

    /**
     * Get Google Access Token
     *
     * @return
     */
    String getAccessToken(Long shopifyShopId);

    /**
     * Get Google Analytics Access Token
     *
     * @return
     */
    String getGaAccessToken(Long shopId);

    /**
     * Get Google Oauth Addr
     *
     * @return
     */
    String getGoogleOauthAddr(Long shopifyShopId);

    /**
     * Get Google Oauth Addr
     *
     * @return
     */
    String getGoogleOauthAddrByRedirectUri(Long shopifyShopId,String redirectUri);

    /**
     * Get Google analytics oauth Addr
     *
     * @return
     */
    String getGoogleAnalyticsOauthAddrByRedirectUri(Long shopId,String redirectUri);

    /**
     * 校验GSC token的是否生效的问题
     */
    Boolean verifyAccessToken(Long shopifyShopId, String shopDomain, Integer maxRetry);

    /**
     * 校验GA propertiesId是否有效
     */
    Boolean verifyGaPropertiesId(Long shopId, String propertiesId, Integer maxRetry);
}
