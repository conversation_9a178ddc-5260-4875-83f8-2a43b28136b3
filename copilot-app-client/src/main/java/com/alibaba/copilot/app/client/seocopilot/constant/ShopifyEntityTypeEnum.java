package com.alibaba.copilot.app.client.seocopilot.constant;

import com.alibaba.copilot.boot.basic.enums.IEnum;
import com.google.common.collect.Lists;
import lombok.Getter;

import java.util.List;

/**
 * Shopify实体类型
 */
@Getter
public enum ShopifyEntityTypeEnum implements IEnum<ShopifyEntityTypeEnum> {

    /**
     * PRODUCT
     */
    PRODUCT,

    /**
     * BLOG
     */
    BLOG,

    /**
     * COLLECTION
     */
    COLLECTION;

    public static String getSeoJobType(ShopifyEntityTypeEnum entityTypeEnum) {
        return entityTypeEnum.name() + "_SYNC";
    }

    public static List<String> getSeoJobTypeList(){
        List<String> result = Lists.newArrayList();
        ShopifyEntityTypeEnum[] values = ShopifyEntityTypeEnum.values();
        for (ShopifyEntityTypeEnum value : values) {
            if(value == null){
                continue;
            }

            result.add(getSeoJobType(value));
        }

        return result;
    }
}
