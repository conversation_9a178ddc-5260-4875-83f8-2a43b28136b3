package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.app.domain.seocopilot.constant.CompressStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SeoImage {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * shopify店铺ID
     */
    private Long shopId;

    /**
     * 批次id
     */
    private String batchId;

    /**
     * shopify_image_id
     */
    private String shopifyImageId;

    /**
     * shopify_image_url
     */
    private String shopifyImageUrl;

    /**
     * 压缩前的OSS url
     */
    private String preCompressOssUrl;

    /**
     * 压缩前图片大小（KB）
     */
    private Integer preCompressSize;

    /**
     * 压缩状态
     */
    private CompressStatus compressStatus;

    /**
     * 压缩后的OSS url
     */
    private String afterCompressOssUrl;

    /**
     * 压缩后图片大小（KB）
     */
    private Integer afterCompressSize;

    /**
     * 逻辑删除
     */
    private Boolean deleted;
}
