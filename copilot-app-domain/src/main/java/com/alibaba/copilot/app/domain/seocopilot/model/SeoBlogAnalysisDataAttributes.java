package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.boot.basic.data.Attributes;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SeoBlogAnalysisDataAttributes extends Attributes {

    public SeoBlogAnalysisDataAttributes(String json) {
        super(json);
    }

    private static final String APPLY_SHOPIFY_DATE = "applyShopifyDate";

    private static final String GSC_INDEX_DATE = "gscIndexDate";

    public String getApplyShopifyDate() {
        return get(APPLY_SHOPIFY_DATE, String.class);
    }

    public void setApplyShopifyDate(String applyShopifyDate) {
        put(APPLY_SHOPIFY_DATE, applyShopifyDate);
    }

    public String getGscIndexDate() {
        return get(GSC_INDEX_DATE, String.class);
    }

    public void setGscIndexDate(String gscIndexDate) {
        put(GSC_INDEX_DATE, gscIndexDate);
    }
}
