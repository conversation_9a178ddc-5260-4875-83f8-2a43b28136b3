package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.client.seocopilot.constant.CrawlJobStatusEnum;
import com.alibaba.copilot.app.client.seocopilot.constant.CrawlJobType;
import com.alibaba.copilot.app.domain.seocopilot.model.CrawlJob;

import java.util.List;

public interface CrawlJobRepository {

    /**
     * 更新任务心跳
     *
     * @param jobId
     */
    boolean updateTaskHeartbeatDate(Long jobId);

    /**
     * 更新任务状态
     *
     * @param job
     * @param newStatus
     */
    boolean updateTaskStatus(CrawlJob job, CrawlJobStatusEnum newStatus);

    /**
     * 获取失去心跳的任务
     *
     * @param size
     * @return
     */
    List<CrawlJob> getLoseHeartbeatTasks(Integer size);

    /**
     * 获取可执行的任务
     *
     * @return
     */
    CrawlJob getExecutableTask(CrawlJobType jobType);

    /**
     * 获取待重新的任务
     *
     * @param size
     * @return
     */
    List<CrawlJob> getNeedRetryTasks(Integer size);

    /**
     * 保存任务
     */
    Boolean saveJob(CrawlJob crawlJob);

    /**
     * 根据ID获取
     */
    CrawlJob getById(Long id);

}
