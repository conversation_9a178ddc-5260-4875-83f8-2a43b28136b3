package com.alibaba.copilot.app.domain.aynctask.model;

import java.util.Arrays;
import java.util.List;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-06-26
 **/
public enum AsyncTaskStatus {
    /**
     * 初始状态
     */
    INITIAL,
    /**
     * 待执行
     */
    PENDING,
    /**
     * 执行中
     */
    RUNNING,
    /**
     * 任务过期(未执行)
     */
    EXPIRED,
    /**
     * 执行失败(可重试)
     */
    FAILURE,
    /**
     * 任务悬挂(可重试)
     */
    SUSPEND,
    /**
     * 任务中断(不可重试)
     */
    INTERRUPT,
    /**
     * 执行成功
     */
    SUCCESS;

    /**
     * 异步任务的结束状态
     */
    private static final List<AsyncTaskStatus> ASYNC_TASK_COMPLETED_STATUSES = Arrays.asList(
            AsyncTaskStatus.SUCCESS,
            AsyncTaskStatus.FAILURE,
            AsyncTaskStatus.EXPIRED,
            AsyncTaskStatus.INTERRUPT
    );

    /**
     * 判断当前任务状态是否结束
     */
    public boolean isCompleted() {
        return ASYNC_TASK_COMPLETED_STATUSES.contains(this);
    }

    public static AsyncTaskStatus of(String status) {
        for (AsyncTaskStatus s : AsyncTaskStatus.values()) {
            if (s.name().equals(status)) {
                return s;
            }
        }
        return null;
    }
}
