package com.alibaba.copilot.app.domain.base.request.security;

import lombok.Getter;
import lombok.Setter;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-12-08
 **/
@Setter
@Getter
public class CheckTextRiskRequest {
    public static final String LANGUAGE_EN = "EN";

    /**
     * 检查的内容
     */
    private String text;
    /**
     * 文本语言
     */
    private String language;
    /**
     * 外部唯一ID（业务定义）
     */
    private String outerID;
    /**
     * 会话ID
     */
    private String conversationId;

    /**
     * 获取内容的MD5
     * @return
     */
    public String getTextMd5() {
        if(StringUtils.isBlank(text)) {
            return text;
        }

        return DigestUtils.md5Hex(text);
    }
}
