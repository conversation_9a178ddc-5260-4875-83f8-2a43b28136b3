package com.alibaba.copilot.app.domain.seocopilot.request;

import lombok.Data;

import java.util.List;

@Data
public class CollectionOptimizeRequest {
    /**
     * shopifyCollectionId
     */
    private Long shopifyCollectionId;
    /**
     * 应用最新的proposal
     */
    private Boolean applyNewest = false;
    /**
     * 标题
     */
    private TitleData title;
    /**
     * 详情
     */
    private DescriptionData description;
    /**
     * TDK
     */
    private SearchAppearanceData searchAppearance;
    /**
     * 图片
     */
    private ImagesData images;

    public interface RequestData {
        Long getId();
    }

    @Data
    public static class TitleData implements RequestData {
        /**
         * proposal id
         */
        private Long id;
        /**
         * 优化数据
         */
        private OptimizeData optimize;
    }

    @Data
    public static class DescriptionData implements RequestData {
        /**
         * proposal id
         */
        private Long id;
        /**
         * 优化数据
         */
        private OptimizeData optimize;
    }

    @Data
    public static class SearchAppearanceData implements RequestData {
        /**
         * proposal id
         */
        private Long id;
        /**
         * 优化数据
         */
        private OptimizeData optimize;
    }

    @Data
    public static class ImagesData implements RequestData {
        /**
         * proposal id
         */
        private Long id;
        /**
         * desc proposal id
         */
        private Long descriptionId;
        /**
         * 优化数据
         */
        private List<OptimizeImageData> optimize;
    }

    @Data
    public static class OptimizeData {
        /**
         * 标题
         */
        private String title;
        /**
         * 详情
         */
        private String description;
        /**
         * 标题
         */
        private String titleTag;
        /**
         * 详情
         */
        private String metaDescription;
        /**
         * url
         */
        private String url;
    }

    @Data
    public static class OptimizeImageData {
        /**
         * proposal id
         */
        private Long id;
        /**
         * URL
         */
        private String url;
        /**
         * alt标签
         */
        private String alt;
        /**
         * 格式
         */
        private String format;
        /**
         * 大小
         */
        private Integer size;
    }
}
