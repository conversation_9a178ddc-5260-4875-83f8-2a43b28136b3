package com.alibaba.copilot.app.domain.seocopilot.dto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class DashboardUserDailyTaskInfoDTO {
    /**
     * 是否已订阅付费
     */
    private Boolean subscribed;
    /**
     * 今天是否已生成博客
     */
    private Boolean blogGeneratedToday;
    /**
     * setting信息是否已完整
     */
    private Boolean settingCompleted;
    /**
     * 是否绑定了域名
     */
    private Boolean domainBound;
    /**
     * 是否绑定了gsc
     */
    private Boolean gscValid;
    /**
     * 是否绑定了ga
     */
    private Boolean gaValid;

    /**
     * 是否完成竞对的设置
     */
    private Boolean competitorSettingCompleted;

}
