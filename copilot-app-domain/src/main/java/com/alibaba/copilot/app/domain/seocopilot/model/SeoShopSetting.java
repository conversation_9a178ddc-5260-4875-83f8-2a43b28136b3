package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.app.client.seocopilot.constant.InternalLinkStrategyEnum;
import com.alibaba.copilot.app.client.seocopilot.constant.PageTypeEnum;
import com.alibaba.copilot.app.client.seocopilot.dto.BlogCategoryApplyDTO;
import com.alibaba.copilot.app.client.seocopilot.dto.FullManagedModuleDTO;
import com.alibaba.copilot.app.domain.seocopilot.constant.LanguageEnum;
import com.google.common.collect.Lists;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * SeoShopSetting 实体
 */
@Slf4j
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SeoShopSetting {

    public static final List<String> includedFields = Lists.newArrayList(
            "targetCountry",
            "targetCustomer",
            "conversionGoal",
            "brandStyle",
            "mainIndustry",
            "shopKeyword",
            "internalLinkStrategy");

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * seo店铺ID
     */
    private Long shopId;

    /**
     * 是否同意发送邮件
     */
    private Boolean canSendEmail;

    /**
     * 全托管-内容发布频率（每天）
     */
    private Integer contentPublishFrequency;

    /**
     * 时区
     */
    private String timeZone;

    /**
     * 全托管-任务执行时间
     */
    private String taskExectuteTime;

    /**
     * 图片压缩配置
     */
    private List<PageTypeEnum> autoCompressImage;

    /**
     * 图片格式转换配置
     */
    private List<PageTypeEnum> autoConvertImage;

    /**
     * 目标市场
     */
    private List<String> targetCountry;

    /**
     * 排除市场
     */
    private List<String> excludeCountry;

    /**
     * 目标人群
     */
    private List<String> targetCustomer;

    /**
     * 品牌风格
     */
    private List<BrandStyle> brandStyle;

    /**
     * 转换目标
     */
    private List<String> conversionGoal;

    /**
     * 主营行业
     */
    private List<String> mainIndustry;

    /**
     * 店铺级关键词
     */
    private List<String> shopKeyword;

    /**
     * 内链策略（average、customize）
     */
    private InternalLinkStrategyEnum internalLinkStrategy;

    /**
     * 内链
     */
    private List<InternalLink> internalLink;

    /**
     * 竞对内链
     */
    private List<CompetitiveLink> competitiveLink;

    /**
     * 托管模块
     */
    private List<FullManagedModuleDTO> fullManagedModule;

    /**
     * 语言
     */
    private List<LanguageEnum> language;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 扩展字段
     */
    private SeoShopSettingAttributes attributes = new SeoShopSettingAttributes(null);

    /**
     * 品牌颜色
     */
    private String brandColor;

    /**
     * slogan
     */
    private String slogan;

    /**
     * 卖点
     */
    private String sellingPoint;

    /**
     * Blog分类
     */
    private List<String> blogCategory;

    /**
     * Sitemap信息
     */
    private SitemapInfo sitemapInfo = new SitemapInfo();

    /**
     * 图片风格（主要是生成图片）
     */
    private List<ImageStyle> imageStyle;

    /**
     * 指定Blog分类发布
     */
    private List<BlogCategoryApplyDTO> blogCategoryApply;

    /**
     * 专有名词
     */
    private List<String> properNoun;

    /**
     * 计算当前Setting配置分数
     *
     * @return
     * @throws IllegalAccessException
     */
    public BigDecimal calculateScore() {
        long nullOrEmptyFieldCount = 0L;

        Class<?> clazz = this.getClass();
        Field[] fields = clazz.getDeclaredFields();

        try {
            for (Field field : fields) {
                if (!includedFields.contains(field.getName())) {
                    continue;
                }
                field.setAccessible(true);
                Object value = field.get(this);
                if (value == null) {
                    nullOrEmptyFieldCount++;
                } else if (value instanceof Collection) {
                    Collection<?> collection = (Collection<?>) value;
                    if (collection.isEmpty()) {
                        nullOrEmptyFieldCount++;
                    }
                }
            }
        } catch (Exception e) {
            log.error("calculateScore error={},shopId={}", e.getMessage(), this.getShopId(), e);
        }
        return BigDecimal.valueOf(includedFields.size() - nullOrEmptyFieldCount)
                .multiply(new BigDecimal(100))
                .divide(BigDecimal.valueOf(includedFields.size()), 0, RoundingMode.UP)
                .setScale(0, RoundingMode.UP);
    }

    /**
     * 是否可以提交Sitemap
     *
     * @return
     */
    public Boolean canSubmitSitemap() {
        Boolean isCanSubmit = sitemapInfo.getIsCanSubmit();
        if (isCanSubmit == null) {
            isCanSubmit = false;
        }
        return isCanSubmit;
    }
}
