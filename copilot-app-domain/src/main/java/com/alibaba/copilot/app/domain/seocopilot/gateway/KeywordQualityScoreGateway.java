package com.alibaba.copilot.app.domain.seocopilot.gateway;

import com.alibaba.copilot.app.domain.seocopilot.dto.KeywordQualityScoreDTO;
import com.alibaba.copilot.app.domain.seocopilot.request.KeywordQualityScoreRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

import java.math.BigDecimal;

/**
 * 算法获取质量分数
 */
public interface KeywordQualityScoreGateway {

    /**
     * 获取关键词质量分数
     *
     * @param request
     * @return
     */
    SingleResult<KeywordQualityScoreDTO> getKeywordQualityScore(KeywordQualityScoreRequest request);

}
