package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;

/**
 * PresentmentPriceDTO
 *
 * <AUTHOR>
 * @date 2024/7/8 4:30 下午
 */
@Data
public class PresentmentPriceDTO {

    /**
     * price
     */
    private Price price;

    /**
     * compareAtPrice
     */
    private Price compareAtPrice;

    @Data
    public static class Price {

        /**
         * currencyCode
         */
        @JSONField(name = "currency_code")
        private String currencyCode;

        /**
         * amount
         */
        private BigDecimal amount;
    }
}
