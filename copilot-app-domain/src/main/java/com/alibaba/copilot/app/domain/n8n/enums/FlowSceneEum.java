package com.alibaba.copilot.app.domain.n8n.enums;

import lombok.Getter;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/05/22
 */
public enum FlowSceneEum {

    /**
     * 已安装
     */
    INSTALLED("installed"),

    BLOG_RUN_OUT_OF_QUOTA("blogRunOutOfQuota"),

    PRODUCT_RUN_OUT_OF_QUOTA("productRunOutOfQuota"),

    COLLECTION_RUN_OUT_OF_QUOTA("collectionRunOutOfQuota"),
    RUN_OUT_OF_QUOTA("runOutOfQuota"),

    SUBSCRIBED("subscribed"),

    FULLY_MANAGED("fullyManaged");

    @Getter
    private final String code;

    FlowSceneEum(String code) {
        this.code = code;
    }

    public String getType() {
        if (this.equals(BLOG_RUN_OUT_OF_QUOTA) || this.equals(PRODUCT_RUN_OUT_OF_QUOTA) || this.equals(COLLECTION_RUN_OUT_OF_QUOTA)) {
            return RUN_OUT_OF_QUOTA.getCode();
        }
        return this.getCode();
    }


    public static FlowSceneEum getByCode(String code) {
        for (FlowSceneEum flowSceneEum : FlowSceneEum.values()) {
            if (flowSceneEum.getCode().equals(code)) {
                return flowSceneEum;
            }
        }
        return null;
    }

}
