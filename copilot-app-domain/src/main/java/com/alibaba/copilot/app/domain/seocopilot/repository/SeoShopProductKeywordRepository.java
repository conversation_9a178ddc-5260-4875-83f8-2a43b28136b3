package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.domain.seocopilot.model.SeoShopProductKeyword;

import java.util.List;

/**
 * SeoShopProductKeywordRepository
 *
 * <AUTHOR>
 * @date 2024/5/15 5:29 下午
 */
public interface SeoShopProductKeywordRepository {

    /**
     * 保存关键词信息
     */
    Integer save(SeoShopProductKeyword seoShopProductKeyword);

    /**
     * 保存关键词信息
     */
    void batchSave(List<SeoShopProductKeyword> seoShopProductKeywords);

    /**
     * 根据商品title查询
     *
     * @param shopId
     * @param title
     * @return
     */
    List<SeoShopProductKeyword> getByProductTitle(Long shopId, String title);
}
