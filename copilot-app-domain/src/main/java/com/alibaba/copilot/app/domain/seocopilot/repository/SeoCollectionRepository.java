package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.domain.seocopilot.constant.SeoEntityStatus;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoCollection;

import java.util.List;
import java.util.Map;

public interface SeoCollectionRepository {

    Integer save(SeoCollection seoCollection);

    List<SeoCollection> getByShopId(Long shopId);

    /**
     * 查询14天内更新且未被收录的记录
     *
     * @param shopId
     * @return
     */
    List<SeoCollection> getByShopIdIn14DayAndNoIndex(Long shopId);

    SeoCollection getById(Long id);

    PageWrapper<SeoCollection> getByShopId(Long shopId, Integer pageNum, Integer pageSize);

    SeoCollection getByShopifyCollectionId(Long shopId, Long shopifyCollectionId);

    Integer updateStatus(Long collectionId, SeoEntityStatus status);

    Integer updateDelete(Long shopId, Long shopifyCollectionId);

    Integer updateAttribute(Long id, Map<String, Object> updateAttributeMap);

    Integer deleteByShopId(Long shopId);

    List<SeoCollection> getByShopifyCollectionIdIgnoreDeleted(Long shopId, Long shopifyCollectionId);

    /**
     * 查询seoProduct
     *
     * @param shopId
     * @return
     */
    SeoCollection getSeoCollectionByShopIdAndHandle(Long shopId, String handle);
}
