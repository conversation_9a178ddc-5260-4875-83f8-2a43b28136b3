package com.alibaba.copilot.app.domain.seocopilot.gateway;


import com.alibaba.copilot.app.domain.seocopilot.dto.FireCrawResponseDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.FireCrawSinglePageResponseDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.GetAllUrlResponseDTO;
import com.alibaba.copilot.app.domain.seocopilot.request.CrawlSinglePageRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.SubmitCrawlJobRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

import java.util.List;

/**
 * FireCrawl爬虫服务
 */
public interface FireCrawlGateway {

    /**
     * 通过sitemap获取所有的url
     */
    SingleResult<GetAllUrlResponseDTO> getAllUrlBySitemap(String sitemapUrl);

    /**
     * 提交爬虫异步任务
     */
    SingleResult<String> submitCrawlJob(SubmitCrawlJobRequest request);

    /**
     * 获取异步任务状态
     */
    SingleResult<FireCrawResponseDTO> getCrawlJobStatus(String jobId);

    /**
     * 爬取单页面html你容
     */
    SingleResult<FireCrawSinglePageResponseDTO> crawlSinglePage(CrawlSinglePageRequest request);

}
