package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.domain.seocopilot.constant.CheckEntityType;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoCheckRecord;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoProduct;
import com.alibaba.copilot.app.domain.seocopilot.request.CheckRecordSearchRequest;

import java.util.List;

/**
 * SeoShopRepository
 */
public interface SeoCheckRecordRepository {

    /**
     * 根据查询条件获取check records
     *
     * @param searchRequest
     * @return
     */
    List<SeoCheckRecord> getCheckRecordsByRequest(CheckRecordSearchRequest searchRequest);

    /**
     * 根据id获取
     *
     * @param id
     * @return
     */
    SeoCheckRecord getCheckRecordById(Long id);

    /**
     * 根据id获取
     *
     * @param id
     * @return
     */
    SeoCheckRecord getCheckRecordByIdIgnoreDeleted(Long id);

    /**
     * 根据ids获取
     *
     * @param ids
     * @return
     */
    List<SeoCheckRecord> getCheckRecordByIds(List<Long> ids);

    /**
     * 根据id列表获取
     *
     * @param ids
     * @return
     */
    List<SeoCheckRecord> getCheckRecordsByIds(List<Long> ids);

    /**
     * 根据batchId查询check record
     *
     * @return
     */
    List<SeoCheckRecord> getCheckRecordsByBatchId(String batchId);

    /**
     * 根据batchId查询check record
     *
     * @return
     */
    List<SeoCheckRecord> getCheckRecordsByShopIdAndBatchId(Long shopId, String batchId);

    /**
     * 根据taskId查询check record
     *
     * @return
     */
    List<SeoCheckRecord> getCheckRecordsByTaskId(Long taskId);

    /**
     * 根据taskId查询check record
     *
     * @return
     */
    List<SeoCheckRecord> getCheckRecordsByShopIdAndTaskId(Long shopId, Long taskId);

    /**
     * 根据taskId、entityType、entityId查询check record
     *
     * @return
     */
    List<SeoCheckRecord> getCheckRecordsByTaskIdAndEntity(Long taskId, CheckEntityType entityType, Long entityId);

    /**
     * 根据batchId和entityId获取某个对象的check record
     */
    List<SeoCheckRecord> getCheckRecordsByEntityId(String batchId, Long entityId, String entityType);

    /**
     * 保存SeoCheckRecord
     *
     * @return
     */
    int save(SeoCheckRecord seoCheckRecord);


    /**
     * 批量保存SeoCheckRecord
     *
     * @return
     */
    void batchSave(List<SeoCheckRecord> seoCheckRecords);

    Integer deleteByShopId(Long shopId);

    Integer deleteByEntityId(Long shopId, CheckEntityType entityType, Long entityId);

    /**
     * 查询一天的CheckRecord
     *
     * @param searchRequest
     * @return
     */
    List<SeoCheckRecord> getCheckRecordsOneDay(CheckRecordSearchRequest searchRequest);

    /**
     * @return
     */
    List<SeoCheckRecord> getCheckRecordsByShopId(Long shopId);

    /**
     * @return
     */
    List<SeoCheckRecord> getCheckRecordsByType(Long shopId, Long taskId, String entityType, String checkType);

    /**
     * @return
     */
    List<SeoCheckRecord> getCheckRecordsByIdAndType(Long shopId, Long taskId, Long entityId, String entityType, List<String> checkTypes);

    /**
     * @param shopId
     * @param batchId
     * @param taskId
     * @param entityId
     * @param entityType
     * @param checkTypes
     * @return
     */
    List<SeoCheckRecord> getCheckRecordsByIdsAndTypes(Long shopId, String batchId, Long taskId, Long entityId, String entityType, List<String> checkTypes);

    /**
     * @return
     */
    List<SeoCheckRecord> getCheckRecordsByTaskIdIsNull(Long shopId, String batchId, String entityType, List<String> notInStatus);

    /**
     * @return
     */
    List<SeoCheckRecord> getCheckRecordsByType(Long shopId, Long taskId, Long entityId, String entityType, String checkType);

    List<SeoCheckRecord> getByEntity(Long shopId, List<Long> entityIds, String entityType);

    List<SeoCheckRecord> getByEntityIgnoreDeleted(Long shopId, List<Long> entityIds, String entityType);
}
