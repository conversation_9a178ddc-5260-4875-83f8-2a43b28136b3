package com.alibaba.copilot.app.domain.seocopilot.gateway;

import com.alibaba.copilot.app.domain.seocopilot.dto.*;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShop;
import com.alibaba.copilot.app.domain.seocopilot.request.*;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * <AUTHOR>
 * @des 算法生成调用
 */
public interface AlgorithmGenerateGateway {

    /**
     * 生成关键词，场景 collection/product/blog/homepage
     *
     * @param seoShop
     * @param srcBizName
     * @param keywordGenerateParam
     * @return
     */
    SingleResult<List<KeywordGenDTO>> keywordGenFromAlgorithm(SeoShop seoShop, String srcBizName, KeywordGenerateRequest.KeywordGenerateParam keywordGenerateParam);

    /**
     * 粗召
     *
     * @param seoShop
     * @param request
     * @return
     */
    SingleResult<KeywordRecallDTO> keywordRecall(SeoShop seoShop, KeywordRecallRequest request);

    /**
     * 扩词
     *
     * @param seoShop
     * @param request
     * @return
     */
    SingleResult<List<KeywordExtensionDTO>> keywordExtension(SeoShop seoShop, KeywordExtensionRequest request);

    /**
     * 精排
     *
     * @param seoShop
     * @param request
     * @return
     */
    SingleResult<KeywordRerankDTO> keywordRerank(SeoShop seoShop, KeywordRerankRequest request);

    /**
     * 生成内容，原子型
     *
     * @param seoShop
     * @param apiName
     * @param srcBizName
     * @param contentGenerateParam
     * @return
     */
    SingleResult<List<String>> contentAiGenFromAlgorithm(SeoShop seoShop, String apiName, String srcBizName, ContentAiGenerateRequest.ContentGenerateParam contentGenerateParam);

    /**
     * 算法调用
     *
     * @param shopifyShopId
     * @param apiName
     * @param srcBizName
     * @param data
     * @return
     */
    JSONObject requestAlgorithm(Long shopifyShopId, String apiName, String srcBizName, String data);

    /**
     * scrapeKeyword
     *
     * @param request
     * @return
     */
    SingleResult<ScrapeKeywordDTO> scrapeKeyword(ScrapeKeywordRequest request);

    /**
     * scrapeUrl
     *
     * @param request
     * @return
     */
    SingleResult<ScrapeUrlDTO> scrapeUrl(ScrapeUrlRequest request);


    /**
     * 获取内链锚文本
     */
    SingleResult<List<String>> getInnerLinkAnchorText(SeoShop seoShop, InnerLinkAnchorTextRequest request);
}
