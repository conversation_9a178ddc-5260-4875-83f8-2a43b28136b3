package com.alibaba.copilot.app.domain.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FireCrawResponseDTO {
    public String status;
    public Integer current;
    public String current_url;
    public String current_step;
    public Integer total;
    public List<Data> data;
    public List<Data> partial_data;


    @lombok.Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Data {
        public String markdown;
        public String content;
        public String html;
        public String rawHtml;
        public Integer index;
        public Metadata metadata;
    }

    @lombok.Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Metadata {
        public String title;
        public String description;
        public String language;
        public String sourceURL;
        public Map<String, String> otherMetadata;
        public Integer pageStatusCode;
        public String pageError;
    }
}
