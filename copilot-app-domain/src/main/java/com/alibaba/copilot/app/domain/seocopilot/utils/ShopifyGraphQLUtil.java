package com.alibaba.copilot.app.domain.seocopilot.utils;

import com.alibaba.copilot.app.domain.seocopilot.dto.ImageResultDTO;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoProduct;
import org.apache.commons.lang3.StringUtils;

public class ShopifyGraphQLUtil {
    public static String getGraphQLProductId(Long id) {
        return "gid://shopify/Product/" + id;
    }

    public static String getGraphQLImageId(Long id) {
        return "gid://shopify/MediaImage/" + id;
    }

    public static String getGraphQLId(ImageResultDTO image) {
        return StringUtils.defaultIfBlank(image.getGraphqlId(), getGraphQLImageId(image.getImageId()));
    }

    public static String getGraphQLId(SeoProduct seoProduct) {
        return StringUtils.defaultIfBlank(seoProduct.getShopifyGraphqlId(), getGraphQLProductId(seoProduct.getShopifyProductId()));
    }
}
