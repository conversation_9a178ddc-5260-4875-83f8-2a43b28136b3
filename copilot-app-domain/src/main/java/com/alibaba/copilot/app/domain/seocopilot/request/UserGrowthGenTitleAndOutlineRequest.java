package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class UserGrowthGenTitleAndOutlineRequest {

    private String apiName = "seo_offline_title_outline";

    private String srcBizName = "";

    private Request data;

    @Data
    public static class Request {

        private String keywords;

        private String category;

        private String style;

        private String country;

        private Boolean craw;

        private String promptVersion;

        private List<String> url;

        @JSONField(name = "rewrite_reference_blog")
        private String rewriteReferenceBlog;
    }
}
