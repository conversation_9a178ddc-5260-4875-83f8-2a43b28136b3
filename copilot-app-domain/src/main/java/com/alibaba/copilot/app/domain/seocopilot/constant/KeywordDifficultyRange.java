package com.alibaba.copilot.app.domain.seocopilot.constant;


import lombok.Getter;

/**
 * KD区间范围
 */
public enum KeywordDifficultyRange {

    /**
     * 0-30
     */
    LOW("0-30", 0, 30),

    /**
     * 30-60
     */
    MEDIUM("30-60", 30, 60),


    /**
     * 60-100
     */
    HIGH("60-100", 60, 100);


    @Getter
    private String name;

    @Getter
    private Integer low;

    @Getter
    private Integer up;

    KeywordDifficultyRange(String name, Integer low, Integer up) {
        this.name = name;
        this.low = low;
        this.up = up;
    }

    /**
     * 根据区间名称查询区间
     *
     * @param name
     * @return
     */
    public static KeywordDifficultyRange getKdRangeByName(String name) {
        for (KeywordDifficultyRange range : KeywordDifficultyRange.values()) {
            if (range.getName().equals(name)) {
                return range;
            }
        }
        return null;
    }
}
