package com.alibaba.copilot.app.domain.seocopilot.request.copyleaks;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Results {
    private List<ResultType> internet;
    private List<ResultType> database;
    private List<ResultType> batch;
    private List<ResultType> repositories;
    private Score score;
    // getters and setters
}
