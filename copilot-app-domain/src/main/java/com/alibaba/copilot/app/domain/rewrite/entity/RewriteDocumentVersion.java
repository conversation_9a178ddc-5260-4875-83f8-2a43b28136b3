package com.alibaba.copilot.app.domain.rewrite.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RewriteDocumentVersion {
    private Long id;
    private Date gmtCreate;

    private Date gmtModified;

    private String content;

    private Long documentId;

    private String outerUserId;
    
    /**
     * 输入参数，Object类型
     */
    private Object input;
}
