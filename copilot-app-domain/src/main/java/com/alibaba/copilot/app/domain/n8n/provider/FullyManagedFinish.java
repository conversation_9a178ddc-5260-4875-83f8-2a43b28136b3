package com.alibaba.copilot.app.domain.n8n.provider;

import com.alibaba.copilot.app.client.flow.FlowNodeExecuteSPI;
import com.alibaba.copilot.app.client.flow.model.FlowNodeRequest;
import com.alibaba.copilot.app.client.flow.model.FlowNodeResponse;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShop;
import com.alibaba.copilot.app.domain.seocopilot.repository.SeoShopRepository;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import static com.alibaba.copilot.app.domain.seocopilot.model.SeoShopAttributes.FINISH_OPTIMIZE;

@Slf4j
@HSFProvider(serviceName = "fullyManagedFinishFlowNodeExecuteSPI", serviceInterface = FlowNodeExecuteSPI.class, serviceVersion = "1.0.0.SEO_COPILOT.isFullyManagedFinish")
public class FullyManagedFinish implements FlowNodeExecuteSPI{
    public static final String IS_FULLY_MANAGED_FINISH = "isFullyManagedFinish";

    @Resource
    private SeoShopRepository seoShopRepository;
    @Override
    public SingleResult<FlowNodeResponse> execute(FlowNodeRequest request) {
        log.info("FullyManagedFinish.execute request:{}", JSON.toJSONString(request));
        FlowNodeResponse response = new FlowNodeResponse();
        response.setAppCode(request.getAppCode());
        response.setActionCode(request.getActionCode());
        Long userId = request.getData().getLong("userId");
        SeoShop seoShop = seoShopRepository.getShopByShopifyShopId(userId);
        log.info("FullyManagedFinish.execute seoShop:{}", JSON.toJSONString(seoShop));
        JSONObject data = new JSONObject();
        if (seoShop == null) {
            log.info("FullyManagedFinish.execute userId doesn't exist. {}", userId);
            data.put(IS_FULLY_MANAGED_FINISH, "not_exist");
            response.setData(data);
            return SingleResult.buildSuccess(response);
        }
        if (seoShop.getFullyManagedEnabled() != null && seoShop.getFullyManagedEnabled() && FINISH_OPTIMIZE.equals(seoShop.getAttributes().getFullManagedStep())) {
            data.put(IS_FULLY_MANAGED_FINISH, "true");
        } else {
            data.put(IS_FULLY_MANAGED_FINISH, "false");
        }
        response.setData(data);
        return SingleResult.buildSuccess(response);
    }
}
