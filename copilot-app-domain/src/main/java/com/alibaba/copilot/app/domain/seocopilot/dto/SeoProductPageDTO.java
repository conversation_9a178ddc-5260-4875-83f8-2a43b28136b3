package com.alibaba.copilot.app.domain.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SeoProductPageDTO {
    /**
     *  id
     */
    private Long id;
    /**
     * shopify product id
     */
    private Long shopifyProductId;

    /**
     * product的title
     */
    private String title;

    /**
     * 主图
     */
    private String mainPicUrl;

    /**
     * 商品所属的集合
     */
    private List<String> collectionNames;

    /**
     * 更新时间
     */
    private Long updateDate;

    /**
     * 关键词列表
     */
    private List<String> keywords;

    /**
     * AlphaRank所处状态
     */
    private String status;
}
