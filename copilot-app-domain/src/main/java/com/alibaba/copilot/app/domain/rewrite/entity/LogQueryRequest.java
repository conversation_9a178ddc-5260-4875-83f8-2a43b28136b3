package com.alibaba.copilot.app.domain.rewrite.entity;

import lombok.Data;

import java.util.Date;

@Data
public class LogQueryRequest {
    /**
     * 日志类型
     */
    private String eventType;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间（可选）
     */
    private Date endTime;

    /**
     * 页码（默认为1）
     */
    private Integer pageNum = 1;

    /**
     * 每页大小（默认为10）
     */
    private Integer pageSize = 10;
} 