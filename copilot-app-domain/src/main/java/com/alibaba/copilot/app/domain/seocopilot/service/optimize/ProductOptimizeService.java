package com.alibaba.copilot.app.domain.seocopilot.service.optimize;

import com.alibaba.copilot.app.domain.seocopilot.dto.ContentAiGenerateDTO;
import com.alibaba.copilot.app.domain.seocopilot.request.ContentAiGenerateRequest;

public interface ProductOptimizeService {

    /**
     * 优化商品title
     *
     * @param request
     * @return
     */
    ContentAiGenerateDTO optimizeTitle(Long shopId, ContentAiGenerateRequest request);


    /**
     * 优化商品description
     *
     * @param request
     * @return
     */
    ContentAiGenerateDTO optimizeDescription(Long shopId, ContentAiGenerateRequest request);


    /**
     * 优化商品Title tag
     *
     * @param request
     * @return
     */
    ContentAiGenerateDTO optimizeTitleTag(Long shopId, ContentAiGenerateRequest request);


    /**
     * 优化商品Meta Description
     *
     * @param request
     * @return
     */
    ContentAiGenerateDTO optimizeMetaDescription(Long shopId, ContentAiGenerateRequest request);
}
