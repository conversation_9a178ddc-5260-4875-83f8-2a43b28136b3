package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @des Blog多轮交互 - 长标题生成 的返回值
 */
@Data
public class BlogTitleAiGenerateDTO {

    /**
     * title1
     */
    private String title1;

    /**
     * title2
     */
    private String title2;

    /**
     * title3
     */
    private String title3;

    /**
     * title4
     */
    private String title4;

    /**
     * title5
     */
    private String title5;

    /**
     * title6
     */
    private String title6;

    /**
     * @return
     */
    public List<String> getTitles() {
        List<String> titles = Lists.newArrayList(this.getTitle1(),
                this.getTitle2(),
                this.getTitle3(),
                this.getTitle4(),
                this.getTitle5(),
                this.getTitle6()
        );

        return titles;
    }
}
