package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.domain.seocopilot.model.SeoShopSiteUser;

import java.util.Collection;
import java.util.List;

public interface SeoShopSiteUserRepository {

    /**
     * 保存
     */
    Integer save(SeoShopSiteUser seoShopSiteUser);

    /**
     * 根据ShopId获取数据
     *
     * @param shopId
     * @return
     */
    SeoShopSiteUser getByShopId(Long shopId, String siteType);

    /**
     * 根据seoShopId获取数据
     *
     * @param seoShopId
     * @return
     */
    List<SeoShopSiteUser> getBySeoShopId(Long seoShopId);

    /**
     * 根据userId获取数据
     *
     * @param userId
     * @return
     */
    SeoShopSiteUser getByUserId(Long userId, String siteType);

    /**
     * 根据userId获取数据
     *
     * @param userId
     * @return
     */
    SeoShopSiteUser getByUserId(Long userId);

    /**
     * 根据outerUserId获取数据
     *
     * @param outerUserId
     * @return
     */
    SeoShopSiteUser getByOuterUserId(String outerUserId);

    List<SeoShopSiteUser> getAllByOuterUserId(String appCode, Collection<String> outerUserIds);

    /**
     * 根据email获取数据
     *
     * @param email
     * @return
     */
    SeoShopSiteUser getSeoUserByEmail(String email);

    /**
     * getById
     *
     * @param id
     * @return
     */
    SeoShopSiteUser getById(Long id);
}
