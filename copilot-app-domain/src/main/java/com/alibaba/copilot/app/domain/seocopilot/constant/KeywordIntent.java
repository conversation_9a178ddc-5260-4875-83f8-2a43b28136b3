package com.alibaba.copilot.app.domain.seocopilot.constant;


import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 关键词意图
 */
public enum KeywordIntent {


    Commercial("Commercial", 0),

    Informational("Informational", 1),

    Navigational("Navigational", 2),

    Transactional("Transactional", 3);

    private String name;

    private Integer value;


    KeywordIntent(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

    public static KeywordIntent getKeywordIntentByValue(Integer value) {
        for (KeywordIntent keywordIntent : KeywordIntent.values()) {
            if (Objects.equals(keywordIntent.value, value)) {
                return keywordIntent;
            }
        }
        throw new IllegalArgumentException("KeywordIntent value illegal value=" + value);
    }

    public static KeywordIntent getKeywordIntentByName(String name) {
        for (KeywordIntent keywordIntent : KeywordIntent.values()) {
            if (StringUtils.equals(keywordIntent.name, name)) {
                return keywordIntent;
            }
        }
        throw new IllegalArgumentException("KeywordIntent value illegal name=" + name);
    }

    public static String getIntentNameByValue(String intentValue) {

        if (StringUtils.isEmpty(intentValue)) {
            return null;
        }

        // 逗号分隔
        String[] intentValues = intentValue.split(",");
        return Arrays.stream(intentValues).map(intent -> {
            try {
                return KeywordIntent.getKeywordIntentByValue(Integer.valueOf(intent)).name();
            } catch (Exception e) {
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.joining(","));
    }
}
