package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoKeyword;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoKeywordSearchParam;

import java.util.List;

public interface SeoKeywordRepository {

    PageWrapper<SeoKeyword> query(SeoKeywordSearchParam param);

    List<SeoKeyword> queryTopSearch(String keyword);

    /**
     * 根据多个关键字精确匹配进行查询
     */
    List<SeoKeyword> queryExactMatchByKeyword(List<String> keywords);
}
