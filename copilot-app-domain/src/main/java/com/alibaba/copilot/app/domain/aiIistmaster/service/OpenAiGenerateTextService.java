package com.alibaba.copilot.app.domain.aiIistmaster.service;

import java.io.StringWriter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.alibaba.copilot.app.domain.aiIistmaster.config.BaseDataConfig;
import com.alibaba.copilot.app.domain.aiIistmaster.model.TextPreferenceTemplate;
import freemarker.template.Configuration;
import freemarker.template.Template;

import javax.annotation.Resource;

import com.alibaba.copilot.boot.llm.openai.client.OpenAiBaseClient;
import com.alibaba.copilot.boot.llm.openai.constant.Models;
import com.alibaba.copilot.boot.llm.openai.data.completion.chat.ChatCompletionChoice;
import com.alibaba.copilot.boot.llm.openai.data.completion.chat.ChatCompletionRequest;
import com.alibaba.copilot.boot.llm.openai.data.completion.chat.ChatMessage;
import com.alibaba.copilot.boot.llm.openai.data.completion.chat.ChatMessageRole;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * @ClassName OpenAiBusinessService
 * <AUTHOR>
 * @Date 2023/8/20 22:45
 */
@Service
public class OpenAiGenerateTextService {
    @Resource
    private OpenAiBaseClient openAiBaseClient;

    @Resource
    private BaseDataConfig baseDataConfig;

    /**
     * 自动生成推荐标题
     *
     * @param originalValue          优化内容
     * @param textPreferenceTemplate 优化参数
     * @return
     */
    public List<String> autoGenerateTitle(String originalValue, TextPreferenceTemplate textPreferenceTemplate) {
        List<String> titleList = new ArrayList<>();
        ChatMessage systemMessage = new ChatMessage(
            ChatMessageRole.USER.value(), generateGenerateTitlePrompt(originalValue,
            Arrays.asList(textPreferenceTemplate.getKeyWords().split(",")), textPreferenceTemplate.getStyle(),
            textPreferenceTemplate.getWorldsLimit()));

        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest
            .builder()
            .model(Models.GTP3.GPT_3_5_TURBO.getId())
            .messages(Collections.singletonList(systemMessage))
            .temperature(1.0)
            .build();

        List<ChatCompletionChoice> choices = openAiBaseClient.createChatCompletion(chatCompletionRequest).getChoices();
        if (!CollectionUtils.isEmpty(choices)) {
            String firstNonEmptyContent = choices.stream()
                .map(completionChoice -> completionChoice.getMessage().getContent())
                .filter(Objects::nonNull)
                .filter(content -> !content.isEmpty())
                .findFirst()
                .orElse("");
            String[] parts = firstNonEmptyContent.split("\n");
            for (String part : parts) {
                titleList.add(part.trim().replaceAll("\\*", ""));
            }
        }

        return titleList;
    }

    private String generateGenerateTitlePrompt(String oldTitle, List<String> keyWords, String modalParticle, Long minWords) {
        String template = baseDataConfig.getCommodityTitleTemplate();
        String keyWordsString = String.join("、", keyWords);
        try (StringWriter writer = new StringWriter()) {
            Template tpl = new Template("strTpl", template, new Configuration());
            Map<String, String> params = new HashMap<>();
            params.put("oldTitle", oldTitle == null ? "" : oldTitle);
            params.put("keyWords", keyWordsString);
            params.put("modalParticle", modalParticle == null ? "Business" : modalParticle);
            params.put("minWords", minWords == null ? "" : String.valueOf(minWords));
            tpl.process(params, writer);
            return writer.toString();
        } catch (Exception e) {
            throw new RuntimeException("Error processing template", e);
        }
    }

    /**
     * 自动生成推荐标题
     *
     * @param originalValue          优化内容
     * @param textPreferenceTemplate 优化参数
     * @return
     */
    public String autoGenerateShortSellPoint(String originalValue, TextPreferenceTemplate textPreferenceTemplate) {
        String shortSellPoint = "";
        ChatMessage systemMessage = new ChatMessage(
            ChatMessageRole.USER.value(), generateGenerateShortSellPointPrompt(originalValue,
            Arrays.asList(textPreferenceTemplate.getKeyWords().split(",")), textPreferenceTemplate.getStyle(),
            textPreferenceTemplate.getWorldsLimit()));
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest
            .builder()
            .model(Models.GTP3.GPT_3_5_TURBO.getId())
            .messages(Collections.singletonList(systemMessage))
            .temperature(1.0)
            .build();
        List<ChatCompletionChoice> choices = openAiBaseClient.createChatCompletion(chatCompletionRequest).getChoices();
        if (!CollectionUtils.isEmpty(choices)) {
            shortSellPoint = choices.stream()
                .map(completionChoice -> completionChoice.getMessage().getContent())
                .filter(Objects::nonNull)
                .filter(content -> !content.isEmpty())
                .collect(Collectors.joining(""));
            // 使用空格作为分隔符拼接所有非空选项
        }
        return shortSellPoint;
    }
    private String generateGenerateShortSellPointPrompt(String oldTitle, List<String> keyWords, String modalParticle, Long minWords) {
        String template = baseDataConfig.getShortSellPointTemplate();
        String keyWordsString = String.join("、", keyWords);
        try {
            Template tpl = new Template("strTpl", template, new Configuration());
            Map<String, String> params = new HashMap<>();
            params.put("oldTitle", oldTitle == null ? "" : oldTitle);
            params.put("keyWords", keyWordsString);
            params.put("modalParticle", modalParticle == null ? "Business" : modalParticle);
            params.put("minWords", minWords == null ? "" : String.valueOf(minWords));
            StringWriter writer = new StringWriter();
            tpl.process(params, writer);
            return writer.toString();
        } catch (Exception e) {
            throw new RuntimeException("Error processing template", e);
        }
    }


    /**
     * 自动生成推荐标题
     *
     * @param originalValue          优化内容
     * @param textPreferenceTemplate 优化参数
     * @return
     */
    public String autoGenerateDescription(String originalValue, TextPreferenceTemplate textPreferenceTemplate) {
        String description = "";
        ChatMessage systemMessage = new ChatMessage(
            ChatMessageRole.USER.value(), generateDescriptionPrompt(originalValue,
            Arrays.asList(textPreferenceTemplate.getKeyWords().split(",")), textPreferenceTemplate.getStyle(),
            textPreferenceTemplate.getWorldsLimit()));
        ChatCompletionRequest chatCompletionRequest = ChatCompletionRequest
            .builder()
            .model(Models.GTP3.GPT_3_5_TURBO_16K.getId())
            .messages(Collections.singletonList(systemMessage))
            .temperature(0.7)
            .maxTokens(7168)
            .build();
        List<ChatCompletionChoice> choices = openAiBaseClient.createChatCompletion(chatCompletionRequest).getChoices();
        if (!CollectionUtils.isEmpty(choices)) {
            description = choices.stream()
                .map(completionChoice -> completionChoice.getMessage().getContent())
                .filter(Objects::nonNull)
                .filter(content -> !content.isEmpty())
                .collect(Collectors.joining(""));
            // 使用空格作为分隔符拼接所有非空选项
        }
        return description;
    }
    private String generateDescriptionPrompt(String oldDesc, List<String> keyWords, String style, Long minWords) {
        String template = baseDataConfig.getDescriptionTemplate();
        String keyWordsString = String.join("、", keyWords);
        try {
            Template tpl = new Template("strTpl", template, new Configuration());
            Map<String, String> params = new HashMap<>();
            params.put("oldDesc", oldDesc == null ? "" : oldDesc);
            params.put("keyWords", keyWordsString);
            params.put("style", style == null ? "Business" : style);
            params.put("minWords", minWords == null ? "" : String.valueOf(minWords));
            StringWriter writer = new StringWriter();
            tpl.process(params, writer);
            return writer.toString();
        } catch (Exception e) {
            throw new RuntimeException("Error processing template", e);
        }
    }
}
