package com.alibaba.copilot.app.domain.seocopilot.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
public class AnalyticsOverViewDataDTO {
    /**
     * 数据截止时间
     */
    private String dataCutoffTime;
    /**
     * 平均排名
     */
    private BigDecimal averagePosition;
    /**
     * 平均排名环比 -31.25%
     */
    private String averagePositionGrowth;
    /**
     * 曝光量
     */
    private BigDecimal impressions;
    /**
     * 未转换单位的实际曝光量
     */
    private Long realImpressions;
    /**
     * 曝光量单位 k m等
     */
    private String impressionsUnit;
    /**
     * 曝光量环比 -31.25%
     */
    private String impressionsGrowth;
    /**
     * ctr
     */
    private BigDecimal ctr;
    /**
     * ctr环比 -31.25%
     */
    private String ctrGrowth;
    /**
     * 点击量
     */
    private BigDecimal clicks;
    /**
     * 未转换单位的实际点击量
     */
    private Long realClicks;
    /**
     * 点击数单位 k m等
     */
    private String clicksUnit;
    /**
     * 点击量环比 -31.25% 如果前一天的数据是0，返回 -
     */
    private String clicksGrowth;
    /**
     * 站点当天的活跃用户数
     */
    private BigDecimal users;
    /**
     * 未转换单位的实际用户数
     */
    private Long realUsers;
    /**
     * 用户数单位 k m等
     */
    private String usersUnit;
    /**
     * 用户增长环比 -31.25%
     */
    private String usersGrowth;
    /**
     * 站点当天的新用户数
     */
    private BigDecimal newUsers;
    /**
     * 未转换单位的实际新用户数
     */
    private Long realNewUsers;
    /**
     * 新用户数单位 k m等
     */
    private String newUsersUnit;
    /**
     * 新用户增长环比 24.2%
     */
    private String newUsersGrowth;
    /**
     * 整个站点的用户平均停留时长
     */
    private BigDecimal averageEngageTime;
    /**
     * 未转换单位的实际平均停留时长
     */
    private BigDecimal realAverageEngageTime;
    /**
     * 平均停留时长单位 min s等
     */
    private String averageEngageTimeUnit;
    /**
     * 平均停留时间环比 24.2%
     */
    private String averageEngageTimeGrowth;
    /**
     * 感兴趣的会话占比
     */
    private BigDecimal engagementRate;
    /**
     * 感兴趣的会话占比环比 24.2%
     */
    private String engagementRateGrowth;
    /**
     * 站点当天的浏览量
     */
    private BigDecimal views;
    /**
     * 未转换单位的实际访问量
     */
    private Long realViews;
    /**
     * 浏览量单位
     */
    private String viewsUnit;
    /**
     * 浏览量环比 24.2%
     */
    private String viewsGrowth;
    /**
     * 会话数量
     */
    private Long sessions;
    /**
     * 网站或应用在用户设备前台运行的总时长（以秒为单位）。
     */
    private BigDecimal userEngagementDuration;
    /**
     * 未转换单位的网站或应用在用户设备前台运行的总时长（以秒为单位）。
     */
    private BigDecimal realUserEngagementDuration;
    /**
     * 网站或应用在用户设备前台运行的总时长单位 min s等
     */
    private String userEngagementDurationUnit;
    /**
     * 总停留时间环比 24.2%
     */
    private String userEngagementDurationGrowth;
    /**
     * 感兴趣的会话数
     */
    private Long engagedSessions;
    /**
     * gsc有效性
     */
    private Boolean gscValid;
    /**
     * ga有效性
     */
    private Boolean gaValid;

    public static AnalyticsOverViewDataDTO defaultInstance() {
        return AnalyticsOverViewDataDTO.builder()
                .averagePosition(BigDecimal.valueOf(0))
                .averagePositionGrowth("-")
                .impressions(BigDecimal.valueOf(0))
                .impressionsGrowth("-")
                .ctr(BigDecimal.valueOf(0))
                .ctrGrowth("-")
                .clicks(BigDecimal.valueOf(0))
                .clicksGrowth("-")
                .users(BigDecimal.valueOf(0))
                .usersGrowth("-")
                .newUsers(BigDecimal.valueOf(0))
                .newUsersGrowth("-")
                .averageEngageTime(BigDecimal.valueOf(0))
                .averageEngageTimeGrowth("-")
                .userEngagementDuration(BigDecimal.valueOf(0))
                .userEngagementDurationGrowth("-")
                .engagementRate(BigDecimal.valueOf(0))
                .engagementRateGrowth("-")
                .engagedSessions(0L)
                .views(BigDecimal.valueOf(0))
                .viewsGrowth("-")
                .sessions(0L)
                .usersUnit("")
                .newUsersUnit("")
                .viewsUnit("")
                .clicksUnit("")
                .averageEngageTimeUnit("s")
                .impressionsUnit("")
                .userEngagementDurationUnit("")
                .realUsers(0L)
                .realNewUsers(0L)
                .realAverageEngageTime(BigDecimal.valueOf(0))
                .realClicks(0L)
                .realImpressions(0L)
                .realViews(0L)
                .realUserEngagementDuration(BigDecimal.valueOf(0))
                .gscValid(Boolean.FALSE)
                .gaValid(Boolean.FALSE)
                .build();
    }
}
