package com.alibaba.copilot.app.domain.seocopilot.gateway;

import com.alibaba.copilot.app.domain.seocopilot.constant.KeywordSource;
import com.alibaba.copilot.boot.semrush.web.data.semrush.*;

import java.util.List;

/**
 * Sermush 服务
 */
public interface SemrushGateway {

    /**
     * 根据关键词获取问题
     *
     * @param shopId
     * @param sourceType 查询关键词的类型（自身站点、自身关键词、竞对关键词）
     * @param keyword
     * @param limit
     * @return
     */
    List<SemrushQuestionData> getPhraseQuestions(Long shopId, KeywordSource sourceType, String keyword, Integer limit, Boolean isSaveData);

    /**
     * 根据关键词获取关键词
     *
     * @param shopId     用户唯一标识
     * @param sourceType 查询关键词的类型（自身站点、自身关键词、竞对关键词）
     * @param keyword
     * @param limit
     * @return
     */
    List<SemrushPhraseRelatedData> getRelatedKeywords(Long shopId, KeywordSource sourceType, String keyword, Integer limit);

    /**
     * 获取关键词概览数据
     *
     * @param shopId     用户唯一标识
     * @param sourceType 查询关键词的类型（自身站点、自身关键词、竞对关键词）
     * @param keyword
     * @return
     */
    SemrushKeywordOverviewData getKeywordOverview(Long shopId, KeywordSource sourceType, String keyword);

    /**
     * 获取关键词概览数据
     *
     * @param keyword
     * @return
     */
    SemrushKeywordOverviewData getKeywordOverview(String keyword,String database);

    /**
     * 根据domain获取关键词
     *
     * @param shopId     用户唯一标识
     * @param sourceType 查询关键词的类型（自身站点、自身关键词、竞对关键词）
     * @param domain
     * @param limit
     * @return
     */
    List<SemrushDomainOrganicData> getDomainOrganicSearchKeywords(Long shopId, KeywordSource sourceType, String domain, Integer limit);

    /**
     * 根据竞对信息
     *
     * @param domain
     * @param limit
     * @return
     */
    List<SemrushDomainOrganicCompetitor> getCompetitorsInOrganicSearch(String domain, Integer limit);

    /**
     * 根据Url获取关键词
     */
    List<SemrushUrlOrganicData> getUrlOrganicSearchKeywords(SemrushUrlOrganicRequest request);

    /**
     * 根据关键词获取关键词
     *
     * @param keyword
     * @param limit
     * @return
     */
    List<SemrushPhraseRelatedData> getRelatedKeywordsWithoutShop(String keyword, Integer limit);


    /**
     * 根据关键词获取问题
     */
    List<SemrushQuestionData> getPhraseQuestions(SemrushQuestionRequest request);

    /**
     * 根据关键词获取关键词
     */
    List<SemrushPhraseRelatedData> getRelatedKeywords(SemrushPhraseRelatedRequest request);

    /**
     * 根据domain获取关键词
     * @return
     */
    List<SemrushDomainOrganicData> getDomainOrganicSearchKeywords(SemrushDomainOrganicRequest request);

    SemrushUrlOverviewData getUrlOverview(String url,String database);

    List<SemrushUrlOverviewData> getUrlOverviewHistory(String url,String database);

    SemrushBacklinksOverviewData getBacklinksOverview(String target,String type);
}
