package com.alibaba.copilot.app.domain.rewrite.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RewriteEventLog {
    private Long id;
    private Date gmtCreate;
    private Date gmtModified;
    private String eventType;
    private String userId;
    private String anonymousId;
    private String eventData;
    private String additionalData;
    private String utmSource;
    private String sourceCountry;
}
