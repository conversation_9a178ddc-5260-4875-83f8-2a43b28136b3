package com.alibaba.copilot.app.domain.seocopilot.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/9/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CrawlSinglePageRequest {
    private String url;
    private PageOptions pageOptions;
    private ExtractorOptions extractorOptions;
    private Integer timeout;


    /**
     * 页面参数
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class PageOptions {
        Boolean onlyMainContent = false;
        Boolean includeHtml = true;
        Boolean includeRawHtml = false;
        Boolean screenshot = false;
        Integer waitFor;
        List<String> removeTags;
        List<String> onlyIncludeTags;
        Map<String, String> headers;
        Boolean replaceAllPathsWithAbsolutePaths;
    }

    /**
     * LLM参数
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class ExtractorOptions {
        String mode;
        String extractionPrompt;
        Map<String, Object> extractionSchema;
    }
}
