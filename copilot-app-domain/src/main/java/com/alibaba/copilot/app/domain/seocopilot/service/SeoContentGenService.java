package com.alibaba.copilot.app.domain.seocopilot.service;

import com.alibaba.copilot.app.client.seocopilot.dto.SeoContentGenOutlineDTO;
import com.alibaba.copilot.app.client.seocopilot.dto.SeoContentGenTitleDTO;
import com.alibaba.copilot.app.client.seocopilot.request.BlogArticleGenerateRequest;
import com.alibaba.copilot.app.client.seocopilot.request.ContentGenRequest;
import com.alibaba.copilot.app.domain.seocopilot.constant.LanguageEnum;
import com.alibaba.copilot.app.domain.seocopilot.dto.SeoContentGenKeywordDTO;
import com.alibaba.copilot.app.client.seocopilot.dto.SeoContentGenProductDTO;
import com.alibaba.copilot.app.domain.seocopilot.request.BlogOutlineAiPromptRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

import java.util.List;

/**
 * SeoContentGenService
 *
 * <AUTHOR>
 * @date 2024/6/19 11:33 上午
 */
public interface SeoContentGenService {

    /**
     * getKeywordsBasedKeyword
     *
     * @param shopId
     * @param word
     * @return
     */
    SingleResult<SeoContentGenKeywordDTO> getKeywordsBasedKeyword(Long shopId, String word, LanguageEnum language);

    /**
     * generateTitleBasedKeyword
     *
     * @param shopId
     * @param request
     * @param language
     * @return
     */
    SingleResult<List<SeoContentGenTitleDTO>> generateTitleBasedKeyword(Long shopId, ContentGenRequest request);

    /**
     * generateOutlineBasedKeyword
     *
     * @param shopId
     * @param request
     * @param language
     * @return
     */
    SingleResult<SeoContentGenOutlineDTO> generateOutlineBasedKeyword(Long shopId, ContentGenRequest request);

    /**
     * generateContentBasedKeyword
     *
     * @param shopId
     * @param request
     * @return
     */
    SingleResult<Long> generateContentBasedKeyword(Long shopId, ContentGenRequest request);

    /**
     * getKeywordsBasedTopic
     *
     * @param shopId
     * @param keywordId
     * @param topic
     * @return
     */
    SingleResult<SeoContentGenKeywordDTO> getKeywordsBasedTopic(Long shopId, Long keywordId, String topic, LanguageEnum language);

    /**
     * generateOutlineBasedTopic
     *
     * @param shopId
     * @param request
     * @return
     */
    SingleResult<SeoContentGenOutlineDTO> generateOutlineBasedTopic(Long shopId, ContentGenRequest request);

    /**
     * generateContentBasedTopic
     *
     * @param shopId
     * @param request
     * @return
     */
    SingleResult<Long> generateContentBasedTopic(Long shopId, ContentGenRequest request);

    /**
     * getKeywordsByProductUrl
     *
     * @param shopId
     * @param productUrl
     * @param productDetail
     * @return
     */
    SingleResult<SeoContentGenKeywordDTO> getKeywordsByProductUrl(Long shopId, String productUrl, String productDetail);

    /**
     * getKeywordsByProductDetail
     *
     * @param shopId
     * @param productDetail
     * @return
     */
    SingleResult<SeoContentGenKeywordDTO> getKeywordsByProductDetail(Long shopId, String productDetail);

    /**
     * generateTitleBasedProduct
     *
     * @param shopId
     * @param request
     * @return
     */
    SingleResult<List<SeoContentGenTitleDTO>> generateTitleBasedProduct(Long shopId, ContentGenRequest request);

    /**
     * generateOutlineBasedProduct
     *
     * @param shopId
     * @param request
     * @return
     */
    SingleResult<SeoContentGenOutlineDTO> generateOutlineBasedProduct(Long shopId, ContentGenRequest request);

    /**
     * generateContentBasedProduct
     *
     * @param shopId
     * @param request
     * @return
     */
    SingleResult<Long> generateContentBasedProduct(Long shopId, ContentGenRequest request);

    /**
     * generateOutline
     *
     * @param shopId
     * @param request
     * @return
     */
    SingleResult<String> generateOutline(Long shopId, BlogArticleGenerateRequest request);

    /**
     * generateOutline
     *
     * @param shopId
     * @param request
     * @return
     */
    SingleResult<String> generateOutline(Long shopId, BlogOutlineAiPromptRequest request);

    /**
     * generateContent
     *
     * @param shopifyShopId
     * @param request
     * @return
     */
    SingleResult<Long> generateContent(Long shopifyShopId, ContentGenRequest request);

    /**
     * getRecommendProducts
     *
     * @param shopifyShopId
     * @param productUrl
     * @return
     */
    SingleResult<List<SeoContentGenProductDTO>> getRecommendProducts(Long shopifyShopId, String productUrl);

    /**
     * addProduct
     *
     * @param shopifyShopId
     * @param request
     * @return
     */
    SingleResult<SeoContentGenProductDTO> addProduct(Long shopifyShopId, ContentGenRequest request);

    /**
     * generateTitleBasedProductRank
     *
     * @param shopId
     * @param request
     * @return
     */
    SingleResult<List<SeoContentGenTitleDTO>> generateTitleBasedProductRank(Long shopId, ContentGenRequest request);

    /**
     * generateOutlineBasedProductRank
     *
     * @param shopId
     * @param request
     * @return
     */
    SingleResult<SeoContentGenOutlineDTO> generateOutlineBasedProductRank(Long shopId, ContentGenRequest request);

    /**
     * generateContentBasedProductRank
     *
     * @param shopId
     * @param request
     * @return
     */
    SingleResult<Long> generateContentBasedProductRank(Long shopId, ContentGenRequest request);

    /**
     * generateTitleBasedComparison
     *
     * @param shopId
     * @param request
     * @return
     */
    SingleResult<List<SeoContentGenTitleDTO>> generateTitleBasedComparison(Long shopId, ContentGenRequest request);

    /**
     * generateOutlineBasedComparison
     *
     * @param shopId
     * @param request
     * @return
     */
    SingleResult<SeoContentGenOutlineDTO> generateOutlineBasedComparison(Long shopId, ContentGenRequest request);

    /**
     * generateContentBasedComparison
     *
     * @param shopId
     * @param request
     * @return
     */
    SingleResult<Long> generateContentBasedComparison(Long shopId, ContentGenRequest request);
}
