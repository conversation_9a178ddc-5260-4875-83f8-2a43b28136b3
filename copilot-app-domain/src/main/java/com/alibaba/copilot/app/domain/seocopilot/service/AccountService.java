package com.alibaba.copilot.app.domain.seocopilot.service;

import com.alibaba.copilot.app.client.seocopilot.request.AccountRegisterRequest;
import com.alibaba.copilot.app.client.seocopilot.request.AccountRegisterShopRequest;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShop;

import java.util.List;

/**
 * AccountService
 *
 * <AUTHOR>
 * @date 2024/7/10 2:29 下午
 */
public interface AccountService {

    /**
     * registerUser
     *
     * @param request
     * @return
     */
    Long registerUser(AccountRegisterRequest request);

    /**
     * registerShop
     *
     * @param request
     * @return
     */
    Long registerShop(AccountRegisterRequest request);

    Long registerShop(AccountRegisterShopRequest request);

    Long activeShop(AccountRegisterShopRequest request);

    Boolean inactiveShops(Long userId);

    /**
     * getUserIdByShopId
     *
     * @param shopId
     * @param needTransfer
     * @return
     */
    Long getUserIdByShopId(Long shopId, Boolean needTransfer);

    /**
     * getUserIdByShopifyShopId
     *
     * @param shopifyShopId
     * @return
     */
    Long getUserIdByShopifyShopId(Long shopifyShopId);

    /**
     * getUserIdBySeoShopId
     *
     * @param seoShopId
     * @return
     */
    Long getUserIdBySeoShopId(Long seoShopId);

    /**
     * getUserId
     *
     * @param seoShop
     * @return
     */
    Long getUserId(SeoShop seoShop);

    /**
     * getSeoShopIdsByUserId
     *
     * @param userId
     * @return
     */
    List<Long> getSeoShopIdsByUserId(Long userId);

    /**
     * getUserIdTransferred
     *
     * @param userId
     * @return
     */
    Long getUserIdTransferred(Long userId);

    Long revertUserIdTransfer(Long userId);

    /**
     * getPublishPlatform
     *
     * @param seoShopId
     * @return
     */
    String getPublishPlatform(Long seoShopId);


    /**
     * 注册 enable 用户信息
     * @param userId
     * @param email
     * @param appCode
     * @return
     */
    Boolean registerUserAndAppRelation(Long userId, String email, String appCode);
}
