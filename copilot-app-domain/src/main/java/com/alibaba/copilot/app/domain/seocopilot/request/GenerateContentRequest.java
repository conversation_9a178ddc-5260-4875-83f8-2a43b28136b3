package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class GenerateContentRequest {

    private String apiName = "seo_rewrite_content";

    private String srcBizName = "";

    private String scene = "tools_ranking";

    private Request data;


    @Data
    public static class Request {

        /**
         * keywords
         */
        private List<String> keywords;

        /**
         * title
         */
        private String title;

        /**
         * outline
         */
        private String outline;

        /**
         * references_blogs
         */
        @JSONField(name = "references_blogs")
        private String referencesBlogs;

        /**
         * retrieval_info_list
         */
        @<PERSON><PERSON><PERSON>ield(name = "retrieval_info_list")
        private List<String> retrievalInfoList;
    }
}
