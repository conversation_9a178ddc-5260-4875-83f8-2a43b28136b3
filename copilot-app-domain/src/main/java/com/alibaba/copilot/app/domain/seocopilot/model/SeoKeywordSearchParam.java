package com.alibaba.copilot.app.domain.seocopilot.model;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class SeoKeywordSearchParam {

    /**
     * 关键词意图
     */
    private String intents;

    /**
     * 预测一级类目
     */
    private String cateLv1Desc;

    /**
     * KD得分
     */
    private String kdScoreType;

    /**
     * original_keyword 模糊匹配
     */
    private String originalKeyword;

    /**
     * 当前是按哪个字段来排序
     */
    private String orderWord;

    /**
     * 增序 1 减序 0
     */
    private Boolean order;

    /**
     * 查询页面
     */
    private int pageNum;

    /**
     * 每页几条
     */
    private int pageSize;
}
