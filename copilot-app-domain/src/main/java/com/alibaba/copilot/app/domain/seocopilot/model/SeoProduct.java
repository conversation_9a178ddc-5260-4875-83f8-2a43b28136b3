package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.app.domain.seocopilot.constant.SeoEntityStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

/**
 * seo商品
 *
 * <AUTHOR>
 * Date 2023-12-11
 */
@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class SeoProduct {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * seo店铺ID
     */
    private Long shopId;

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * seo_web_page表主键id
     */
    private Long seoWebPageId;

    /**
     * shopify商品ID
     */
    private Long shopifyProductId;

    /**
     * 商品所属collection
     */
    private List<SeoCollectionSimple> collection;

    /**
     * 商品本身title
     */
    private String title;

    /**
     * 商品本身description
     */
    private String description;

    /**
     * 商品图片资源
     */
    private List<Image> image;

    /**
     * seo的title
     */
    private String seoTitle;

    /**
     * seo的description
     */
    private String seoDescription;

    /**
     * seo的url
     */
    private String seoHandle;

    /**
     * shopify更新时间
     */
    private Date shopifyUpdateAt;

    /**
     * 所处状态
     */
    private SeoEntityStatus status;

    /**
     * 对应页面收录状态
     */
    private String indexStatus;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 扩展属性
     */
    private SeoProductAttributes attributes = new SeoProductAttributes("{}");

    /**
     * 商品分类
     */
    private String productType;

    /**
     * 诊断问题类型
     */
    private List<String> issueType;

    private String shopifyGraphqlId;
}
