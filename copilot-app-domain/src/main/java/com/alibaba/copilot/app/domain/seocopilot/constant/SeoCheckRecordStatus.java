package com.alibaba.copilot.app.domain.seocopilot.constant;

import org.apache.commons.lang3.StringUtils;

/**
 * SeoWebPageType
 */
public enum SeoCheckRecordStatus {

    /**
     * 状态 问题未优化、优化建议生成中、优化建议已生成、问题已优化
     */


    /**
     * 问题未优化
     */
    WAITING,

    /**
     * 优化建议生成中
     */
    GENERATING,

    /**
     * 优化建议已生成
     */
    GENERATED,

    /**
     * 优化建议生成失败
     */
    GENERATEFAIL,

    /**
     * 优化建议应用失败
     */
    APPLYFAIL,

    /**
     * 问题已优化
     */
    FINISH,

    /**
     * 诊断没问题
     */
    NO_ISSUE;


    public static SeoCheckRecordStatus getLower(String... statusList) {
        if (statusList.length < 1) {
            return null;
        }
        SeoCheckRecordStatus result = null;
        for (String status : statusList) {
            if (StringUtils.isBlank(status)) {
                continue;
            }
            SeoCheckRecordStatus recordStatus = SeoCheckRecordStatus.valueOf(status);
            if (result == null) {
                result = recordStatus;
            }
            if (recordStatus.ordinal() < result.ordinal()) {
                result = recordStatus;
            }
        }
        return result;
    }

}
