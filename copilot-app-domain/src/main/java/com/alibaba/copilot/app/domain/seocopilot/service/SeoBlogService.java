package com.alibaba.copilot.app.domain.seocopilot.service;


import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.client.seocopilot.dto.*;
import com.alibaba.copilot.app.client.seocopilot.request.BlogProposalRequest;
import com.alibaba.copilot.app.client.seocopilot.request.SeoBlogSearchRequest;
import com.alibaba.copilot.app.client.seocopilot.response.ShopifyArticleDetailResponseV2;
import com.alibaba.copilot.app.domain.seocopilot.constant.LanguageEnum;
import com.alibaba.copilot.app.domain.seocopilot.dto.unsplash.SearchPhotosResponse;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoBlogArticle;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShop;
import com.alibaba.copilot.app.domain.seocopilot.request.GenerateAltRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.unsplash.SearchPhotosRequest;

import java.math.BigDecimal;
import java.util.List;

public interface SeoBlogService {

    /**
     * 获取所有的博客类目
     */
    List<SeoBlogDTO> getBlogs(Long shopifyShopId);

    /**
     * 按shopId 获取所有的博客类目
     */
    List<SeoBlogDTO> getBlogsByShopId(Long shopId);

    /**
     * 复合查询博客列表
     */
    PageWrapper<SeoBlogArticleDTO> queryArticles(Long shopId, SeoBlogSearchRequest request);

    boolean verifyAndDeleteIfArticleMissing(Long shopifyShopId, Long articleId);

    SeoBlogArticleDetailDTO queryArticleDetail(SeoShop seoShop, Long articleId);

    ShopifyArticleDetailResponseV2 queryArticleDetailV2(SeoShop seoShop, Long articleId);

    List<SeoBlogProposalDTO> proposalsRandom(BlogProposalRequest request);


    List<SeoShopDTO> queryShops();

    Boolean deleteBlogByArticleId(Long shopifyShopId, Long articleId, String deleteReason);

    Boolean retryGenerateArticle4GenerateFail(Long shopifyShopId, Long articleId);

    List<SeoBlogArticleHistoryRecordDTO> rollbackArticleHistoryRecords(Long shopifyShopId, Long articleId);

    SeoBlogArticleHistoryDetailDTO getArticleHistoryDetail(Long shopifyShopId, Long articleId);

    Boolean rollbackArticle(Long shopifyShopId, Long articleId, Long recordId);

    BigDecimal calculateArtileScore(SeoBlogArticle seoBlogArticle);

    Long translateArticle(LanguageEnum targetLanguage, Long shopifyShopId, Long articleId,
                          Long titleProposalId, Long bodyProposalId, Long tdkProposalId);

    Boolean autoTranslateRelatedArticles(Long shopifyShopId, Long articleId,
                          Long titleProposalId, Long bodyProposalId, Long tdkProposalId);

    String getSiteLang(Long shopifyShopId);

    String generateAlt(Long shopifyShopId, GenerateAltRequest request);

    SearchPhotosResponse selectPhotosFromUnsplash(Long shopifyShopId, SearchPhotosRequest request);

    String downloadPhotosFromUnsplash(Long shopifyShopId, String downloadLocation);
}
