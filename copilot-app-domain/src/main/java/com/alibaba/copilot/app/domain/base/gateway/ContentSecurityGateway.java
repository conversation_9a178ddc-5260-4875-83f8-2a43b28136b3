package com.alibaba.copilot.app.domain.base.gateway;

import com.alibaba.copilot.app.domain.base.request.security.CheckPictureRiskRequest;
import com.alibaba.copilot.app.domain.base.request.security.CheckTextRiskRequest;
import com.alibaba.copilot.boot.basic.result.Result;

import java.util.List;

/**
 * @desc: 内容风控
 * @author: yixiao.cx
 * @create: 2023-12-06
 **/
public interface ContentSecurityGateway {

    /**
     * 检查英文文本风险
     * @param text
     * @param outerId
     * @return
     */
    Result checkEnTextRisk(String text, String outerId);

    /**
     * 检查文本风险
     * @param request
     * @return
     */
    Result checkTextRisk(CheckTextRiskRequest request);

    /**
     * 检查图片风险
     * @param request
     * @return
     */
    Result checkPictureRisk(CheckPictureRiskRequest request);

}
