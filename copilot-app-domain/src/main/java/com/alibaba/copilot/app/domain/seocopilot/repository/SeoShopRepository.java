package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShop;

import java.util.List;
import java.util.Map;

/**
 * SeoShopRepository
 */
public interface SeoShopRepository {

    /**
     * 根据shopId查询Shop
     */
    SeoShop getShopById(Long shopId);

    /**
     * getShopsByUserId
     *
     * @param userId
     * @return
     */
    List<SeoShop> getShopsByUserId(Long userId);

    /**
     * getShopsByEmail
     *
     * @param email
     * @param siteType
     * @return
     */
    List<SeoShop> getShopsByEmail(String email, String siteType);

    /**
     * 根据shopifyShopId查询Shop
     */
    SeoShop getShopByShopifyShopId(Long shopifyShopId);

    /**
     * 根据shopifyShopId查询Shop
     */
    SeoShop getShopByShopifyShopIdWithoutEnv(Long shopifyShopId);

    /**
     * 根据shopifyShopId查询Shop，和上面的区别是不限制status
     */
    SeoShop getShopByShopifyShopIdNoStatus(Long shopifyShopId);

    /**
     * 根据shopifyShopId查询Shop，和上面的区别是不限制status
     */
    SeoShop getShopByShopifyShopIdAndDomainNoStatus(Long shopifyShopId,String shopifyShopDomain);

    SeoShop getShopByUserIdAndDomain(Long userId,String domain);

    SeoShop getShopByUserIdAndSystemDomain(Long userId, String systemDomain);

    /**
     * 根据shopDomain查询Shop
     *
     * @param shopDomain
     * @return
     */
    SeoShop getShopByShopifyShopDomain(String shopDomain);

    List<SeoShop> getShopsByGscEamil(String email);

    /**
     * 根据GSC Email查询店铺且不区分环境
     *
     * @param email
     * @return
     */
    List<SeoShop> getShopsByGscEmailWithoutEnv(String email);

    /**
     * 查询绑定店铺
     */
    List<SeoShop> getBindShops();

    /**
     * 查询绑定店铺
     */
    List<SeoShop> getBindShopsWithEnv(String env);

    /**
     * 分页查询绑定店铺
     */
    PageWrapper<SeoShop> getBindShopsWithPage(int pageNum, int pageSize);

    /**
     * 查询绑定店铺（传入环境标）
     */
    List<SeoShop> getBindShops(String env);

    /**
     * 获取已绑定GSC和GA的数据
     *
     * @return
     */
    List<SeoShop> getBindGoogleShops();

    /**
     * 获取已绑定GSC的店铺
     *
     * @return
     */
    List<SeoShop> getBindGscShops();

    /**
     * 获取blog新产品，且已完成GSC GA绑定可扫描获取google数据的店铺
     *
     * @return
     */
    List<SeoShop> getBlogScanShops();

    /**
     * 获取AlphaRank，且已完成GSC GA绑定可扫描获取google数据的店铺
     *
     * @return
     */
    List<SeoShop> getAlphaRankScanShops();

    /**
     * 新增SeoShop
     */
    Integer addShop(SeoShop seoShop);

    Integer deleteShopAttribute(Long id, String key);

    /**
     * 更新SeoShop扩展属性
     */
    Integer updateShopAttribute(Long id, Map<String, String> updateAttributeMap);

    /**
     * 更新SeoShop字段(更新Attribute请使用updateShopAttribute方法)
     * updateMap的key为数据库的字段名称
     */
    void updateShopColumn(Long id, Map<String, Object> updateMap);

    Integer updateDelete(Long id);

    /**
     * 更新店铺名称
     */
    Integer updateShopName(Long id, String shopName);

    /**
     * 根据domain和access token查询店铺
     */
    List<SeoShop> getShopByDomainAndAccessToken(String domain, String accessToken);

    /**
     * 更新shop
     */
    Integer updateShop(SeoShop seoShop);
}
