package com.alibaba.copilot.app.domain.aynctask.model;

import com.alibaba.copilot.app.domain.seocopilot.request.BlogContentAiPromptRequest;
import com.alibaba.copilot.boot.basic.data.Attributes;
import com.alibaba.fastjson.JSONArray;
import com.taobao.unifiedsession.core.json.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SeoCheckRecordAttributes
 */
@Slf4j
public class AsyncTaskAttributes extends Attributes {

    private static final String SUCCEED_COMMANDS = "succeedCommands";

    private static final String FAIL_COMMANDS = "failCommands";

    public AsyncTaskAttributes(String json) {
        super(json);
    }

    public static final String EXE_TRACE_ID = "exeTraceId";

    public static final String JOB_TYPE = "jobType";

    public static final String BATCH_ID = "batchId";

    public static final String OPTIMIZE_LEVEL = "optimizeLevel";

    public static final String MODULE = "module";

    public static final String CHECK_RECORD_IDS = "checkRecordIds";

    public static final String BLOG_ARTICLE_ID = "blogArticleId";

    private static final String KEYWORDS = "keywords";

    /**
     * 是否自动触发
     */
    public static final String IS_AUTO = "isAuto";

    public static final String IS_FULL_MANAGED = "isFullManaged";

    public static final String NEW_CUS_SCAN_CHK = "newCusScanChk";

    public static final String SHOP_EMAIL = "shopEmail";

    public static final String SHOP_DOMAIN = "shopDomain";

    public static final String NOAUTH_DIAGNOSE_ID = "noAuthDiagnoseId";

    private static final String GENERATE_CONTENT_REQUEST = "generateContentRequest";

    public void setNoAuthDiagnoseId(Long noAuthDiagnoseId) {
        put(NOAUTH_DIAGNOSE_ID, noAuthDiagnoseId);
    }

    public Long getNoAuthDiagnoseId() {
        return getAsLong(NOAUTH_DIAGNOSE_ID);
    }

    public void setShopEmail(String shopEmail) {
        put(SHOP_EMAIL, shopEmail);
    }

    public String getShopEmail() {
        return getAsString(SHOP_EMAIL);
    }

    public void setShopDomain(String shopDomain) {
        put(SHOP_DOMAIN, shopDomain);
    }

    public String getShopDomain() {
        return getAsString(SHOP_DOMAIN);
    }

    public void setIsAuto(Boolean status) {
        put(IS_AUTO, status);
    }

    public Boolean getIsAuto() {
        return get(IS_AUTO, Boolean.class);
    }

    public void setIsFullManaged(Boolean isFullManaged) {
        put(IS_FULL_MANAGED, isFullManaged);
    }

    public Boolean getIsFullManaged() {
        return get(IS_FULL_MANAGED, Boolean.class);
    }

    public void setNewCusScanChk(Boolean newCusScanChk) {
        put(NEW_CUS_SCAN_CHK, newCusScanChk);
    }

    public Boolean getNewCusScanChk() {
        return get(NEW_CUS_SCAN_CHK, Boolean.class);
    }

    public void setExeTraceId(String traceId) {
        put(EXE_TRACE_ID, traceId);
    }

    public String getExeTraceId() {
        return get(EXE_TRACE_ID, String.class);
    }

    public void setJobType(String jobType) {
        put(JOB_TYPE, jobType);
    }

    public String getJobType() {
        return getAsString(JOB_TYPE);
    }

    public void setBatchId(String batchId) {
        put(BATCH_ID, batchId);
    }

    public String getBatchId() {
        return getAsString(BATCH_ID);
    }

    public void setOptimizeLevel(String batchId) {
        put(OPTIMIZE_LEVEL, batchId);
    }

    public String getOptimizeLevel() {
        return getAsString(OPTIMIZE_LEVEL);
    }

    public void setModules(List<String> modules) {
        put(MODULE, modules);
    }

    public List<String> getModules() {
        if (!containsKey(MODULE)) {
            return new ArrayList<>();
        }
        return get(MODULE, List.class);
    }

    public void setCheckRecordIds(List<String> checkRecordIds) {
        put(CHECK_RECORD_IDS, checkRecordIds);
    }

    public List<String> getCheckRecordIds() {
        if (!containsKey(CHECK_RECORD_IDS)) {
            return new ArrayList<>();
        }
        return get(CHECK_RECORD_IDS, List.class);
    }

    public void setBlogArticleId(Long checkRecordId) {
        put(BLOG_ARTICLE_ID, checkRecordId);
    }

    public Long getBlogArticleId() {
        return getAsLong(BLOG_ARTICLE_ID);
    }

    public void setKeywords(List<String> keywords) {
        put(KEYWORDS, keywords);
    }

    public List<String> getKeywords() {
        return get(KEYWORDS, List.class);
    }

    public synchronized void addSucceedCommand(String command) {
        JSONArray succceed = get(SUCCEED_COMMANDS, JSONArray.class);
        if (succceed == null) {
            succceed = new JSONArray();
        }
        if (succceed.contains(command)) {
            return;
        }
        succceed.add(command);
        put(SUCCEED_COMMANDS, succceed);
    }

    public List<String> getSucceedCommands() {
        JSONArray succceed = get(SUCCEED_COMMANDS, JSONArray.class);
        if (succceed == null) {
            return Lists.newArrayList();
        }
        return succceed.stream().map(cmd -> cmd.toString()).collect(Collectors.toList());
    }

    public synchronized void addFailCommand(String command) {
        JSONArray failCommads = get(FAIL_COMMANDS, JSONArray.class);
        if (failCommads == null) {
            failCommads = new JSONArray();
        }
        if (failCommads.contains(command)) {
            return;
        }
        failCommads.add(command);
        put(FAIL_COMMANDS, failCommads);
    }

    public List<String> getFailCommands() {
        JSONArray failCommands = get(FAIL_COMMANDS, JSONArray.class);
        if (failCommands == null) {
            return Lists.newArrayList();
        }
        return failCommands.stream().map(cmd -> cmd.toString()).collect(Collectors.toList());
    }

    public synchronized void deleteFailCommand(String command) {
        JSONArray failCommands = get(FAIL_COMMANDS, JSONArray.class);
        if (failCommands == null) {
            return;
        }
        failCommands.remove(command);
        put(FAIL_COMMANDS, failCommands);
    }

    public void setBlogContentAiPromptRequest(BlogContentAiPromptRequest request) {
        put(GENERATE_CONTENT_REQUEST, JSON.toJSONString(request));
    }

    public BlogContentAiPromptRequest getBlogContentAiPromptRequest() {
        try {
            return JSON.parseObject(getAsString(GENERATE_CONTENT_REQUEST), BlogContentAiPromptRequest.class);
        } catch (Exception e) {
            log.error("getGenerateContentRequest error={}", e.getMessage(), e);
            return null;
        }
    }
}
