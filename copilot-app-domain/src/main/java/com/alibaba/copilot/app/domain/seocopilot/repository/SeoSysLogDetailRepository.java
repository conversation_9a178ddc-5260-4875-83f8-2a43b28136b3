package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.domain.seocopilot.model.SeoSysLogDetail;

import java.util.List;

/**
 * SeoSysLogDetailRepository
 */
public interface SeoSysLogDetailRepository {

    /**
     * 保存SeoSysLogDetail
     */
    Integer saveSeoSysLogDetail(SeoSysLogDetail seoSysLogDetail);

    /**
     * 根据sysLogId查询SeoSysLogDetail
     */
    List<SeoSysLogDetail> getSeoSysLogDetailBySysLogId(Long sysLogId, List<String> checkStatus);

    /**
     * 删除
     *
     * @param shopId
     * @param taskId
     * @return
     */
    Integer deleteByShopIdAndTaskId(Long shopId, Long taskId);

    /**
     * 删除sysLogDetail
     *
     * @param shopId
     * @return
     */
    Integer deleteByShopId(Long shopId);
}
