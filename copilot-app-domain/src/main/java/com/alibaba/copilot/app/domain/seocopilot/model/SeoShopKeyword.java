package com.alibaba.copilot.app.domain.seocopilot.model;


import com.alibaba.copilot.app.domain.seocopilot.constant.KeywordSource;
import com.alibaba.copilot.app.domain.seocopilot.constant.KeywordType;
import com.alibaba.copilot.app.domain.seocopilot.constant.LanguageEnum;
import com.alibaba.copilot.app.client.seocopilot.constant.ShopKeywordPreference;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * seo shop keyword实体
 *
 * <AUTHOR>
@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class SeoShopKeyword {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 区分店铺
     */
    private Long shopId;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * keyword_difficulty
     */
    private BigDecimal keywordDifficulty;

    /**
     * search_volume
     */
    private Integer searchVolume;

    /**
     * quality_score
     */
    private BigDecimal qualityScore;

    /**
     * intent
     */
    private String intent;

    /**
     * position
     */
    private BigDecimal position;

    /**
     * traffic
     */
    private BigDecimal traffic;

    /**
     * ctr
     */
    private BigDecimal ctr;

    /**
     * clicks
     */
    private Integer clicks;

    /**
     * impressions
     */
    private Integer impressions;

    /**
     * category
     */
    private String category;

    /**
     * source
     */
    private List<KeywordSource> source = new ArrayList<>();

    private KeywordType type;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 关键词/话题语种
     */
    private LanguageEnum language;

    /**
     * 扩展字段
     */
    private SeoShopKeywordAttributes attributes = new SeoShopKeywordAttributes(null);

    /**
     * preference
     */
    private ShopKeywordPreference preference;

    /**
     * unsatisfied_reason
     */
    private String unsatisfiedReason;

}
