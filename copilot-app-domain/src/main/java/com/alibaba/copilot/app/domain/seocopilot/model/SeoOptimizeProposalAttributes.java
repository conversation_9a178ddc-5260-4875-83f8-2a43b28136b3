package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.boot.basic.data.Attributes;

import java.math.BigDecimal;

public class SeoOptimizeProposalAttributes extends Attributes {

    public SeoOptimizeProposalAttributes(String json) {
        super(json);
    }

    private static final String BLOG_SCORE = "blog_score";

    private static final String PLAGIARISM_SCORE = "plagiarismScore";

    private static final String PLAGIARISM_SCAN_ID = "plagiarismScanId";

    public BigDecimal getBlogScore() {
        return get(BLOG_SCORE, BigDecimal.class);
    }

    public void setBlogScore(BigDecimal blogScore) {
        put(BLOG_SCORE, blogScore);
    }

    public BigDecimal getPlagiarismScore() {return get(PLAGIARISM_SCORE, BigDecimal.class);}

    public void setPlagiarismScore(BigDecimal plagiarismScore) {put(PLAGIARISM_SCORE, plagiarismScore);}

    public String getPlagiarismScanId() { return get(PLA<PERSON>ARISM_SCAN_ID, String.class);}

    public void setPlagiarismScanId(String scanId) {put(PLAGIARISM_SCAN_ID, scanId);}

}
