package com.alibaba.copilot.app.domain.base.constant;

import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description
 * @email <EMAIL>
 * @date 2023/6/10
 */
@Getter
public enum FileExtEnum {
    /**
     * image
     */
    JPEG(".jpeg"),
    JPG(".jpg"),
    WEBP(".webp"),
    PNG(".png"),
    BMP(".bmp"),
    GIF(".gif"),
    TIFF(".tiff"),
    HEIC(".heic"),
    AVIF(".avif");

    /**
     * 扩展格式
     */
    private final String extStr;

    FileExtEnum(String extStr) {
        this.extStr = extStr;
    }

    public static FileExtEnum getEnumByName(String extStr) {
        return Arrays.stream(FileExtEnum.values())
                .filter(fileExtEnum -> fileExtEnum.getExtStr().equals(extStr))
                .findFirst()
                .orElse(null);
    }
}
