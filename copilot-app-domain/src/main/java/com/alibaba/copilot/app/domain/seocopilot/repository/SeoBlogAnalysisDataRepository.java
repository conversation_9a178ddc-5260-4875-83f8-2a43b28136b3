package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.domain.seocopilot.model.SeoBlogAnalysisData;

import java.util.List;

public interface SeoBlogAnalysisDataRepository {

    /**
     * 根据ShopId获取数据
     *
     * @param shopId
     * @return
     */
    List<SeoBlogAnalysisData> getByShopId(Long shopId);

    /**
     * 获取shop id 为空的数据
     *
     * @return
     */
    List<SeoBlogAnalysisData> getWithoutShopId();

    /**
     * 保存
     */
    Integer save(SeoBlogAnalysisData seoBlogAnalysisData);

    /**
     * 批量保存
     */
    void batchSave(List<SeoBlogAnalysisData> seoBlogAnalysisDatas);


    SeoBlogAnalysisData getByMultiturnIdempotentToken(Long shopId, String multiturnIdempotentToken);

    Integer updateContent(Long articleId, String content);

    Integer updateArticleId(Long id, Long articleId);

}
