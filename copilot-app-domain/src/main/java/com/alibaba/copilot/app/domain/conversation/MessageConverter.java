package com.alibaba.copilot.app.domain.conversation;

import com.alibaba.copilot.app.domain.seocopilot.model.ConversationMessage;
import com.alibaba.copilot.boot.basic.factory.Converter;
import com.alibaba.copilot.boot.llm.openai.data.completion.chat.ChatMessage;

public class MessageConverter implements Converter<ConversationMessage, ChatMessage> {
    public static final MessageConverter INSTANCE = new MessageConverter();

    @Override
    public ChatMessage convertA2B(ConversationMessage conversationMessage) {
        ChatMessage chatMessage = new ChatMessage();
        chatMessage.setContent(conversationMessage.getContent());
        chatMessage.setRole(conversationMessage.getRole());
        return chatMessage;
    }

    @Override
    public ConversationMessage convertB2A(ChatMessage chatMessage) {
        ConversationMessage conversationMessage = new ConversationMessage();
        conversationMessage.setContent(chatMessage.getContent());
        conversationMessage.setRole(chatMessage.getRole());
        return conversationMessage;
    }
}
