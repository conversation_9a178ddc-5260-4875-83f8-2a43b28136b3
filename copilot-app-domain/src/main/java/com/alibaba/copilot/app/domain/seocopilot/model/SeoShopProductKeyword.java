package com.alibaba.copilot.app.domain.seocopilot.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 店铺级商品关键词
 *
 * <AUTHOR>
 * @date 2024/5/15 4:53 下午
 */
@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class SeoShopProductKeyword {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺名
     */
    private String shopName;

    /**
     * 店铺域名
     */
    private String shopDomain;

    /**
     * 店铺分类
     */
    private String shopType;

    /**
     * 商品title
     */
    private String productTitle;

    /**
     * 商品description
     */
    private String productDescription;

    /**
     * seo title
     */
    private String seoTitle;

    /**
     * seo description
     */
    private String seoDescription;

    /**
     * seo url
     */
    private String seoHandle;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 扩展字段
     */
    private SeoShopProductKeywordAttributes attributes = new SeoShopProductKeywordAttributes(null);
}
