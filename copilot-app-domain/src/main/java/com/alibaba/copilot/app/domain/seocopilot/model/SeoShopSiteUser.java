package com.alibaba.copilot.app.domain.seocopilot.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * SeoShopSiteUser 实体
 */
@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class SeoShopSiteUser {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * seo_shop 表主键
     */
    private Long seoShopId;

    /**
     * UIC 用户id
     */
    private Long userId;

    /**
     * 站点类型
     * shopify/wordpress
     */
    private String siteType;

    /**
     * 状态：绑定/解绑
     * 1/0
     */
    private Boolean bindStatus;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * outerUserId
     */
    private String outerUserId;

    /**
     * email
     */
    private String email;

    /**
     * 扩展字段
     */
    private SeoShopSiteUserAttributes attributes = new SeoShopSiteUserAttributes("{}");

    /**
     * 登录体系 access token
     */
    private String loginAccessToken;

    /**
     * 登录体系 refresh token
     */
    private String loginRefreshToken;

    /**
     * 用户来源
     */
    private String source;
}
