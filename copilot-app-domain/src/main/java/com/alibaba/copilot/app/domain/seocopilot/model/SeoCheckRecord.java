package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.app.domain.seocopilot.constant.CheckEntityType;
import com.alibaba.copilot.app.domain.seocopilot.constant.DeviceType;
import com.alibaba.copilot.app.domain.seocopilot.constant.SeoCheckRecordStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * SeoCheckRecord 实体
 *
 * <AUTHOR>
 */
@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class SeoCheckRecord {
    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * seo copilot店铺ID
     */
    private Long shopId;

    /**
     * 批次ID
     */
    private String batchId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 诊断资源ID
     */
    private Long entityId;

    /**
     * 诊断资源类型
     */
    private CheckEntityType entityType;

    /**
     * 诊断类型
     */
    private String checkType;

    /**
     * 设备类型
     */
    private DeviceType deviceType;

    /**
     * 是否能够自动优化（程序做不了的）
     */
    private Boolean autoOptimize;

    /**
     * 诊断标题
     */
    private String title;

    /**
     * 诊断描述
     */
    private String description;

    /**
     * 原始内容
     */
    private String originalContent;

    /**
     * 状态 问题未优化、优化建议生成中、优化建议已生成、问题已优化
     */
    private SeoCheckRecordStatus status;

    /**
     * 优化后的优化建议ID
     */
    private Long proposalId;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 扩展字段
     */
    private SeoCheckRecordAttributes attributes = new SeoCheckRecordAttributes(null);

}
