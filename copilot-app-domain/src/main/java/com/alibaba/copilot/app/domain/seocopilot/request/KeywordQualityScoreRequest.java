package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class KeywordQualityScoreRequest {


    private List<Keyword> keywords;

    @JSONField(name = "parent_keywords")
    private List<String> parentKeywords;


    @Data
    public static class Keyword {
        private String keyword;

        private BigDecimal kd;

        private Integer volume;

        private BigDecimal cpc;

        private BigDecimal competition;

        private Long numberOfResults;

        private String trends;

        private String relatedRelevance;

        private String serpFeatures;

        private String intent;
    }

}
