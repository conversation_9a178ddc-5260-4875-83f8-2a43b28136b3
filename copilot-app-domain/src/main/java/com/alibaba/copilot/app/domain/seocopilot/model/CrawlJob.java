package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.app.client.seocopilot.constant.CrawlJobStatusEnum;
import com.alibaba.copilot.app.client.seocopilot.constant.CrawlJobType;
import com.alibaba.copilot.app.client.seocopilot.constant.CrawlTriggerTypeEnum;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 爬虫日志
 */
@Slf4j
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CrawlJob {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 域名
     */
    private String domain;

    /**
     * 触发类型
     */
    private CrawlTriggerTypeEnum triggerType;

    /**
     * job_type
     */
    private CrawlJobType jobType;

    /**
     * 爬虫任务ID(全量异步)
     */
    private String crawlJobId;

    /**
     * 任务状态
     */
    private CrawlJobStatusEnum jobStatus;

    /**
     * 心跳
     */
    private Date heartbeatDate;

    /**
     * 最大执行次数
     */
    private Integer maxRetryTimes;

    /**
     * 执行次数
     */
    private Integer executeCount;

    /**
     * 爬取参数
     */
    private String request;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 环境标记
     */
    private String env;

    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 场景编码
     */
    private String sceneCode;

    /**
     * 外部主键
     */
    private String outerId;
}
