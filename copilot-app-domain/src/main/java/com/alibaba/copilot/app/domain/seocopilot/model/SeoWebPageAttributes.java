package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.boot.basic.data.Attributes;

/**
 * SeoWebPageAttributes
 */
public class SeoWebPageAttributes extends Attributes {

    public SeoWebPageAttributes(String json) {
        super(json);
    }

    private static final String ATTR_FIRST_SEO_PC_SCORE = "firstPcSeoScore";
    private static final String ATTR_FIRST_SEO_MOBILE_SCORE = "firstMobileSeoScore";
    private static final String ATTR_FIRST_SHOP_INDEX = "firstShopIndex";
    private static final String ATTR_FIRST_KEYWORD_RANK = "firstKeywordRank";
    private static final String WEB_URL_INDEX_STATUS = "indexStatus";

    private static final String WEB_URL_INDEX_DATE="indexDate";

    private static final String COLLECTION_ID = "collectionId";

    private static final String COLLECTION_TITLE = "collectionTitle";

    private static final String PRODUCT_ID = "productId";

    private static final String MOLECULE = "molecule";

    private static final String DENOMINATOR = "denominator";

    public void setFirstPcSeoScore(String seoScore) {
        put(ATTR_FIRST_SEO_PC_SCORE, seoScore);
    }

    public String getFirstPcSeoScore() {
        return getAsString(ATTR_FIRST_SEO_PC_SCORE);
    }

    public void setFirstMobileSeoScore(String seoScore) {
        put(ATTR_FIRST_SEO_MOBILE_SCORE, seoScore);
    }

    public String getFirstMobileSeoScore() {
        return getAsString(ATTR_FIRST_SEO_MOBILE_SCORE);
    }

    public void setFirstShopIndex(String shopIndex) {
        put(ATTR_FIRST_SHOP_INDEX, shopIndex);
    }

    public String getFirstShopIndex() {
        return getAsString(ATTR_FIRST_SHOP_INDEX);
    }

    public void setFirstKeywordRank(String keywordRank) {
        put(ATTR_FIRST_KEYWORD_RANK, keywordRank);
    }

    public String getFirstKeywordRank() {
        return getAsString(ATTR_FIRST_KEYWORD_RANK);
    }

    public void setWebUrlIndex(String indexStatus) {
        put(WEB_URL_INDEX_STATUS, indexStatus);
    }

    public String getWebUrlIndex() {
        return getAsString(WEB_URL_INDEX_STATUS);
    }

    public void setWebUrlIndexDate(String indexDate) {
        put(WEB_URL_INDEX_DATE, indexDate);
    }

    public String getWebUrlIndexDate() {
        return getAsString(WEB_URL_INDEX_DATE);
    }

    public void setCollectionId(Long collectionId) {
        put(COLLECTION_ID, collectionId);
    }

    public Long getCollectionId() {
        return getAsLong(COLLECTION_ID);
    }

    public String getCollectionTitle() {
        return getAsString(COLLECTION_TITLE);
    }

    public void setCollectionTitle(String collectionTitle) {
        put(COLLECTION_TITLE, collectionTitle);
    }

    public void setProductId(Long productId) {
        put(PRODUCT_ID, productId);
    }

    public Long getProductId() {
        return getAsLong(PRODUCT_ID);
    }

    public void setMolecule(Integer molecule) {
        put(MOLECULE, molecule);
    }

    public Integer getMolecule() {
        return getAsInteger(MOLECULE);
    }

    public void setDenominator(Integer denominator) {
        put(DENOMINATOR, denominator);
    }

    public Integer getDenominator() {
        return getAsInteger(DENOMINATOR);
    }


}
