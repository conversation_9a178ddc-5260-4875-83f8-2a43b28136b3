package com.alibaba.copilot.app.domain.seocopilot.service;

public interface GoogleBindChangedEventService {
    /**
     * 店铺数据抓取，存到seo_shop_google_data表
     * @param shopId
     */
    void fetchGoogleDataForShop(Long shopId,Integer days);
    /**
     * 绑定域名变化，重新探测GSC GA有效性
     * @param shopId
     */
    void checkFetchGoogleDataValidity(Long shopId);

    /**
     * 店铺页面纬度数据抓取，存到seo_shop_google_data表
     * @param shopId
     */
    void fetchPageGoogleDataForShop(Long shopId,Integer days);
}
