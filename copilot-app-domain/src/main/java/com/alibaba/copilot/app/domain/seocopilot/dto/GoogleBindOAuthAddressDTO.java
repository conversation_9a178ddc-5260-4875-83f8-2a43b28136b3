package com.alibaba.copilot.app.domain.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GoogleBindOAuthAddressDTO {
    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * shop domain
     */
    private String shopDomain;

    /**
     * GSC绑定地址
     */
    private String gscOauthAddr;

    /**
     * GA绑定地址
     */
    private String gaOauthAddr;
}
