package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.copilot.app.domain.seocopilot.constant.LanguageEnum;
import lombok.Data;

@Data
public class TranslateArticleRequest {

    private String targetLanguage;

    private Long shopId;

    private Long articleId;

    private Long titleProposalId;

    private Long bodyProposalId;

    private Long tdkProposalId;


    public LanguageEnum convertTargetLanguage2Enum() {
        return LanguageEnum.fromValue(targetLanguage);
    }
}
