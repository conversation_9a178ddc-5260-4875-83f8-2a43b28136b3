package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.domain.seocopilot.model.SeoBlog;

import java.util.List;

public interface SeoBlogRepository {

    List<SeoBlog> getByShopId(Long shopId);

    SeoBlog getOneByShopId(Long shopId);

    List<SeoBlog> getByShopifyBlogId(Long shopifyBlogId);

    List<SeoBlog> getByShopifyBlogId(Long shopId, Long shopifyBlogId);

    SeoBlog getById(Long id);

    Long save(SeoBlog seoBlog);

    Integer deleteByShopId(Long shopId);

    SeoBlog getByShopIdAndTitle(Long shopId, String title);
}
