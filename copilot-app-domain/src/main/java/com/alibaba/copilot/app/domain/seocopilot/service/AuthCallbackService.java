package com.alibaba.copilot.app.domain.seocopilot.service;

import com.alibaba.copilot.app.client.seocopilot.request.AuthAuthorizeRequest;
import com.alibaba.copilot.app.client.seocopilot.request.AuthLoginUrlRequest;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Map;

public interface AuthCallbackService {

    String getLoginUrl(Map<String, String> params,HttpServletResponse response) throws URISyntaxException, IOException;

    Boolean authorize(AuthAuthorizeRequest request, HttpServletResponse response) throws IOException;

    Boolean logout(HttpServletResponse response);

}
