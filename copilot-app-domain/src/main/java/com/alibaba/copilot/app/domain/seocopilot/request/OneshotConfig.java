package com.alibaba.copilot.app.domain.seocopilot.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class OneshotConfig {
    private String device;
    private boolean fullPage;
    private String url;
    private int ttl;
    private List<String> script;
    private String format;
    private boolean lazy;
    private String bucMode;
    private String viewport;
    private int isSync;

}
