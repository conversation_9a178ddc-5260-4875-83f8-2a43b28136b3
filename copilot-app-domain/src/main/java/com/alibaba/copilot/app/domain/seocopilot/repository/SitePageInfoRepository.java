package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.domain.seocopilot.model.SitePageInfo;

import java.util.Date;
import java.util.List;

public interface SitePageInfoRepository {

    List<SitePageInfo> getBySiteId(Long siteId);

    List<SitePageInfo> getBySiteIdAndUrl(Long siteId, String url);

    void save(SitePageInfo sitePageInfo);

    void batchSave(List<SitePageInfo> sitePageInfos);

    void batchUpdate(List<Long> idList,
                     Date gmtModified,
                     String type,
                     String content,
                     String cleanContent,
                     String ossUrl);
}
