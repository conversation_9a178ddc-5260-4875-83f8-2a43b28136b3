package com.alibaba.copilot.app.domain.seocopilot.utils;

import com.alibaba.copilot.app.domain.seocopilot.dto.NumberUnitDTO;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class NumberConverterUtil {

    public static final int ZERO_SCALE = 0;

    public static final int TWO_SCALE = 2;


    private static BigDecimal stripTrailingZeros(BigDecimal value, Integer scale, Boolean needZeroPart) {
        if (value == null) {
            return BigDecimal.ZERO;
        }
        if (value.scale() > 0 && value.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) != 0) {
            // 如果存在非0的有效小数部分，则按照scale保留小数 eg:1.06 -> 1.06
            return value.setScale(scale, RoundingMode.HALF_UP);
        } else if (value.scale() > 0) {
            // 小数部分全是0，则按照scale保留整数 eg:1.00 -> 1
            if (needZeroPart) {
                return value.setScale(scale, RoundingMode.HALF_UP);
            } else {
                return value.setScale(0, RoundingMode.HALF_UP);
            }
        } else {
            // 小数部分为0，则按照scale保留整数 eg:1 -> 1
            return value.setScale(0, RoundingMode.HALF_UP);
        }
    }

    /**
     * 数据单位转换 10000 -> 10K 10000000 -> 10M
     */
    public static NumberUnitDTO numberConverter(BigDecimal number, Integer scale) {
        if (number == null) {
            return NumberUnitDTO.builder().number(BigDecimal.valueOf(0)).real(BigDecimal.valueOf(0)).unit("").build();
        }
        if (scale == null) {
            scale = 1;
        }
        BigDecimal absNumber = number.abs();
        if (absNumber.compareTo(BigDecimal.valueOf(1e3)) < 0) {
            return NumberUnitDTO.builder().number(stripTrailingZeros(number, scale, false)).real(number).unit("").build();
        }
        if (absNumber.compareTo(BigDecimal.valueOf(1e6)) < 0) {
            BigDecimal n = number.divide(BigDecimal.valueOf(1e3), scale, RoundingMode.HALF_UP);
            return NumberUnitDTO.builder().number(stripTrailingZeros(n, scale, true)).real(number).unit("K").build();
        }
        BigDecimal n = number.divide(BigDecimal.valueOf(1e6), scale, RoundingMode.HALF_UP);
        return NumberUnitDTO.builder().number(stripTrailingZeros(n, scale, true)).real(number).unit("M").build();
    }

    /**
     * 数据单位转换 10000 -> 10K 10000000 -> 10M
     */
    public static NumberUnitDTO formatNumberWithUnit(BigDecimal number, Integer scale) {
        if (number == null) {
            return NumberUnitDTO.builder().number(null).real(null).unit("").build();
        }
        if (scale == null) {
            scale = 1;
        }
        BigDecimal absNumber = number.abs();
        if (absNumber.compareTo(BigDecimal.valueOf(1e3)) < 0) {
            return NumberUnitDTO.builder().number(stripTrailingZeros(number, scale, false)).real(number).unit("").build();
        }
        if (absNumber.compareTo(BigDecimal.valueOf(1e6)) < 0) {
            BigDecimal n = number.divide(BigDecimal.valueOf(1e3), scale, RoundingMode.HALF_UP);
            return NumberUnitDTO.builder().number(stripTrailingZeros(n, scale, true)).real(number).unit("K").build();
        }
        BigDecimal n = number.divide(BigDecimal.valueOf(1e6), scale, RoundingMode.HALF_UP);
        return NumberUnitDTO.builder().number(stripTrailingZeros(n, scale, true)).real(number).unit("M").build();
    }

    public static NumberUnitDTO numberConverter(Long number, Integer scale) {
        if (number == null) {
            return NumberUnitDTO.builder().number(BigDecimal.ZERO).real(BigDecimal.valueOf(0)).unit("").build();
        }
        return numberConverter(BigDecimal.valueOf(number), scale);
    }

    public static NumberUnitDTO numberConverter(Double number, Integer scale) {
        if (number == null) {
            return NumberUnitDTO.builder().number(BigDecimal.ZERO).real(BigDecimal.valueOf(0)).unit("").build();
        }
        return numberConverter(BigDecimal.valueOf(number), scale);
    }

    public static NumberUnitDTO numberConverter(Integer number, Integer scale) {
        if (number == null) {
            return NumberUnitDTO.builder().number(BigDecimal.ZERO).real(BigDecimal.valueOf(0)).unit("").build();
        }
        return numberConverter(BigDecimal.valueOf(number), scale);
    }

    /**
     * 时间单位转换，传入秒级时间 10 -> 10s  60 -> 1min  3800 -> 1.06h
     */
    public static NumberUnitDTO timeConverter(BigDecimal t, Integer scale) {
        if (t == null) {
            return NumberUnitDTO.builder().number(BigDecimal.valueOf(0)).real(BigDecimal.valueOf(0)).unit("s").build();
        }
        if (scale == null) {
            scale = 1;
        }
        // 定义时间转换常量
        final BigDecimal SECONDS_IN_A_MINUTE = BigDecimal.valueOf(60);
        final BigDecimal SECONDS_IN_AN_HOUR = BigDecimal.valueOf(3600);
        BigDecimal tAbs = t.abs();
        if (tAbs.compareTo(SECONDS_IN_A_MINUTE) < 0) {
            return NumberUnitDTO.builder().number(stripTrailingZeros(t, scale, false)).real(t).unit("s").build();
        }
        if (tAbs.compareTo(SECONDS_IN_AN_HOUR) < 0) {
            BigDecimal n = t.divide(SECONDS_IN_A_MINUTE, scale, RoundingMode.HALF_UP);
            return NumberUnitDTO.builder().number(stripTrailingZeros(n, scale, true)).real(t).unit("min").build();
        }
        BigDecimal n = t.divide(SECONDS_IN_AN_HOUR, scale, RoundingMode.HALF_UP);
        return NumberUnitDTO.builder().number(stripTrailingZeros(n, scale, true)).real(t).unit("h").build();
    }
}
