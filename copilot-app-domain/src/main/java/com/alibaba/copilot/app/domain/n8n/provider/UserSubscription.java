package com.alibaba.copilot.app.domain.n8n.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.app.client.flow.FlowNodeExecuteSPI;
import com.alibaba.copilot.app.client.flow.model.FlowNodeRequest;
import com.alibaba.copilot.app.client.flow.model.FlowNodeResponse;
import com.alibaba.copilot.app.domain.seocopilot.constant.SeoConstant;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShop;
import com.alibaba.copilot.app.domain.seocopilot.repository.SeoShopRepository;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.enabler.client.subscription.dto.SubscribedAppDTO;
import com.alibaba.copilot.enabler.client.subscription.facade.SubscriptionHsfApi;
import com.alibaba.copilot.enabler.client.subscription.request.SubscribedAppInfoQuery;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@HSFProvider(serviceInterface = FlowNodeExecuteSPI.class, serviceVersion = "1.0.0.SEO_COPILOT.isUserSubscribed")
public class UserSubscription implements FlowNodeExecuteSPI {

    public static final String IS_SUBSCRIBED_KEY = "isSubscribed";
    public static final String EMAIL_KEY = "email";
    @Autowired
    private SubscriptionHsfApi subscriptionHsfApi;

    @Resource
    private SeoShopRepository seoShopRepository;

    @Override
    public SingleResult<FlowNodeResponse> execute(FlowNodeRequest request) {
        Long userId = request.getData().getLong("userId");
        SubscribedAppInfoQuery subscribedAppInfoQuery = SubscribedAppInfoQuery.builder()
                .userId(userId)
                .appCode(SeoConstant.APP_CODE)
                .build();

        SingleResult<List<SubscribedAppDTO>> singleResult = subscriptionHsfApi.getSubscribedAppList(subscribedAppInfoQuery);
        log.info("UserSubscription.getSubscribedAppList res {}", JSON.toJSONString(singleResult));
        FlowNodeResponse response = new FlowNodeResponse();
        response.setAppCode(request.getAppCode());
        response.setActionCode(request.getActionCode());
        JSONObject data = new JSONObject();
        List<SubscribedAppDTO> data1 = singleResult.getData();
        List<SubscribedAppDTO> collect = data1.stream().filter(w -> !w.getCurrentPlan().equals("Starter") && w.getStatus().equals("IN_EFFECT")).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            data.put(IS_SUBSCRIBED_KEY, "true");
        } else {
            data.put(IS_SUBSCRIBED_KEY, "false");
            String email = "";
            SeoShop seoShop = seoShopRepository.getShopByShopifyShopId(userId);
            if (Objects.nonNull(seoShop) && StringUtils.isNotBlank(seoShop.getShopEmail())) {
                email = seoShop.getShopEmail();
            }
            data.put(EMAIL_KEY, email);
        }

        response.setData(data);
        return SingleResult.buildSuccess(response);
    }
}
