package com.alibaba.copilot.app.domain.seocopilot.request.unsplash;


import com.alibaba.copilot.boot.basic.request.MapQuery;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class SearchPhotosRequest implements MapQuery {

    /**
     * 搜索词
     */
    private String query;

    /**
     * 页码
     */
    private Integer page = 1;

    /**
     * 每页大小
     */
    @JSONField(name = "per_page")
    private Integer perPage = 30;


    /**
     * 每页大小:如何对照片进行排序。（可选；默认值relevant：）。有效值为latest和relevant。
     */
    @JSONField(name = "order_by")
    private String orderBy = "relevant";

    /**
     * 用于缩小搜索范围的集合 ID。可选。如果有多个，请用逗号分隔。
     */
    private String collections;

    /**
     * 按内容安全性限制结果。（可选；默认值low：）。有效值为low和high。
     */
    @JSONField(name = "content_filter")
    private String contentFilter = "low";

    /**
     * 按颜色过滤结果:black_and_white, black, white, yellow, orange, red, purple, magenta, green, teal, and blue.
     */
    private String color;

    /**
     * 按照片方向过滤。可选。（有效值：landscape、portrait、squarish）
     */
    private String orientation;

}
