package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.LinkedList;
import java.util.List;

/**
 * OptionDTO
 *
 * <AUTHOR>
 * @date 2024/7/8 4:11 下午
 */
@Data
public class OptionDTO {

    /**
     * id
     */
    private String id;

    /**
     * productId
     */
    @JSONField(name = "product_id")
    private String productId;

    /**
     * name
     */
    private String name;

    /**
     * position
     */
    private int position;

    /**
     * values
     */
    private List<String> values = new LinkedList<>();
}
