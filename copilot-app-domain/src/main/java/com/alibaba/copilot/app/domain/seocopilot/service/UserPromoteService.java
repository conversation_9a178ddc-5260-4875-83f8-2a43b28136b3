package com.alibaba.copilot.app.domain.seocopilot.service;

import com.alibaba.copilot.app.client.seocopilot.request.UserPromptCollectRequest;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoUserPromoteCheckRecord;

import java.util.List;

public interface UserPromoteService {

    Long triggerWebsiteDiagnoseNoAuth(UserPromptCollectRequest request);

    Long saveSeoUserPromoteCheckRecord(SeoUserPromoteCheckRecord checkRecord);

    Long triggerWebsiteNoAuth(UserPromptCollectRequest request);

}
