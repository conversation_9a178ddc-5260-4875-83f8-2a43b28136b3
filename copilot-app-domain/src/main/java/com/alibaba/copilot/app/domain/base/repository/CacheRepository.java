package com.alibaba.copilot.app.domain.base.repository;


import com.alibaba.copilot.boot.tools.lock.Lock;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-08-22
 **/
public interface CacheRepository {
    /**
     * 添加到缓存
     *
     * @param key
     * @param value
     * @return
     */
    boolean put(String key, Serializable value);

    boolean mput(Map<String, String> kv, long expireInSeconds);

    /**
     * 添加到缓存(待超时时间，s)
     *
     * @param key
     * @param value
     * @return
     */
    boolean put(String key, Serializable value, Long timeout);

    /**
     * 自增
     * @param key
     * @return
     */
    public long increment(String key);

    /**
     * set
     *
     * @param key
     * @param value
     * @return
     */
    boolean set(String key, Serializable value);

    /**
     * set(待超时时间，s)
     *
     * @param key
     * @param value
     * @return
     */
    boolean set(String key, Serializable value, Long timeout);

    /**
     * 从缓存中获取
     *
     * @param key
     * @return
     */
    Object get(String key);

    /**
     * 从缓存中删除
     *
     * @param keys
     * @return
     */
    boolean delete(String keys);

    /**
     * 从缓存中删除
     *
     * @param keys
     * @return
     */
    boolean delete(List<String> keys);

    /**
     * 获取分布式锁
     *
     * @param lockSpace
     * @param lockId
     * @return
     */
    Lock getLock(String lockSpace, String lockId);

    /**
     * 批量数值压入缓存队列
     *
     * @param queueKey
     * @param values
     * @return
     */
    Long pushCacheQueue(String queueKey, String... values);

    /**
     * 从缓存队列弹出指定数量值
     *
     * @param queueKey
     * @param count
     * @return
     */
    List<String> popCacheQueue(String queueKey, int count);

    /**
     * 缓存队列大小
     *
     * @param queueKey
     * @return
     */
    Long cacheQueueSize(String queueKey);

    Long addCacheSet(String setKey, String value, long expireSecond);

    List<String> randomCacheSet(String setKey, int count);

    Set<String> allCacheSet(String setKey);

    long removeCacheSet(String setKey, List<String> value);
}
