package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.app.domain.seocopilot.constant.CollectionType;
import com.alibaba.copilot.app.domain.seocopilot.constant.SeoEntityStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SeoCollection {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * shopify店铺ID
     */
    private Long shopId;

    /**
     * 批次id
     */
    private String batchId;

    /**
     * seo_web_page表主键id
     */
    private Long seoWebPageId;

    /**
     * shopify collection id
     */
    private Long shopifyCollectionId;

    /**
     * collection本身的title
     */
    private String title;

    /**
     * collection本身的description
     */
    private String description;

    /**
     * collection图片资源
     */
    private List<Image> image;

    /**
     * seo的title
     */
    private String seoTitle;

    /**
     * seo的description
     */
    private String seoDescription;

    /**
     * seo的url
     */
    private String seoHandle;

    /**
     * AlphaRank所处状态
     */
    private SeoEntityStatus status;

    /**
     * shopify更新时间
     */
    private Date shopifyUpdateAt;

    /**
     * 对应页面收录状态
     */
    private String indexStatus;

    /**
     * type
     */
    private CollectionType type;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 扩展字段
     */
    private SeoCollectionAttributes attributes = new SeoCollectionAttributes(null);
}
