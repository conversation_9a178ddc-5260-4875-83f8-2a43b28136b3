package com.alibaba.copilot.app.domain.base.model;

import java.util.Map;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain=true)
public class EdmRequest {
    /**
     * 活动Id
     */
    private Long id;

    /**
     * 收件邮箱
     */
    private String toEmail;

    /**
     * 发件邮箱 大多数场景下不用填
     */
    private String fromEmail;

    /**
     * 用于模板渲染的变量，也包含标题
     */
    private Object params;
}
