package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.copilot.app.domain.seocopilot.constant.BlogApplyProposalType;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ArticleBatchApplyRequest {
    /**
     * 优化类别
     */
    private List<BlogApplyProposalType> typeList;
    /**
     * 店铺shopifyID
     */
    private Long shopifyShopId;
}
