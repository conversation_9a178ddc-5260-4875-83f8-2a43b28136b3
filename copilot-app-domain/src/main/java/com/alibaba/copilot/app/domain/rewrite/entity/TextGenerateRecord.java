package com.alibaba.copilot.app.domain.rewrite.entity;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class TextGenerateRecord {
    private Long id;
    private Date gmtCreate;
    private Date gmtModified;
    private String userId;
    private String type;
    private String input;
    private String output;
    private Long promptTemplateId;
    private Attributes attributes;

    @Data
    public static class Output {
        private String text;
        private String detectedLanguage;
    }

    @Data
    public static class Attributes {
        private List<String> feedback;
    }
}
