package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.app.client.seocopilot.constant.ShopifyEntityTypeEnum;
import com.alibaba.copilot.boot.basic.data.Attributes;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * SeoCheckRecordAttributes
 */
public class SeoShopAttributes extends Attributes {

    public SeoShopAttributes(String json) {
        super(json);
    }


    /**
     * 初始化状态
     */
    public static final String INIT = "INIT";

    /**
     * 扫描中
     */
    public static final String SCANNING = "SCANNING";

    /**
     * 扫描完成
     */
    public static final String SCANNED = "SCANNED";

    /**
     * 扫描失败
     */
    public static final String SCANFAIL = "SCANFAIL";

    /**
     * 同步中
     */
    public static final String SYNCING = "SYNCING";

    /**
     * 同步完成
     */
    public static final String SYNCED = "SYNCED";

    /**
     * 同步失败
     */
    public static final String SYNCFAIL = "SYNCFAIL";

    /**
     * 诊断中
     */
    public static final String CHECKING = "CHECKING";

    /**
     * 诊断完成
     */
    public static final String CHECKED = "CHECKED";

    /**
     * 诊断失败
     */
    public static final String CHECKFAIL = "CHECKFAIL";

    /**
     * 优化中
     */
    public static final String OPTIMIZING = "OPTIMIZING";

    /**
     * 优化完成
     */
    public static final String OPTIMIZED = "OPTIMIZED";

    /**
     * 优化失败
     */
    public static final String OPTIMIZEFAIL = "OPTIMIZEFAIL";

    /**
     * 应用完成（非全托管）
     */
    public static final String APPLIED = "APPLIED";

    /**
     * 页面模块
     */
    public static final String SEO_SCORE_MODULE = "seoScore";
    public static final String SEO_INDEX_MODULE = "seoIndex";
    public static final String SEO_RANK_MODULE = "seoRank";

    /**
     * 每个模块的状态应该是存在 初始化状态、诊断中、诊断完成、优化中、优化完成
     */
    public static final String SEO_SCORE_STATUS = "seoScoreStatus";
    public static final String SEO_INDEX_STATUS = "seoIndexStatus";
    public static final String SEO_RANK_STATUS = "seoRankStatus";

    public static final String SCORE_PRE_END_TIME = "scorePreEndTime";
    public static final String INDEX_PRE_END_TIME = "indexPreEndTime";
    public static final String RANK_PRE_END_TIME = "rankPreEndTime";

    public static final String SCORE_TASK_ID = "scoreTaskId";
    public static final String INDEX_TASK_ID = "indexTaskId";
    public static final String RANK_TASK_ID = "rankTaskId";

    public static final String BLOG_GEN_COUNT = "blogGenCount";

    /**
     * 首次提示文案tag记录
     */
    public static final String REMARK_TAGS = "remarkTags";

    /**
     * 安装应用首次扫描
     */
    public static final String FIRST_SCAN_TAG = "firstScanTag";

    /**
     * 新用户安装应用引导扫描诊断是否完成
     */
    public static final String NEW_CUS_SCAN_CHK_FIN = "newCusScanChkFin";

    public static final String CURRENT_SHOP = "currentShop";

    /**
     * GSC标记
     */
    public static final String GSC_STATUS = "gscStatus";

    /**
     * GA标记
     */
    public static final String GA_STATUS = "gaStatus";

    /**
     * GA PropertiesId
     */
    public static final String GA_PROPERTIES_ID = "gaPropertiesId";

    /**
     * GA status
     */
    public static final String GA_STATUS_VALID = "valid";

    /**
     * GA status
     */
    public static final String GA_STATUS_INVALID = "invalid";

    /**
     * GA status
     */
    public static final String GA_STATUS_UNBOUND = "unbound";

    /**
     * GSC status
     */
    public static final String GSC_STATUS_VAID = "valid";

    /**
     * GSC status
     */
    public static final String GSC_STATUS_INVAID = "invalid";

    /**
     * GSC status
     */
    public static final String GSC_STATUS_UNBOUND = "unbound";

    /**
     * 全托管诊断异步任务开始时间
     */
    public static final String FULL_CHECK_START_TIME = "fullCheckStartTime";

    /**
     * 全托管优化结束任务ID
     */
    public static final String FULL_OPTINMIZE_END_TASK_ID = "fullOptimizeEndTaskId";

    /**
     * 全托管异步任务结束时间
     */
    public static final String FULL_OPTIMIZE_END_TIME = "fullOptimizeEndTime";


    /**
     * 非全托管首次点击Optimize提示
     */
    public static final String NO_FULL_MANAGED_OPTIMIZE_REMARK = "noFullManagedOptimizeRemark";

    /**
     * Blog多轮交互偏好关键词
     */
    public static final String BLOG_PREFERENCE_KEYWORDS = "blogPreferenceKeywords";
    /**
     * Blog多轮交互偏好类目
     */
    public static final String BLOG_PREFERENCE_CATEGORY = "blogPreferenceCategory";
    /**
     * Blog多轮交互偏好国家
     */
    public static final String BLOG_PREFERENCE_COUNTRY = "blogPreferenceCountry";
    /**
     * Blog多轮交互偏好风格
     */
    public static final String BLOG_PREFERENCE_STYLE = "blogPreferenceStyle";
    /**
     * Blog多轮交互偏好内链
     */
    public static final String BLOG_PREFERENCE_LINK = "blogPreferenceLink";

    /**
     * shop secretStatus
     *
     * @return
     */
    public static final String SHOP_SCRECT_STATUS = "shopSecretStatus";


    /***
     * ***********************发布平台绑定相关***********************
     */


    /**
     * 期望支持发布平台
     */
    public static final String EXPECT_SUPPORT_PUBLISH_PLATFORM = "expectPublishPlatform";

    /**
     * 发布平台
     */
    public static final String PUBLISH_PLATFORM = "platform";

    /**
     * 发布平台-wordpress.org
     */
    public static final String PUBLISH_PLATFORM_WORDPRESS_ORG = "wordpress.org";

    /**
     * 发布平台-wordpress.com
     */
    public static final String PUBLISH_PLATFORM_WORDPRESS_COM = "wordpress.com";

    /**
     * 发布平台-shopify
     */
    public static final String PUBLISH_PLATFORM_SHOPIFY = "shopify";

    /**
     * 发布平台-shoplazza
     */
    public static final String PUBLISH_PLATFORM_SHOPLAZZA = "shoplazza";

    /**
     * 绑定账号
     */
    public static final String PUBLISH_PLATFORM_ACCOUNT = "platformAccount";

    /**
     * 绑定异常信息
     */
    public static final String PUBLISH_PLATFORM_BING_ERROR = "platformBingError";

    /**
     * 平台绑定状态
     */
    public static final String PUBLISH_PLATFORM_STATUS = "platformStatus";

    /**
     * 平台绑定状态-有效
     */
    public static final String PUBLISH_PLATFORM_VALID = "valid";

    /**
     * 平台绑定状态-无效
     */
    public static final String PUBLISH_PLATFORM_INVALID = "invalid";

    /**
     * 平台绑定状态-未绑定
     */
    public static final String PUBLISH_PLATFORM_UNBOUND = "unbound";

    /***
     * ***********************发布平台绑定相关***********************
     */

    public Boolean getShopScrectStatus() {
        return Boolean.valueOf(getAsString(SHOP_SCRECT_STATUS));
    }

    public void setShopScrectStatus(String status) {
        put(SHOP_SCRECT_STATUS, status);
    }

    public void removeShopScrectStatus() {
        remove(SHOP_SCRECT_STATUS);
    }

    public String getGscStatus() {
        return getAsString(GSC_STATUS);
    }

    public void setGscStatus(String status) {
        put(GSC_STATUS, status);
    }

    public String getGaStatus() {
        String status = getAsString(GA_STATUS);
        if (StringUtils.isEmpty(status)) {
            return GA_STATUS_UNBOUND;
        }
        return status;
    }

    public void setGaStatus(String status) {
        put(GA_STATUS, status);
    }

    public String getGaPropertiesId() {
        return getAsString(GA_PROPERTIES_ID);
    }

    public void setGaPropertiesId(String propertiesId) {
        put(GA_PROPERTIES_ID, propertiesId);
    }

    public String getFirstScanTag() {
        return getAsString(FIRST_SCAN_TAG);
    }

    public void setFirstScanTag(String firstScanTag) {
        put(FIRST_SCAN_TAG, firstScanTag);
    }

    public String getNewCusScanChkFin() {
        return getAsString(NEW_CUS_SCAN_CHK_FIN);
    }

    public void setNewCusScanChkFin(String newCusScanChkFin) {
        put(NEW_CUS_SCAN_CHK_FIN, newCusScanChkFin);
    }

    public String getCurrentShop() {
        return getAsString(CURRENT_SHOP);
    }

    public void setCurrentShop(String currentShop) {
        put(CURRENT_SHOP, currentShop);
    }

    public void setFullOptinmizeEndTaskId(Long taskId) {
        put(FULL_OPTINMIZE_END_TASK_ID, taskId);
    }

    public Long getFullOptinmizeEndTaskId() {
        return getAsLong(FULL_OPTINMIZE_END_TASK_ID);
    }


    public void setFullCheckStartTime(String startTime) {
        put(FULL_CHECK_START_TIME, startTime);
    }

    public void removeFullCheckStartTime() {
        remove(FULL_CHECK_START_TIME);
    }

    public Date getFullCheckStartTime() {
        if (!containsKey(FULL_CHECK_START_TIME)) {
            return null;
        }
        String startTime = getAsString(FULL_CHECK_START_TIME);
        if (StringUtils.isBlank(startTime)) {
            return null;
        }
        return JSON.parseObject(startTime, Date.class);
    }

    public void setFullOptimizeEndTime(String endTime) {
        put(FULL_OPTIMIZE_END_TIME, endTime);
    }

    public Date getFullOptimizeEndTime() {
        if (!containsKey(FULL_OPTIMIZE_END_TIME)) {
            return null;
        }
        return JSON.parseObject(getAsString(FULL_OPTIMIZE_END_TIME), Date.class);
    }

    public void setRemarkTags(List<String> remarkTags) {
        put(REMARK_TAGS, remarkTags);
    }

    public List<String> getRemarkTags() {
        if (!containsKey(REMARK_TAGS)) {
            return new ArrayList<>();
        }
        return get(REMARK_TAGS, List.class);
    }

    public void setScoreTaskId(Long taskId) {
        put(SCORE_TASK_ID, taskId);
    }

    public Long getScoreTaskId() {
        return getAsLong(SCORE_TASK_ID);
    }

    public void setIndexTaskId(Long taskId) {
        put(INDEX_TASK_ID, taskId);
    }

    public Long getIndexTaskId() {
        return getAsLong(INDEX_TASK_ID);
    }

    public void setRankTaskId(Long taskId) {
        put(RANK_TASK_ID, taskId);
    }

    public Long getRankTaskId() {
        return getAsLong(RANK_TASK_ID);
    }

    public void setBlogGenCount(Integer blogGenCount) {
        put(BLOG_GEN_COUNT, blogGenCount);
    }

    public Integer getBlogGenCount() {
        return getAsInteger(BLOG_GEN_COUNT);
    }

    public void incrBlogGenCount() {
        Integer count = getBlogGenCount();
        if (count == null) {
            put(BLOG_GEN_COUNT, 1);
        } else {
            count++;
            put(BLOG_GEN_COUNT, count);
        }
    }

    /**
     * 新手村步骤
     */
    public static final String FINISH_GSC = "finishGsc";
    public static final String START_CHECK = "startCheck";
    public static final String FINISH_CHECK = "finishCheck";
    public static final String START_OPTIMIZE = "startOptimize";
    public static final String FINISH_OPTIMIZE = "finishOptimize";

    /**
     * 新手村步骤
     *
     * @param step
     */
    public static final String FULL_MANAGED_STEP = "fullManagedStep";


    public void setFullManagedStep(String step) {
        put(FULL_MANAGED_STEP, step);
    }

    public void removeFullManagedStep() {
        remove(FULL_MANAGED_STEP);
    }

    public String getFullManagedStep() {
        return getAsString(FULL_MANAGED_STEP);
    }


    public void setScorePreEndTime(Long endTimeSeconds) {
        put(SCORE_PRE_END_TIME, endTimeSeconds);
    }

    public Long getScorePreEndTime() {
        if (!containsKey(SCORE_PRE_END_TIME)) {
            return null;
        }
        return getAsLong(SCORE_PRE_END_TIME);
    }

    public void setIndexPreEndTime(Long endTimeSeconds) {
        put(INDEX_PRE_END_TIME, endTimeSeconds);
    }

    public Long getIndexPreEndTime() {
        if (!containsKey(INDEX_PRE_END_TIME)) {
            return null;
        }
        return getAsLong(INDEX_PRE_END_TIME);
    }

    public void setRankPreEndTime(Long endTimeSeconds) {
        put(RANK_PRE_END_TIME, endTimeSeconds);
    }

    public Long getRankPreEndTime() {
        if (!containsKey(RANK_PRE_END_TIME)) {
            return null;
        }
        return getAsLong(RANK_PRE_END_TIME);
    }

    public void setSeoScoreStatus(String status) {
        put(SEO_SCORE_STATUS, status);
    }

    public String getSeoScoreStatus() {
        return getAsString(SEO_SCORE_STATUS);
    }

    public String getSeoSyncStatus(ShopifyEntityTypeEnum type) {
        return getAsString(ShopifyEntityTypeEnum.getSeoJobType(type));
    }

    public void setSeoIndexStatus(String status) {
        put(SEO_INDEX_STATUS, status);
    }

    public String getSeoIndexStatus() {
        return getAsString(SEO_INDEX_STATUS);
    }

    public void setSeoRankStatus(String status) {
        put(SEO_RANK_STATUS, status);
    }

    public void setSeoSyncStatus(String key, String status) {
        put(key, status);
    }

    public String getSeoRankStatus() {
        return getAsString(SEO_RANK_STATUS);
    }

    public void setBlogPreferenceKeywords(List<String> keywords) {
        put(BLOG_PREFERENCE_KEYWORDS, keywords);
    }

    public List<String> getBlogPreferenceKeywords() {
        if (!containsKey(BLOG_PREFERENCE_KEYWORDS)) {
            return new ArrayList<>();
        }
        return get(BLOG_PREFERENCE_KEYWORDS, List.class);
    }

    public void setBlogPreferenceCategory(String category) {
        put(BLOG_PREFERENCE_CATEGORY, category);
    }

    public String getBlogPreferenceCategory() {
        return getAsString(BLOG_PREFERENCE_CATEGORY);
    }

    public void setBlogPreferenceCountry(String country) {
        put(BLOG_PREFERENCE_COUNTRY, country);
    }

    public String getBlogPreferenceCountry() {
        return getAsString(BLOG_PREFERENCE_COUNTRY);
    }

    public void setBlogPreferenceStyle(String style) {
        put(BLOG_PREFERENCE_STYLE, style);
    }

    public String getBlogPreferenceStyle() {
        return getAsString(BLOG_PREFERENCE_STYLE);
    }

    public void setBlogPreferenceLink(String link) {
        put(BLOG_PREFERENCE_LINK, link);
    }

    public String getBlogPreferenceLink() {
        return getAsString(BLOG_PREFERENCE_LINK);
    }


    public void setPublishPlatform(String platform) {
        put(PUBLISH_PLATFORM, platform);
    }

    public String getPublishPlatform() {
        return getAsString(PUBLISH_PLATFORM);
    }

    public void setPublishPlatformStatus(String status) {
        put(PUBLISH_PLATFORM_STATUS, status);
    }

    public String getPublishPlatformStatus() {

        if (StringUtils.isBlank(getAsString(PUBLISH_PLATFORM_STATUS))) {
            return PUBLISH_PLATFORM_UNBOUND;
        }
        return getAsString(PUBLISH_PLATFORM_STATUS);
    }

    public void setPublishPlatformBingError(String errorMsg) {
        put(PUBLISH_PLATFORM_BING_ERROR, errorMsg);
    }

    public String getPublishPlatformBingError() {
        return getAsString(PUBLISH_PLATFORM_BING_ERROR);
    }

    public void setPublishPlatformAccount(String account) {
        put(PUBLISH_PLATFORM_ACCOUNT, account);
    }

    public String getPublishPlatformAccount() {
        return getAsString(PUBLISH_PLATFORM_ACCOUNT);
    }

    public void setExpectPublishPlatform(String platform) {
        put(EXPECT_SUPPORT_PUBLISH_PLATFORM, platform);
    }

    public String getExpectPublishPlatform() {
        return getAsString(EXPECT_SUPPORT_PUBLISH_PLATFORM);
    }
}
