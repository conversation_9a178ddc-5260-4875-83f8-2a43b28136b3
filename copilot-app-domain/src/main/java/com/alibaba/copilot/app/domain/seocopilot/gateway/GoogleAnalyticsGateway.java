package com.alibaba.copilot.app.domain.seocopilot.gateway;

import com.alibaba.copilot.app.domain.seocopilot.dto.*;
import com.alibaba.copilot.app.domain.seocopilot.request.GAQueryRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.OauthRefreshTokenRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.OauthTokenRequest;

public interface GoogleAnalyticsGateway {
    /**
     * Google token
     */
    OauthTokenDTO token(OauthTokenRequest request);
    /**
     * Google token refresh
     */
    OauthRefreshTokenDTO refreshToken(OauthRefreshTokenRequest request);
    /**
     * Google Search Console-query
     */
    GAQueryResponseDTO runReport(String accessToken, String propertiesId, GAQueryRequest request, Integer maxRetry);
    /**
     * 用户信息
     */
    UserInfoDTO getUserInfo(String accessToken);
}
