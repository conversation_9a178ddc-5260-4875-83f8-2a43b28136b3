package com.alibaba.copilot.app.domain.seocopilot.constant;

public enum CollectionTDKCheckItem {

    /**
     * 未设置title tag
     */
    no_title_tag("Title tag should be set.", "Title tags are optimized for improved relevance."),
    /**
     * title不包含关键词
     */
    title_tag_quality("Title tag should include target keywords.", "Title tags are optimized for improved relevance."),
    /**
     * title tag长度50-70
     */
    title_tag_length("Title tag should between 50 and 70 characters.", "Title tags are optimized for improved relevance."),
    /**
     * 未设置description
     */
    no_description_tag("Meta description should be set.", "Meta description has been optimized for the improved effect."),
    /**
     * description不包含关键词
     */
    description_tag_quality("Meta description should include target keywords.", "Meta description has been optimized for the improved effect."),
    /**
     * description长度120-200
     */
    description_tag_length("Meta description length should between 120-200 characters.", "Meta description has been optimized for the improved effect."),
    /**
     * url不包含关键词
     */
    url_need_keywords("URL should include target keywords.", "Collection URL is optimized for an ideal effect. (Frequent changes to the URL are discouraged to ensure timely indexing.)"),
    /**
     * url关键词过多
     */
    url_keywords_count("The URL contains keyword(s) only once.", "Collection URL is optimized for an ideal effect. (Frequent changes to the URL are discouraged to ensure timely indexing.)");

    private String advice;
    private String conclusion;
    CollectionTDKCheckItem(String advice, String conclusion) {
        this.advice = advice;
        this.conclusion = conclusion;
    }

    public String getAdvice() {
        return advice;
    }

    public String getConclusion() {
        return conclusion;
    }
}
