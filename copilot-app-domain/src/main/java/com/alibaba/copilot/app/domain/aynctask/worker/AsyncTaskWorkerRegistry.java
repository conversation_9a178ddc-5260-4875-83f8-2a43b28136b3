package com.alibaba.copilot.app.domain.aynctask.worker;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @desc: worker注册器
 * @author: yixiao.cx
 * @create: 2023-06-26
 **/
@Slf4j
@Component
public class AsyncTaskWorkerRegistry {

    @Autowired(required = false)
    private List<AsyncTaskWorker> asyncTaskWorkers;

    private Map<String, AsyncTaskWorker> asyncTaskWorkerMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        if (CollectionUtils.isEmpty(asyncTaskWorkers)) {
            return;
        }

        asyncTaskWorkers.forEach(this::register);
    }

    public void register(AsyncTaskWorker worker) {
        asyncTaskWorkerMap.put(worker.type(), worker);
    }

    public AsyncTaskWorker getWorker(String type) {
        if (!asyncTaskWorkerMap.containsKey(type)) {
            return null;
        }
        return asyncTaskWorkerMap.get(type);
    }
}
