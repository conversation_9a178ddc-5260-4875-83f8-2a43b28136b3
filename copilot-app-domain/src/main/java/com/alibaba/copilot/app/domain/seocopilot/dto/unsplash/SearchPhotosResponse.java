package com.alibaba.copilot.app.domain.seocopilot.dto.unsplash;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class SearchPhotosResponse {
    private int total;

    @J<PERSON>NField(name = "total_pages")
    private int totalPages;

    private List<Photo> results;

    @Data
    public static class Photo {
        private String id;

        @J<PERSON><PERSON>ield(name = "created_at")
        private String createdAt;
        private int width;
        private int height;
        private String color;

        @J<PERSON><PERSON>ield(name = "blur_hash")
        private String blurHash;
        private int likes;

        @J<PERSON><PERSON><PERSON>(name = "liked_by_user")
        private boolean likedByUser;
        private String description;
        private User user;

        @J<PERSON><PERSON>ield(name = "current_user_collections")
        private List<?> currentUserCollections;
        private Urls urls;
        private Links links;
    }

    @Data
    public static class User {
        private String id;
        private String username;
        private String name;

        @J<PERSON><PERSON>ield(name = "first_name")
        private String firstName;

        @<PERSON><PERSON><PERSON><PERSON>(name = "last_name")
        private String lastName;

        @J<PERSON><PERSON><PERSON>(name = "instagram_username")
        private String instagramUsername;

        @<PERSON><PERSON><PERSON><PERSON>(name = "twitter_username")
        private String twitterUsername;

        @<PERSON><PERSON><PERSON><PERSON>(name = "portfolio_url")
        private String portfolioUrl;

        @JSONField(name = "profile_image")
        private ProfileImage profileImage;
        private UserLinks links;
    }

    @Data
    public static class ProfileImage {
        private String small;
        private String medium;
        private String large;
    }

    @Data
    public static class UserLinks {
        private String self;
        private String html;
        private String photos;
        private String likes;
    }

    @Data
    public static class Urls {
        private String raw;
        private String full;
        private String regular;
        private String small;
        private String thumb;
    }

    @Data
    public static class Links {
        private String self;
        private String html;
        private String download;

        @JSONField(name = "download_location")
        private String downloadLocation;
    }
}
