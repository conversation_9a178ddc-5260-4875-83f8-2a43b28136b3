package com.alibaba.copilot.app.domain.seocopilot.request;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class OneshotData {
    private Long id;
    private String uid;
    private String config;
    private Long end;
    private String message;
    private String pid;
    private String result;
    private Integer retry;
    private String stack;
    private Long start;
    private String status;
    private String token;
    private String user_id;
    private String text;
    private Long during;
    private String callback;
    private String path;
    private String uuid;
    private String syncType;
    private String format;
    private Long duration;
}
