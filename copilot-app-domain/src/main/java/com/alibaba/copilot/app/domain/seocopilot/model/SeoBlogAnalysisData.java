package com.alibaba.copilot.app.domain.seocopilot.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class SeoBlogAnalysisData {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * seo店铺ID
     */
    private Long shopId;

    /**
     * article id
     */
    private Long articleId;

    /**
     * country
     */
    private String country;

    /**
     * style
     */
    private String style;

    /**
     * blogCategory
     */
    private String blogCategory;

    /**
     * link
     */
    private String link;

    /**
     * 关键词
     * 格式：a,b,c,d
     */
    private String keywords;

    /**
     * title
     */
    private String title;

    /**
     * outline
     */
    private String outline;

    /**
     * content
     */
    private String content;

    /**
     * handle
     */
    private String handle;

    /**
     * 是否apply到shopify
     */
    private Boolean isApply;

    /**
     * multiturn_idempotent_token
     */
    private String multiturnIdempotentToken;

    /**
     * page url
     */
    private String pageUrl;

    /**
     * clicks
     */
    private BigDecimal clicks;

    /**
     * impressions
     */
    private BigDecimal impressions;

    /**
     * ctr
     */
    private BigDecimal ctr;

    /**
     * position
     */
    private BigDecimal position;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 扩展字段
     */
    private SeoBlogAnalysisDataAttributes attributes = new SeoBlogAnalysisDataAttributes("{}");

}
