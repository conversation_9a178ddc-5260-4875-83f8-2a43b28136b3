package com.alibaba.copilot.app.domain.seocopilot.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * SeoSysLogDetail 实体
 */
@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class SeoSysLogDetail {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * seoShopId
     */
    private Long shopId;

    /**
     * seoSysLogId
     */
    private Long sysLogId;

    /**
     * 诊断资源类型
     */
    private String entityType;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 状态
     */
    private String checkStatus;

    /**
     * 诊断类型
     */
    private String checkType;

    /**
     * 内容
     */
    private String content;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 扩展字段
     */
    private SeoSysLogDetailAttributes attributes = new SeoSysLogDetailAttributes("{}");

    /**
     * 任务id
     */
    private Long taskId;
}
