package com.alibaba.copilot.app.domain.blog_creator.service;

import java.util.List;

import com.alibaba.copilot.app.client.blog_creator.BlogCreatorWorkflowRequest;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain=true)
public class BlogContext {
    private Long sessionId;
    private BlogCreatorWorkflowRequest request;
    private List<String> titles;
    private List<String> outlines;
    private String content;
    private String previewUrl;
    private String referenceContent;
    private List<String> referenceOutlines;

    public BlogContext(Long sessionId, BlogCreatorWorkflowRequest request) {
        this.sessionId = sessionId;
        this.request = request;
    }
}
