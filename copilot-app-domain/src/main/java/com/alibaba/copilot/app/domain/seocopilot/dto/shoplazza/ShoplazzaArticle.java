package com.alibaba.copilot.app.domain.seocopilot.dto.shoplazza;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ShoplazzaArticle {

    private String id;

    private String title;
    private String excerpt;
    private String content;
    private Boolean published;

    @J<PERSON>NField(name = "published_at")
    private Date publishedAt;

    private String handle;

    private String author;

    @JSONField(name = "seo_title")
    private String seoTitle;

    @JSONField(name = "seo_description")
    private String seoDescription;

    @JSONField(name = "seo_keywords")
    private List<String> seoKeywords;

    @J<PERSON><PERSON>ield(name = "blog_ids")
    private List<String> blogIds;

    @J<PERSON><PERSON>ield(name = "created_at")
    private Date createdAt;

    @JSONField(name = "update_at")
    private Date updateAt;

    private Image image;

    @Data
    public static class Image {
        private String src;

        private String alt;
    }

}
