package com.alibaba.copilot.app.domain.seocopilot.dto;

import lombok.Data;

import java.util.List;

@Data
public class ShopifyGraphQLBaseResult {

    public Extensions extensions;

    public List<Error> errors;


    @lombok.Data
    public static class Error{
        private String message;
        private List<Location> locations;
    }

    @lombok.Data
    public static class Location {
        private Integer line;
        private Integer column;
    }

    @lombok.Data
    public static class Extensions {
        public Cost cost;
    }

    @lombok.Data
    public static class Cost {
        /**
         * 系统估算的查询成本
         */
        public Integer requestedQueryCost;

        /**
         * 实际消耗的查询成本
         */
        public Integer actualQueryCost;

        /**
         * 速率限制
         */
        public ThrottleStatus throttleStatus;
    }

    @lombok.Data
    public static class ThrottleStatus {

        /**
         * API允许的最大累积积分
         */
        public Integer maximumAvailable;

        /**
         * API当前可用积分
         */
        public Integer currentlyAvailable;

        /**
         * 恢复速率
         */
        public Integer restoreRate;
    }

}
