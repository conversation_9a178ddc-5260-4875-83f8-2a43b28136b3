package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class LightHouseResultDTO {

    private Audits audits;

    private Categories categories;

    private FullPageScreenshot fullPageScreenshot;

    @Data
    public static class Audits {

        private Element viewport;
        @JSONField(name = "document-title")
        private Element documentTitle;
        @JSONField(name = "image-alt")
        private Element imageAlt;
        @JSONField(name = "meta-description")
        private Element metaDescription;
        @JSONField(name = "http-status-code")
        private Element httpStatusCode;
        @JSONField(name = "font-size")
        private Element fontSize;
        @JSONField(name = "link-text")
        private Element linkText;
        @JSONField(name = "crawlable-anchors")
        private Element crawlableAnchors;
        @JSONField(name = "is-crawlable")
        private Element isCrawlable;
        @JSONField(name = "robots-txt")
        private Element robotsTxt;
        @JSONField(name = "tap-targets")
        private Element tapTargets;
        @JSONField(name = "hreflang")
        private Element hreflang;
        @JSONField(name = "plugins")
        private Element plugins;
        @JSONField(name = "canonical")
        private Element canonical;
        @JSONField(name = "structured-data")
        private Element structuredData;

    }

    @Data
    public static class Categories {
        private Element seo;
    }

    @Data
    public static class Element {
        private String id;
        private String title;
        private String description;
        private Float score;
        private String scoreDisplayMode;
        private Details details;
    }


    @Data
    public static class Details {

        /**
         * 针对不同问题项，该字段差异性比较大，保持Json格式
         */
        private String items;
    }

    @Data
    public static class FullPageScreenshot {
        private Screenshot screenshot;
    }

    @Data
    public static class Screenshot {
        private String data;
        private Integer width;
        private Integer height;
    }
}
