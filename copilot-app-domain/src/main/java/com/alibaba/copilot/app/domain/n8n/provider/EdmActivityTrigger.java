package com.alibaba.copilot.app.domain.n8n.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.app.client.flow.FlowNodeExecuteSPI;
import com.alibaba.copilot.app.client.flow.model.FlowNodeRequest;
import com.alibaba.copilot.app.client.flow.model.FlowNodeResponse;
import com.alibaba.copilot.app.domain.base.email.EdmGateway;
import com.alibaba.copilot.app.domain.base.model.EdmRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.fastjson.JSONObject;

import javax.annotation.Resource;
import java.util.HashMap;

@HSFProvider(serviceName = "edmActivityTriggerFlowNodeExecuteSPI", serviceInterface = FlowNodeExecuteSPI.class, serviceVersion = "1.0.0.SEO_COPILOT.edmActivityTrigger")
public class EdmActivityTrigger implements FlowNodeExecuteSPI {
    @Resource
    private EdmGateway edmGateway;

    @Override
    public SingleResult<FlowNodeResponse> execute(FlowNodeRequest request) {
        EdmRequest edmRequest = new EdmRequest()
                .setId(request.getData().getLong("edmId"))
                .setToEmail(request.getData().getString("email"))
                .setParams(new HashMap<>());
        edmGateway.sendEmail(edmRequest);
        FlowNodeResponse response = new FlowNodeResponse();
        response.setAppCode(request.getAppCode());
        response.setActionCode(request.getActionCode());
        JSONObject data = new JSONObject();
        data.put("isEdmTriggered", "true");
        response.setData(data);
        return SingleResult.buildSuccess(response);
    }
}
