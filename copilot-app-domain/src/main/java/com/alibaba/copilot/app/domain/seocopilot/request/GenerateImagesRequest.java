package com.alibaba.copilot.app.domain.seocopilot.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class GenerateImagesRequest {

    private String taskId;

    private Request params;


    @Data
    public static class Request {
        private Integer height;

        private Integer width;

        private Integer imageNum;

        private String prompt;

        /**
         * [
         * "flat_illustration",                # 平面插画, 之前的模型
         * "indoor_home",                      # 家居风格, 新增
         * "ecommerce_booth",                  # 电商展台, 新增
         * "product_photography",              # 产品摄影, 新增
         * "outdoor_product_photography",      # 室外产品摄影, 新增
         * "atmosphere_photography",           # 氛围感摄影, 新增
         * "fruit_photography",                # 水果摄影, 新增
         * "cosmetics_photography",            # 美妆摄影, 新增
         * "ecommerce_product_photography",    # 电商产品摄影, 新增
         * ]
         */
        private String model;
    }
}
