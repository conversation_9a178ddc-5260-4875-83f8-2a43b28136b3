
package com.alibaba.copilot.app.domain.aiIistmaster.config;



import com.alibaba.copilot.app.domain.aiIistmaster.diamond.annotation.DiamondKey;
import com.alibaba.copilot.app.domain.aiIistmaster.diamond.annotation.DiamondValue;

import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * <AUTHOR>
 * @date 2023/8/10 2:37 PM
 */
@Data
@Component
@EnableTransactionManagement
public class BaseDataConfig {

    @DiamondValue("project.name")
    String projectName;

    @DiamondKey("com.copilot.app.commodityTitle.template")
    String commodityTitleTemplate;


    @DiamondKey("com.copilot.app.commodityShortSellPoint.template")
    String shortSellPointTemplate;


    @DiamondKey("com.copilot.app.commodityDescription.template")
    String descriptionTemplate;
}
