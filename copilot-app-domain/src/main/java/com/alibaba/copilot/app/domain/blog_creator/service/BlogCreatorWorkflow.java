package com.alibaba.copilot.app.domain.blog_creator.service;

import com.alibaba.copilot.app.client.blog_creator.BlogCreatorReference;
import com.alibaba.copilot.app.client.blog_creator.BlogCreatorWorkflowRequest;
import com.alibaba.copilot.app.client.blog_creator.BlogCreatorWorkflowStatus;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain=true)
public class BlogCreatorWorkflow {
    private Long id;
    private BlogCreatorWorkflowRequest request;
    private BlogCreatorWorkflowStatus status;
    /**
     * 参考资料 json
     */
    private BlogCreatorReference reference;

    /**
     * 博客标题
     */
    private String title;

    /**
     * 博客大纲
     */
    private String outline;

    /**
     * 博客正文
     */
    private String content;

    private BlogCreatorWorkflowAttributes attributes;
}
