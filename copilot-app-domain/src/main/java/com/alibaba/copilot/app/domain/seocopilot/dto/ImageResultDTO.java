package com.alibaba.copilot.app.domain.seocopilot.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ImageResultDTO {
    /**
     * image id
     */
    private Long imageId;

    private String graphqlId;
    /**
     * 优化前的image url
     */
    private String originalUrl;
    /**
     * 优化后的image url
     */
    private String optimizeUrl;
    /**
     * 优化后的image 格式
     */
    private String optimizeFormat;

    /**
     * 优化前 image alt
     */
    private String originalAlt;

    /**
     * 优化后的image alt
     */
    private String optimizeAlt;
    /**
     * 优化后的image size
     */
    private Integer optimizeSize;

    private Long position;
}