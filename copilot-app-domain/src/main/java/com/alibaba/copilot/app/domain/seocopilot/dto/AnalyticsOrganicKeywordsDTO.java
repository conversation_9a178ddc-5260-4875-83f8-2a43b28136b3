package com.alibaba.copilot.app.domain.seocopilot.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
public class AnalyticsOrganicKeywordsDTO {
    /**
     * 关键词
     */
    private String keyword;
    /**
     * 点击量
     */
    private String clicks;
    /**
     * 点击量
     */
    private Long realClicks;
    /**
     * 曝光量
     */
    private String impressions;
    /**
     * 曝光量
     */
    private Long realImpressions;
    /**
     * ctr
     */
    private BigDecimal ctr;
    /**
     * 排名
     */
    private String position;
    /**
     * 排名
     */
    private BigDecimal realPosition;
    /**
     * 关键词难度 SEMrush
     */
    private BigDecimal kd;
    /**
     * 搜索量 SEMrush
     */
    private String volume;
    /**
     * 搜索量
     */
    private Long realVolume;
    /**
     * 搜索意图 SEMrush
     */
    private String intent;
    /**
     * Traffic SEMrush
     */
    private String traffic;
    /**
     * Traffic
     */
    private BigDecimal realTraffic;
}
