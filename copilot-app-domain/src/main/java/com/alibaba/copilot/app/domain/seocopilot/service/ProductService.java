package com.alibaba.copilot.app.domain.seocopilot.service;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.client.seocopilot.dto.SeoProductHistoryDetailDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.SeoProductDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.SeoProductPageDTO;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoOptimizeProposal;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShop;
import com.alibaba.copilot.app.domain.seocopilot.request.ProductOptimizeRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.ProductPageRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

import java.util.List;

public interface ProductService {
    /**
     * shopify product list
     */
    PageWrapper<SeoProductPageDTO> getProductList(Long shopifyShopId, ProductPageRequest productPageRequest);

    /**
     * shopify product detail
     */
    SingleResult<SeoProductDTO> getProductDetail(Long entityId, Long shopifyShopId, Long shopifyProductId);

    /**
     * shopify product 优化
     */
    SingleResult<Boolean> optimize(Long shopifyShopId, Long shopifyProductId, List<String> keywords, String scene);

    /**
     * 保存proposal
     */
    SingleResult<Boolean> saveProposal(Long shopifyShopId, ProductOptimizeRequest request);

    /**
     * 应用到shopify
     */
    SingleResult<Boolean> applyToShopify(Long shopifyShopId, ProductOptimizeRequest request);

    SingleResult<Boolean> applyProductProposal(Long shopifyShopId, List<Long> seoProductIds, List<Long> productCheckRecordIds);

    /**
     * 应用到shopify
     */
    SingleResult<Boolean> applyToShopify(Long shopifyShopId, Long shopifyProductId, List<SeoOptimizeProposal> seoOptimizeProposals);

    SingleResult<Boolean> checkProductUsage(SeoShop seoShop);

    SeoProductHistoryDetailDTO getProductHistoryDetail(Long shopifyShopId, Long productId);

    Boolean rollbackProduct(Long shopifyShopId, Long productId, Long recordId);
}
