package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.copilot.app.domain.seocopilot.constant.KeywordDifficultyRange;
import lombok.Data;

import java.util.List;

@Data
public class ShopKeywordQuery {

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * intent
     */
    private String intent;

    private String type;

    /**
     * keyword difficulty
     */
    private KeywordDifficultyRange keywordDifficultyRange;

    /**
     * limit
     */
    private Integer limit;

    /**
     * language
     */
    private String language;

    /**
     * 分页参数-page num
     */
    private Integer pageNum;

    /**
     * 分页参数-page size
     */
    private Integer pageSize;

    /**
     * preferences
     */
    private List<String> preferences;

    /**
     * not in preferences
     */
    private List<String> notInPreferences;

    /**
     * keyword id
     */
    private Long keywordId;
}
