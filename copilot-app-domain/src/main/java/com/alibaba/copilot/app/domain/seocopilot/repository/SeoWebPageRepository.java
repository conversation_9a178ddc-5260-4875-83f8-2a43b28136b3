package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoWebPage;

import java.util.List;

/**
 * SeoShopRepository
 */
public interface SeoWebPageRepository {

    /**
     * 根据ShopID查询店铺下的Page信息
     *
     * @return
     */
    List<SeoWebPage> getPagesByShopId(Long shopId);

    /**
     * 根据ShopID查询店铺下的Page信息
     *
     * @return
     */
    List<SeoWebPage> getPagesByShopIdAndType(Long shopId, String type);

    List<SeoWebPage> getPagesByShopIdAndTypes(Long shopId, List<String> types);

    PageWrapper<SeoWebPage> getPagesByShopId(Long shopId, Integer pageNum, Integer pageSize, Boolean isIndexed);

    List<SeoWebPage> getPagesByPageUrl(Long shopId, String pageUrl, String type);

    /**
     * 根据ShopID查询店铺下的Page信息
     *
     * @return
     */
    int save(SeoWebPage seoWebPage);

    Integer deleteByShopId(Long shopId);

    Integer deleteById(Long id);

    /**
     * 根据主键ID查询数据
     *
     * @return
     */
    SeoWebPage getPageById(Long id);
}
