package com.alibaba.copilot.app.domain.seocopilot.request.copyleaks;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Metadata {
    private String finalUrl;
    private String canonicalUrl;
    private String author;
    private String organization;
    private String filename;
    private String publishDate;
    private String creationDate;
    private String lastModificationDate;
    private List<CustomMetaItem> customMetadata;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class CustomMetaItem implements Serializable {
        private String key;
        private Object value;
    }
}
