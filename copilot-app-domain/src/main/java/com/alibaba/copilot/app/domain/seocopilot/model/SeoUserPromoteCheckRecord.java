package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.app.domain.seocopilot.constant.SeoUserPromoteCheckRecordStatus;
import lombok.*;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class SeoUserPromoteCheckRecord {
    private Long id;

    private Long outerId;

    private String type;

    private String request;

    private String result;

    private String attributes;

    private SeoUserPromoteCheckRecordStatus status;

    private Boolean deleted;
}
