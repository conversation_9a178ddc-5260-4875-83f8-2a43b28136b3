package com.alibaba.copilot.app.domain.seocopilot.request;

import io.fury.annotation.Public;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/03/22
 * @link https://developers.google.com/analytics/devguides/reporting/data/v1/rest/v1beta/properties/runReport?hl=zh-cn
 */

@Data
@Builder
public class GAQueryRequest {
    private List<Dimension> dimensions;
    private List<Metric> metrics;
    private List<DateRange> dateRanges;
    private FilterExpression dimensionFilter;
    private Integer offset;
    private Integer limit;
    private String currencyCode;
    private boolean keepEmptyRows;
    private boolean returnPropertyQuota;

    @Data
    @Builder
    public static class DateRange {
        private String startDate;
        private String endDate;
    }

    @Data
    @Builder
    public static class Dimension {
        private String name;
    }

    @Data
    @Builder
    public static class Metric {
        private String name;
    }



    @Data
    @Builder
    public static class FilterExpression {
        private Filter filter;
    }

    @Data
    @Builder
    public static class Filter {
        private String fieldName;
        private StringFilter stringFilter;
    }

    @Data
    @Builder
    public static class StringFilter {
        private String matchType;
        private String value;
    }
}
