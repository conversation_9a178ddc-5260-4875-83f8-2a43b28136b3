package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class UserGrowthGenTitleAndOutlineDTO {

    private String title;

    private String outline;

    @JSONField(name = "references_blogs")
    private String referencesBlogs;

    @JSONField(name = "retrieval_info_list")
    private List<String> retrievalInfoList;

    private Map<String, String> titles;

    @JSONField(name = "craw_blog_urls")
    private List<String> crawBlogUrls;
}
