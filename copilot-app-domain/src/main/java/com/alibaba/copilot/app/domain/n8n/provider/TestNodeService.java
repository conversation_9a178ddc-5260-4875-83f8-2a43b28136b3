package com.alibaba.copilot.app.domain.n8n.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.copilot.app.client.flow.FlowNodeExecuteSPI;
import com.alibaba.copilot.app.client.flow.model.FlowNodeRequest;
import com.alibaba.copilot.app.client.flow.model.FlowNodeResponse;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.fastjson.JSONObject;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/05/20
 */
@HSFProvider(serviceInterface = FlowNodeExecuteSPI.class, serviceVersion = "1.0.0.seo.testAction")
public class TestNodeService implements FlowNodeExecuteSPI {

    @Override
    public SingleResult<FlowNodeResponse> execute(FlowNodeRequest request) {
        FlowNodeResponse response = new FlowNodeResponse();
        response.setAppCode(request.getAppCode());
        response.setActionCode(request.getActionCode());

        JSONObject data = new JSONObject();
        data.put("testUserId", "123456");

        response.setData(data);
        return SingleResult.buildSuccess(response);
    }
}