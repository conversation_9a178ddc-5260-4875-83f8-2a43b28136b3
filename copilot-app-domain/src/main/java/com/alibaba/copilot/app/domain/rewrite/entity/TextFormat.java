package com.alibaba.copilot.app.domain.rewrite.entity;

import lombok.Data;
import lombok.Builder;

@Data
@Builder
public class TextFormat {
    // 基本格式
    private boolean bold;
    private boolean italic;
    private boolean underline;
    private String fontFamily;
    private int fontSize;
    
    // 对齐方式
    private TextAlignment alignment;
    
    // 缩进
    private int indentationLeft;  // 左缩进(单位: 磅)
    private int indentationRight; // 右缩进
    private int firstLineIndent;  // 首行缩进
    
    // 行距
    private float lineSpacing;
    
    // 颜色
    private String textColor;
    private String backgroundColor;
} 