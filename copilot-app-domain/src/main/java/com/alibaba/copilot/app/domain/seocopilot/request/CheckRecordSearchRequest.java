package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.copilot.app.domain.seocopilot.constant.BlogCheckType;
import com.alibaba.copilot.app.domain.seocopilot.constant.CheckEntityType;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
@Builder
public class CheckRecordSearchRequest {

    private Long shopId;

    private String batchId;

    private Long taskId;

    private CheckEntityType entityType;

    @Builder.Default
    private List<String> checkTypes = new ArrayList<>();

    @Builder.Default
    private List<Long> entityIds = new ArrayList<>();

    @Builder.Default
    private List<String> status = new ArrayList<>();

    private List<String> notInStatus = new ArrayList<>();

    private Date taskCreateTime;

    public void addCheckType(String checkType) {
        checkTypes.add(checkType);
    }

    public void addCheckType(BlogCheckType blogCheckType) {
        checkTypes.add(blogCheckType.name());
    }
}
