package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.copilot.app.domain.seocopilot.dto.KeywordRerankDataDTO;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * KeywordRerankRequest
 *
 * <AUTHOR>
 * @date 2024/6/20 4:30 下午
 */
@Data
public class KeywordRerankRequest {

    /**
     * apiName
     */
    private String apiName = "seo_keyword_rerank";

    /**
     * srcBizName
     */
    private String srcBizName = "";

    /**
     * scene
     */
    private String scene = "";

    /**
     * data
     */
    private RequestData data;

    @Data
    public static class RequestData {

        /**
         * keyword
         */
        @JSONField(name = "keywords_list")
        private List<KeywordRerankDataDTO> keywordsList;
    }
}
