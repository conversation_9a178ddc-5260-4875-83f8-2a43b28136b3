package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.app.client.seocopilot.constant.ImageModuleEnum;
import com.alibaba.copilot.app.client.seocopilot.constant.ImageStyleEnum;
import com.alibaba.copilot.app.client.seocopilot.constant.ImageTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImageStyle {

    private ImageTypeEnum type;

    private ImageStyleEnum style;

    private ImageModuleEnum module;


    public static ImageStyle defaultImageStyle() {
        return ImageStyle.builder().type(ImageTypeEnum.AI_GENERATE).style(ImageStyleEnum.flat_illustration).module(ImageModuleEnum.BLOG_GEN).build();
    }

}
