package com.alibaba.copilot.app.domain.seocopilot.dto.shoplazza;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class ShoplazzaBlog {

    private String id;

    private String title;

    private String handle;

    @J<PERSON>NField(name = "created_at")
    private Date createdAt;

    @JSONField(name = "updated_at")
    private Date updatedAt;

}
