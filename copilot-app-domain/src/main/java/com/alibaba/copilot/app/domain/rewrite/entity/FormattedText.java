package com.alibaba.copilot.app.domain.rewrite.entity;

import lombok.Data;
import lombok.Builder;
import java.util.List;

@Data
@Builder
public class FormattedText {
    // 文本类型
    private TextType type;
    
    // 文本内容
    private String content;
    
    // 格式属性
    private TextFormat format;
    
    // 嵌套的子内容
    private List<FormattedText> children;
    
    // 层级
    private int level;
    
    // 序号(用于编号列表、章节编号等)
    private String number;
    
    // 元数据(如引用信息、注释等)
    private TextMetadata metadata;
    
    // 在文档中的顺序(从0开始)
    private int order;
    
    // 父节点的ID（用于构建层级关系）
    private String parentId;
    
    // 当前节点的唯一ID
    private String id;
    
    // 在父节点中的位置（用于同级内容的排序）
    private int indexInParent;
    
    // 文档块的开始位置（用于定位）
    private int startPosition;
    
    // 文档块的结束位置（用于定位）
    private int endPosition;
    
    // 是否是容器类型（如表格、列表等可以包含其他元素的类型）
    private boolean isContainer;
} 