package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.copilot.app.domain.seocopilot.constant.SeoEntityStatus;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class SeoProductQueryRequest {

    private Long shopId;

    private String batchId;

    private Long taskId;

    private String updateDateStart;

    private String updateDateEnd;

    private String collection;

    private List<SeoEntityStatus> status;

    private Long pageNum;

    private Long pageSize;

    private String title;

    private String issueType;
}
