package com.alibaba.copilot.app.domain.seocopilot.factory;

import com.alibaba.copilot.app.domain.seocopilot.constant.CollectionType;
import com.alibaba.copilot.app.domain.seocopilot.constant.SeoWebPageType;
import com.alibaba.copilot.app.domain.seocopilot.model.*;
import com.alibaba.copilot.app.domain.seocopilot.utils.SeoEntityStatusUtil;
import com.alibaba.copilot.boot.basic.factory.Builder;
import com.alibaba.copilot.boot.shopify.web.data.metafield.ShopifyMetaField;
import com.alibaba.copilot.boot.shopify.web.data.product.ShopifyCollection;
import com.alibaba.fastjson.JSON;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;

import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Slf4j
@Setter
public class SeoCollectionBuilder implements Builder<SeoCollection> {

    public static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssXXX");

    private static final String COLLECTION_TITLE_TAG = "title_tag";

    private static final String COLLECTION_DESCRIPTION_TAG = "description_tag";

    private SeoShop seoShop;

    private List<SeoWebPage> seoWebPages;

    private ShopifyCollection shopifyCollection;

    private String collectionType;

    private List<ShopifyMetaField> collectionMetaField;

    private String indexStatus;

    private SeoCollection seoCollection;

    private Integer checkGapDays;

    @Override
    public SeoCollection build() {
        Date now = new Date();
        SeoCollection seoCollection = new SeoCollection();
        seoCollection.setGmtCreate(now);
        seoCollection.setGmtModified(now);
        seoCollection.setShopId(seoShop.getId());
        seoCollection.setSeoWebPageId(getWebPageId());
        seoCollection.setShopifyCollectionId(shopifyCollection.getId());
        seoCollection.setTitle(shopifyCollection.getTitle());
        seoCollection.setDescription(shopifyCollection.getBodyHtml());
        seoCollection.setImage(getImage());
        seoCollection.setSeoTitle(getSeoTitle());
        seoCollection.setSeoDescription(getSeoDescription());
        seoCollection.setSeoHandle(shopifyCollection.getHandle());
        Date shopifyUpdateAt = getShopifyUpdateAt();
        seoCollection.setStatus(SeoEntityStatusUtil.getStatus(shopifyUpdateAt, indexStatus, checkGapDays, seoCollection.getStatus(), seoCollection.getId()));
        seoCollection.setShopifyUpdateAt(shopifyUpdateAt);
        seoCollection.setIndexStatus(indexStatus);
        seoCollection.setType(CollectionType.valueOf(collectionType));
        seoCollection.setDeleted(false);
        seoCollection.setAttributes(new SeoCollectionAttributes(null));
        return seoCollection;
    }

    private Date getShopifyUpdateAt() {
        ZonedDateTime zdt = ZonedDateTime.parse(shopifyCollection.getUpdatedAt(), formatter);
        return Date.from(zdt.toInstant());
    }

    private String getSeoDescription() {
        ShopifyMetaField shopifyMetaField = collectionMetaField.stream().filter(metafield -> metafield.getKey().equals(COLLECTION_DESCRIPTION_TAG)).findFirst().orElse(null);
        if (shopifyMetaField != null) {
            return shopifyMetaField.getValue();
        }
        if (StringUtils.isBlank(shopifyCollection.getBodyHtml())) {
            return null;
        }
        return getTextFromHtml(shopifyCollection.getBodyHtml());
    }

    private String getTextFromHtml(String html) {
        String descText = null;
        try {
            Document doc = Jsoup.parse(html);
            descText = doc.body().text();
        } catch (Exception e) {
            log.error("SeoCollectionBuilder,getTextFromHtml:提取文本失败：{}", e.getMessage(), e);
        }
        return descText;
    }

    private String getSeoTitle() {
        ShopifyMetaField shopifyMetaField = collectionMetaField.stream().filter(metafield -> metafield.getKey().equals(COLLECTION_TITLE_TAG)).findFirst().orElse(null);
        if (shopifyMetaField != null) {
            return shopifyMetaField.getValue();
        }
        return shopifyCollection.getTitle();
    }

    private List<Image> getImage() {
        log.info("SeoCollectionBuilder getImage shopify collection id:{}", shopifyCollection.getId());
        List<Image> images = new ArrayList<>();
        ShopifyCollection.Image shopifyImage = shopifyCollection.getImage();
        if (shopifyImage != null) {
            Image image = new Image();
            image.setIsMainImage(true);
            image.setShopifyImageId(null);
            image.setUrl(shopifyImage.getSrc());
            image.setAlt(shopifyImage.getAlt());

            images.add(image);
        }
        log.info("SeoCollectionBuilder getImage images:{}", JSON.toJSONString(images));
        return images;
    }

    private Long getWebPageId() {
        log.info("SeoCollectionBuilder getWebPageId shopify collection id:{}", shopifyCollection.getId());
        SeoWebPage seoWebPage = seoWebPages.stream().filter(page -> SeoWebPageType.COLLECTIONPAGE.equals(page.getType())
                && page.getAttributes() != null
                && shopifyCollection.getId().equals(page.getAttributes().getCollectionId())).findFirst().orElse(null);
        if (seoWebPage != null) {
            log.info("SeoCollectionBuilder getWebPageId collection page id:{}", seoWebPage.getId());
            return seoWebPage.getId();
        }
        return null;
    }

}
