package com.alibaba.copilot.app.domain.seocopilot.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SeoBlog {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * shopify店铺ID
     */
    private Long shopId;

    /**
     * 批次id
     */
    private String batchId;

    /**
     * shopify博客ID
     */
    private Long shopifyBlogId;

    /**
     * shopify博客标题
     */
    private String title;

    private SeoBlogAttributes attributes = new SeoBlogAttributes();

    /**
     * 逻辑删除
     */
    private Boolean deleted;
}
