package com.alibaba.copilot.app.domain.aiIistmaster.repository;

import java.util.List;

import com.alibaba.copilot.app.domain.aiIistmaster.model.UserPreferenceTemplate;

/**
 * @ClassName PreferenceTemplateRepository
 * <AUTHOR>
 * @Date 2023/8/17 19:42
 */
public interface UserPreferenceTemplateRepository {

    Boolean save(UserPreferenceTemplate preferenceTemplate);


    List<UserPreferenceTemplate> queryByCriteria(Long userId, String appSource, String templateType);
}
