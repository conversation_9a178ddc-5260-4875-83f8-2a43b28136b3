package com.alibaba.copilot.app.domain.seocopilot.model;

import lombok.*;

import java.util.Date;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ConversationMessage {
    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 对话id
     */
    private Long conversationId;
    /**
     * 对话内容
     */
    private String content;
    /**
     * 角色
     */
    private String role;

    private Long previousId;
    /**
     * 逻辑删除
     */
    private Boolean deleted;
}
