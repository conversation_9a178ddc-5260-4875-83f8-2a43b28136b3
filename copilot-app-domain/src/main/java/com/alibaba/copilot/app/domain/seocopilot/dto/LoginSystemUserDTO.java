package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.copilot.boot.tools.verify.Assertor;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

@Data
public class LoginSystemUserDTO {

    private Email email;

    private OpenId openid;

    private Profile profile;

    private Shopify shopify;

    private List<Shoplazza> shoplazza;


    public Shoplazza filterShopByShopDomain(String shopDomain) {
        Shoplazza shoplazzaShop = shoplazza.stream().filter(shop -> StringUtils.equals(shop.getShopDomain(), shopDomain)).findFirst().orElse(null);
        Assertor.assertNonNull(shoplazzaShop, "shoplazza shop is null");
        return shoplazzaShop;
    }


    @Data
    public static class Email {
        private String email;

        @JSONField(name = "verified_email")
        private String verifiedEmail;
    }

    @Data
    public static class OpenId {
        @J<PERSON>NField(name = "id_token")
        private String idToken;
    }

    @Data
    public static class Profile {
        private String name;

        private String image;

        private String sub;

        private String from;
    }

    @Data
    public static class Shopify {
        @JSONField(name = "access_token")
        private String accessToken;

        private String shopDomain;
    }

    @Data
    public static class Shoplazza {
        @JSONField(name = "access_token")
        private String accessToken;

        private String shopDomain;
    }
}
