package com.alibaba.copilot.app.domain.seocopilot.utils;


import org.apache.commons.lang3.time.DateUtils;

import java.util.Calendar;
import java.util.Date;

/**
 * Date相关工具
 */
public class DateUtil {

    /**
     * 判断time是否为days天之内
     *
     * @param time 要判断的时间
     * @return true: days天之内
     */
    public static boolean isWithinNDays(Date time, Integer days) {
        if (days == null) {
            throw new IllegalArgumentException("days illegal");
        }

        int compareRes = DateUtils.truncatedCompareTo(new Date(), DateUtils.addDays(time, days), Calendar.MILLISECOND);

        return compareRes < 0;
    }
}
