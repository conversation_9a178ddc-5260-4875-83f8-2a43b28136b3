package com.alibaba.copilot.app.domain.blog_creator.service;

import com.alibaba.copilot.boot.basic.data.Attributes;

public class BlogCreatorWorkflowAttributes  extends Attributes {
    private static final String PREVIEW_URL = "previewUrl";
    private static final String ERROR_MESSAGE = "errorMessage";

    public BlogCreatorWorkflowAttributes(String json) {super(json);}

    public String getPreviewUrl() {
        return getAsString(PREVIEW_URL);
    }

    public void setPreviewUrl(String previewUrl) {
        put(PREVIEW_URL, previewUrl);
    }

    public String getErrorMessage() {
        return getAsString(ERROR_MESSAGE);
    }

    public void setErrorMessage(String errorMessage) {
        put(ERROR_MESSAGE, errorMessage);
    }
}
