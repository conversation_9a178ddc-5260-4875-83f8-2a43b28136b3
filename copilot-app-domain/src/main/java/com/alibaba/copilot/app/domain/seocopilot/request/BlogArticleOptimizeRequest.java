package com.alibaba.copilot.app.domain.seocopilot.request;

import lombok.Data;

@Data
public class BlogArticleOptimizeRequest {
    /**
     * article id
     */
    private Long articleId;
    /**
     * 应用最新的proposal
     */
    private Boolean applyNewest = false;
    /**
     * title
     */
    private TitleData title;
    /**
     * 内容
     */
    private ContentData content;
    /**
     * TDK
     */
    private SearchAppearanceData searchAppearance;

    public interface RequestData {
        Long getId();
    }

    @Data
    public static class TitleData implements RequestData {
        /**
         * proposal id
         */
        private Long id;
        /**
         * 优化数据
         */
        private OptimizeData optimize;
    }

    @Data
    public static class ContentData implements RequestData {
        /**
         * proposal id
         */
        private Long id;
        /**
         * 优化数据
         */
        private OptimizeData optimize;
    }

    @Data
    public static class SearchAppearanceData implements RequestData {
        /**
         * proposal id
         */
        private Long id;
        /**
         * 优化数据
         */
        private OptimizeData optimize;
    }

    @Data
    public static class OptimizeData {
        /**
         * 标题
         */
        private String title;
        /**
         * 内容
         */
        private String content;
        /**
         * 标题
         */
        private String titleTag;
        /**
         * 详情
         */
        private String metaDescription;
        /**
         * url
         */
        private String url;
    }
}
