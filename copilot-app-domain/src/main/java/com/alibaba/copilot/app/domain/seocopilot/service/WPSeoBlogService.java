package com.alibaba.copilot.app.domain.seocopilot.service;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.client.seocopilot.dto.SeoBlogArticleDTO;
import com.alibaba.copilot.app.client.seocopilot.request.SeoBlogSearchRequest;
import com.alibaba.copilot.app.client.seocopilot.response.ShopifyArticleDetailResponseV2;
import com.alibaba.copilot.app.client.seocopilot.response.ShopifyBlogResponse;
import com.alibaba.copilot.app.domain.seocopilot.dto.BlogTopicDTO;
import com.alibaba.copilot.app.domain.seocopilot.request.BlogArticleOptimizeRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

import java.util.List;

public interface WPSeoBlogService {

    /*
     * 获取blog列表
     */
    List<ShopifyBlogResponse> getBlogs(Long shopId);

    /*
     * 获取topic列表
     */
    List<BlogTopicDTO> getTopics(Long shopId);

    /*
     * 获取topic列表
     */
    List<String> getKeywordsByTopic(Long shopId, Long topicId);

    /**
     * 复合查询博客列表
     */
    PageWrapper<SeoBlogArticleDTO> queryArticles(Long shopId, SeoBlogSearchRequest request);

    /**
     * 查询博客详情
     */
    ShopifyArticleDetailResponseV2 queryArticleDetail(Long shopId, Long articleId);

    /**
     * 保存proposal
     */
    SingleResult<Boolean> saveProposal(Long shopId, BlogArticleOptimizeRequest request);

    /**
     * 标记已发布
     */
    SingleResult<Boolean> applyAsPosted(Long shopId, BlogArticleOptimizeRequest request);

    Boolean deleteBlogByArticleId(Long shopId, Long articleId,String reason);
}
