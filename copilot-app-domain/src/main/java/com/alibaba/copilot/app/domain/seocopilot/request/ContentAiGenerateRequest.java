package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.copilot.app.domain.seocopilot.constant.SeoBizNameEnum;
import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @des 内容生成算法生成入参
 */
@Data
@Builder
public class ContentAiGenerateRequest {

    /**
     * 传入 ApiNameEnum 的列表
     */
    private List<ApiNameEnum> apiNameEnums;
    /**
     * 场景 collection/product/blog/homepage
     */
    private SeoBizNameEnum seoBizNameEnum;

    /**
     * 算法业务参数
     */
    private ContentGenerateParam contentGenerateParam;

    /**
     * 算法业务参数
     */
    @Data
    public static class ContentGenerateParam {

        /**
         * 原始标题
         */
        private String item_title;
        /**
         * 关键词
         * 格式：a,b,c,d
         */
        private String keywords;

        /**
         * pv属性，规格等，没有就空
         * 格式：   a:b;c:d
         */
        private String item_spec;
        /**
         * 原始描述
         */
        private String item_detail;
        /**
         * sourceLanguage
         * 默认传： en
         */
        private String target_language;
        /**
         * Category类目  分等级，有多少写多少
         * cate1,cate2,cate3
         */
        private List<String> pcate_list;

        // 默认无参数构造器
        public ContentGenerateParam() {
        }

        // 拷贝构造器
        public ContentGenerateParam(ContentGenerateParam other) {
            this.item_title = other.item_title;
            this.keywords = other.keywords;
            this.item_spec = other.item_spec;
            this.item_detail = other.item_detail;
            this.target_language = other.target_language;
            // 注意：对于List，我们需要创建一个新的List来避免修改原始对象的pcateList
            this.pcate_list = (other.pcate_list != null) ? new ArrayList<>(other.pcate_list) : null;
        }

    }

    public enum ApiNameEnum {

        SEO_KEYWORD_GENERATE("seo_keyword_generate"),

        SEO_TITLE_GENERATE_LONG("seo_long_title_generate"),

        SEO_TITLE_GENERATE_SHORT("seo_title_generate"),

        SEO_DESCRIPTION_GENERATE_LONG("seo_long_description_generate"),

        SEO_DESCRIPTION_GENERATE_SHORT("seo_short_description_generate"),

        SEO_PIC_ALT("seo_pic_alt");

        private String apiName;

        ApiNameEnum(String apiName) {
            this.apiName = apiName;
        }

        public String getApiName() {
            return apiName;
        }
    }

}
