package com.alibaba.copilot.app.domain.rewrite.repository;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.domain.rewrite.entity.TextGenerateRecord;

import java.util.List;

/**
 * 文本生成记录仓库接口
 */
public interface TextGenerateRecordRepository {

    /**
     * 保存文本生成记录
     *
     * @param textGenerateRecord 文本生成记录
     * @return 是否保存成功
     */
    boolean save(TextGenerateRecord textGenerateRecord);

    /**
     * 根据ID查询文本生成记录
     *
     * @param id 记录ID
     * @return 文本生成记录
     */
    TextGenerateRecord getById(Long id);

    /**
     * 根据用户ID和类型代码分页查询文本生成记录
     *
     * @param userId 用户ID
     * @param codes 类型代码列表（可选）
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    PageWrapper<TextGenerateRecord> getByUserIdAndCodes(String userId, List<String> codes, Integer pageNum, Integer pageSize);
} 