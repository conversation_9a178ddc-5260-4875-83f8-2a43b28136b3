package com.alibaba.copilot.app.domain.aiIistmaster.diamond.spi;

import java.util.concurrent.Executor;

import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListener;

/**
 * <AUTHOR>
 * @date 2023/8/10 11:28 AM
 */
public class DiamondSpiImpl implements DiamondSpi {
    public DiamondSpiImpl() {
    }

    @Override
    public String getConfig(String dataId, String group, long timeoutMs) {
        try {
            return Diamond.getConfig(dataId, group, timeoutMs);
        } catch (Exception var6) {
            return null;
        }
    }

    @Override
    public boolean setConfig(String dataId, String group, String content) {
        return Diamond.publishSingle(dataId, group, content);
    }

    @Override
    public boolean setConfigCas(String dataId, String group, String old, String update) {
        return Diamond.publishSingleCas(dataId, group, old, update);
    }

    @Override
    public void addListener(String dataId, String group, final DiamondListener listener) {
        Diamond.addListener(dataId, group, new ManagerListener() {
            @Override
            public Executor getExecutor() {
                return null;
            }

            @Override
            public void receiveConfigInfo(String s) {
                if (listener != null) {
                    listener.receiveConfigInfo(s);
                }

            }
        });
    }
}
