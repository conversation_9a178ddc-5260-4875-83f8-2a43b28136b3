package com.alibaba.copilot.app.domain.seocopilot.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SeoProductDTO {
    /**
     * Shopify product ID
     */
    private Long shopifyProductId;

    /**
     * 商品主图
     */
    private String mainPicUrl;

    /**
     * shopify admin url
     */
    private String shopifyAdminUrl;

    /**
     * 状态
     */
    private String status;

    /**
     * keywords列表
     */
    private List<String> keywords;

    /**
     * 是否显示keywords
     */
    private boolean showKeywords;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 标题
     */
    private Title title;

    /**
     * 描述
     */
    private Description description;

    /**
     * searchAppearance
     */
    private SearchAppearance searchAppearance;

    /**
     * 图片列表
     */
    private Images images;

    /**
     * 上次优化时间
     */
    private Long lastOptimizedDate;

    /**
     * 总结列表
     */
    private List<String> summaryList;

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Title {
        /**
         * proposal id
         */
        private Long id;

        /**
         * 原始标题
         */
        private TitleDetail original;

        /**
         * 优化标题
         */
        private TitleDetail optimize;

        /**
         * 优化建议
         */
        private List<String> advices;

        /**
         * 剩余时间
         */
        private String leftTime;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class TitleDetail {
        /**
         * 标题
         */
        private String title;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Description {
        /**
         * proposal id
         */
        private Long id;

        /**
         * 原始描述
         */
        private DescriptionDetail original;

        /**
         * 优化描述
         */
        private DescriptionDetail optimize;

        /**
         * 优化建议
         */
        private List<String> advices;

        /**
         * 剩余时间
         */
        private String leftTime;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DescriptionDetail {
        /**
         * 描述
         */
        private String description;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SearchAppearance {
        /**
         * proposal id
         */
        private Long id;

        /**
         * 优化前
         */
        private SearchAppearanceDetail original;

        /**
         * 优化后
         */
        private SearchAppearanceDetail optimize;

        /**
         * 优化建议
         */
        private List<String> advices;

        /**
         * 剩余时间
         */
        private String leftTime;
        /**
         * 图片
         */
        private Boolean canUrlEdit;
        /**
         * 域名
         */
        private String domain;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SearchAppearanceDetail {
        /**
         * 标题
         */
        private String titleTag;
        /**
         * 描述
         */
        private String metaDescription;
        /**
         * url
         */
        private String url;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Images {
        /**
         * proposal id
         */
        private Long id;
        /**
         * description content id
         */
        private Long descriptionId;

        /**
         * 剩余时间
         */
        private String leftTime;

        /**
         * 图片大小单位
         */
        private String sizeUnit;

        /**
         * 原始图片列表
         */
        private List<Image> original;

        /**
         * 优化图片列表
         */
        private List<Image> optimize;

        /**
         * 优化建议
         */
        private List<String> advices;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Image {
        /**
         * image id
         */
        private Long id;

        /**
         * 图片url
         */
        private String url;

        /**
         * 图片大小
         */
        private Integer size;

        /**
         * alt标签
         */
        private String alt;

        /**
         * 格式
         */
        private String format;
    }
}



