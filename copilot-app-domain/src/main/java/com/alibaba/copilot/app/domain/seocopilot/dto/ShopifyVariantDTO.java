package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.copilot.boot.shopify.web.data.product.PresentmentPrice;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * ShopifyVariantDTO
 *
 * <AUTHOR>
 * @date 2024/7/8 4:15 下午
 */
@Data
public class ShopifyVariantDTO {

    private String id;

    @JSONField(name = "product_id")
    private String productId;

    /**
     * sku标题
     */
    private String title;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 原价
     */
    @JSONField(name = "compare_at_price")
    private BigDecimal compareAtPrice;

    /**
     * 成本价
     */
    private BigDecimal cost;

    /**
     * 展示价格
     */
    private List<PresentmentPriceDTO> presentmentPrices;

    /**
     * sku码
     */
    private String sku;

    /**
     * sku条形码
     */
    private String barcode;

    /**
     *
     */
    private int position;

    /**
     * grams
     */
    private long grams;

    /**
     * 库存数量
     */
    @JSONField(name = "inventory_quantity")
    private Long inventoryQuantity;

    /**
     * imageId
     */
    @JSONField(name = "image_id")
    private String imageId;

    /**
     * 库存模式
     */
    @JSONField(name = "inventory_policy")
    private String inventoryPolicy;

    /**
     * 库存管理方式
     */
    @JSONField(name = "inventory_management")
    private String inventoryManagement;

    /**
     * 销售属性1
     */
    private String option1;

    /**
     * 销售属性2
     */
    private String option2;

    /**
     * 销售属性3
     */
    private String option3;

    /**
     * 履约服务
     */
    @JSONField(name = "fulfillment_service")
    private String fulfillmentService;

    /**
     * 是否需要运费
     */
    @JSONField(name = "requires_shipping")
    private boolean requiresShipping;

    /**
     * 是否收税
     */
    private boolean taxable;

    /**
     * inventoryItemId
     */
    @JSONField(name = "inventory_item_id")
    private String inventoryItemId;

    /**
     * available
     */
    private long available;
}
