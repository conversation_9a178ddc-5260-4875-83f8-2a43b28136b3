package com.alibaba.copilot.app.domain.aiIistmaster.diamond.spi;

/**
 * <AUTHOR>
 * @date 2023/8/10 11:38 AM
 */
public interface DiamondSpi {
    String getConfig(String var1, String var2, long var3);

    boolean setConfig(String var1, String var2, String var3);

    boolean setConfigCas(String var1, String var2, String var3, String var4);

    void addListener(String var1, String var2, DiamondListener var3);

    public interface DiamondListener {
        void receiveConfigInfo(String var1);
    }
}