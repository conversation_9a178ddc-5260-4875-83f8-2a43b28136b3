package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.copilot.app.client.seocopilot.constant.AnalyticsPageType;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class AnalyticsPageDTO {

    private Map<AnalyticsPageType,List<PageData>> pages;

    /**
     * 废弃
     */
    @Deprecated
    private PageData homePage;

    /**
     * 废弃
     */
    @Deprecated
    private List<PageData> productPages;


    /**
     * 废弃
     */
    @Deprecated
    private List<PageData> collectionPages;

    /**
     * 废弃
     */
    @Deprecated
    private List<PageData> blogPages;

    @Data
    @Builder
    public static class PageData {
        private String pageUrl;
        private String title;
    }
}
