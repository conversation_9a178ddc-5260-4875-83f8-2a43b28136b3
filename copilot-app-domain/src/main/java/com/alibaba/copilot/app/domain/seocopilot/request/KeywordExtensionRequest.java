package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * KeywordExtensionRequest
 *
 * <AUTHOR>
 * @date 2024/6/20 4:30 下午
 */
@Data
public class KeywordExtensionRequest {

    /**
     * apiName
     */
    private String apiName = "seo_keyword_extension";

    /**
     * srcBizName
     */
    private String srcBizName = "";

    /**
     * scene
     */
    private String scene = "";

    /**
     * data
     */
    private RequestData data;


    @Data
    public static class RequestData {

        /**
         * max_return_num
         */
        @JSONField(name = "max_return_num")
        private Integer maxReturnNum;

        /**
         * thresh
         */
        private BigDecimal thresh;

        /**
         * topic
         */
        private List<String> topic;
    }
}
