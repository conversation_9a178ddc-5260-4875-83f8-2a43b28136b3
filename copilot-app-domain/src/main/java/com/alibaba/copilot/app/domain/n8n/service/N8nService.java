package com.alibaba.copilot.app.domain.n8n.service;

import com.alibaba.copilot.app.client.flow.FlowNodeExecuteSPI;
import com.alibaba.copilot.app.client.flow.model.FlowNodeRequest;
import com.alibaba.copilot.app.client.flow.model.FlowNodeResponse;
import com.alibaba.copilot.app.domain.base.bizlog.BizLogMonitor;
import com.alibaba.copilot.app.domain.base.bizlog.BizLogTypeEnum;
import com.alibaba.copilot.app.domain.base.gateway.mq.MessageQueueProducer;
import com.alibaba.copilot.app.domain.n8n.adapter.GenericHsfInvoker;
import com.alibaba.copilot.app.domain.n8n.model.FlowTriggerEvent;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/05/17
 */
@Service
@Slf4j
public class N8nService {

    @Resource
    private GenericHsfInvoker genericHsfInvoker;
    @Resource(name = "vpcSoutheastProducer")
    private MessageQueueProducer messageQueueProducer;



    public FlowNodeResponse invoke(FlowNodeRequest request) {

        Object result = genericHsfInvoker.invoke(
                FlowNodeExecuteSPI.class.getCanonicalName()
                , "1.0.0." + request.getAppCode() + "." + request.getActionCode()
                , GenericHsfInvoker.DEFAULT_GROUP
                , "execute"
                , new String[]{"com.alibaba.copilot.app.client.flow.model.FlowNodeRequest"}
                , request
        );

        log.warn("hsf invoke result:{}, request:{}", JSON.toJSONString(result), JSON.toJSONString(request));

        JSONObject res = JSON.parseObject(JSON.toJSONString(result));
        JSONObject data = res.getJSONObject("data");
        if (Objects.isNull(data)) {
            throw new RuntimeException("data is null");
        }

        return JSON.parseObject(JSON.toJSONString(data), FlowNodeResponse.class);
    }


    public void sendFlowTriggerEvent(FlowTriggerEvent event) {
        BizLogMonitor bizLogMonitor = BizLogMonitor.of(BizLogTypeEnum.FLOW_TRIGGER_SEND_MQ);
        try {
            if (ObjectUtils.anyNull(event, event.getAppCode(), event.getSceneCode())) {
                throw new RuntimeException("illegal event");
            }
            messageQueueProducer.send("aib_flow_trigger_topic", event.getAppCode(), event);
        } catch (Exception e) {
            bizLogMonitor.error(e.getMessage());
        } finally {
            bizLogMonitor.recordLog(event);
        }
    }



}