package com.alibaba.copilot.app.domain.seocopilot.constant;

import lombok.Getter;

import java.math.BigDecimal;

/**
 * CollectionContent检测规则量化指标
 */
@Getter
public enum CollectionContentCheckItem {
    /**
     * 未设置title tag
     */
    no_title(BigDecimal.ZERO, "Collection title should be set.", "Collection title is optimized for improved relevance."),
    /**
     * title不包含关键词
     */
    title_no_keyword(BigDecimal.ZERO, "Collection title should include target keywords.", "Collection title is optimized for improved relevance."),
    /**
     * title tag长度应该≤70
     */
    title_length(BigDecimal.ZERO, "Collection titles should be no longer than 70 characters.", "Collection title is optimized for improved relevance."),
    /**
     * 未设置description
     */
    no_description(BigDecimal.ZERO, "Collection description should be set.", "Collection description has been optimized for the improved effect."),
    /**
     * description不包含关键词
     */
    description_no_keyword(BigDecimal.ZERO, "Collection description should include target keywords.", "Collection description has been optimized for the improved effect."),
    /**
     * description包含关键词个数太多或太少（建议4-6个)
     */
    description_keyword_count(BigDecimal.ZERO, "Collection description contains too many or too few keywords.", "Collection description has been optimized for the improved effect."),
    /**
     * description长度应该≤320个字符
     */
    description_length(BigDecimal.ZERO, "Collection description length should be less than 320 characters.", "Collection description has been optimized for the improved effect."),
    /**
     * 原创度
     */
    unique(BigDecimal.ZERO, "The collections' plagiarism rate should be lower than 30%.", "We've enhanced your collection's originality through careful editing."),
    /**
     * 关键词密度1%-2%
     */
    keywords_density(BigDecimal.valueOf(15), "We suggest 1%-2%  keywords density.", "We've optimized keywords for increased relevance."),
    /**
     * 包含图片
     */
    need_image(BigDecimal.valueOf(10), "Each collection's body should include one image at least.", "Suitable images have been integrated for better readability."),
    /**
     * 图片有alt标签
     */
    image_alt(BigDecimal.valueOf(25), "Each image should have one alt tag at least.", "Image alt tags have been fine-tuned for improved indexing."),
    /**
     * 存在h标签
     */
    h_exist(BigDecimal.valueOf(5), "We recommend at least one H tag.", "H tags in this collection have been optimized for better impact."),
    /**
     * h1标签在顶部
     */
    h1_top(BigDecimal.valueOf(5), "We recommend localize H1 tag at the top.", "H1 tags in this collection have been optimized for better impact."),
    /**
     * 只能有一个h1标签
     */
    h1_count(BigDecimal.valueOf(20), "We recommend set up one tag for H1 tag.", "H1 tags in this collection have been optimized for better impact."),
    /**
     * h2标签4-6个
     */
    h2_count(BigDecimal.valueOf(10), "We recommend 4-6 tags for H2 tag.", "H2 tags in this collection have been optimized for better impact."),
    /**
     * 需要有h3标签
     */
    h3_count(BigDecimal.ZERO, "We recommend insert H3 tag.", "H3 tags in this collection have been optimized for better impact.");

    /**
     * 比重
     */
    private BigDecimal weight;
    private String advice;
    private String conclusion;

    CollectionContentCheckItem(BigDecimal weight, String advice, String conclusion) {
        this.weight = weight;
        this.advice = advice;
        this.conclusion = conclusion;
    }

}
