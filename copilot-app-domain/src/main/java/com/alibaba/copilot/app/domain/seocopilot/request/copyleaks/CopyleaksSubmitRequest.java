package com.alibaba.copilot.app.domain.seocopilot.request.copyleaks;


import com.alibaba.copilot.app.domain.seocopilot.constant.CopyleaksTriggerType;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class CopyleaksSubmitRequest implements Serializable {
    private String filename;
    private String content;
    private CopyleaksTriggerType type;

    private Long articleId;

    private Long checkRecordId;

    private Long shopId;
}
