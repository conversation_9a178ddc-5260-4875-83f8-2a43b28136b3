package com.alibaba.copilot.app.domain.aiIistmaster.utils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.AdvisedSupport;
import org.springframework.aop.framework.AopProxy;
import org.springframework.aop.support.AopUtils;

/**
 * <AUTHOR>
 * @date 2023/8/10 2:09 PM
 */
public class ReflectionUtils {
    private static final Logger log = LoggerFactory.getLogger(ReflectionUtils.class);
    private static final Map<String, Object> classFieldMap = new HashMap();
    private static final Map<String, Method> classDeclaredMethodMap = new HashMap();
    private static final Map<String, Method> classMethodMap = new HashMap();
    private static final Map<String, Method> classNameMethodMap = new HashMap();
    private static final Map<String, String> interfaceMap = new ConcurrentHashMap();

    public ReflectionUtils() {
    }

    public static Method getDeclaredMethod(Object object, String methodName, Class... parameterTypes) {
        Class clazz = object.getClass();

        while(clazz != Object.class) {
            try {
                String key = clazz.getName() + "." + methodName + "(" + (String)Arrays.asList(parameterTypes).stream().map((type) -> {
                    return type.getName();
                }).collect(Collectors.joining(",")) + ")";
                Method method = (Method)classDeclaredMethodMap.get(key);
                if (method == null) {
                    method = clazz.getDeclaredMethod(methodName, parameterTypes);
                    classDeclaredMethodMap.put(key, method);
                }

                return method;
            } catch (Exception var6) {
                clazz = clazz.getSuperclass();
            }
        }

        return null;
    }

    public static Method getDeclaredMethod(Class<?> clazz, String methodName) {
        String key = clazz.getName() + "." + methodName;
        Method returnMethod = (Method)classNameMethodMap.get(key);
        if (returnMethod == null) {
            Method[] methods = clazz.getDeclaredMethods();
            Method[] var5 = methods;
            int var6 = methods.length;

            for(int var7 = 0; var7 < var6; ++var7) {
                Method method = var5[var7];
                if (method.getName().equals(methodName)) {
                    returnMethod = method;
                    classNameMethodMap.put(key, method);
                }
            }
        }

        return returnMethod;
    }

    public static Method getMethod(Class<?> clazz, String methodName) {
        String key = clazz.getName() + "." + methodName;
        Method returnMethod = (Method)classMethodMap.get(key);
        if (returnMethod == null) {
            Method[] methods = clazz.getMethods();
            Method[] var5 = methods;
            int var6 = methods.length;

            for(int var7 = 0; var7 < var6; ++var7) {
                Method method = var5[var7];
                if (method.getName().equals(methodName)) {
                    returnMethod = method;
                    classMethodMap.put(key, method);
                }
            }
        }

        return returnMethod;
    }

    public static List<Field> getDeclaredFields(Class clazz) {
        ArrayList fieldList;
        for(fieldList = new ArrayList(); clazz != null && clazz != Object.class; clazz = clazz.getSuperclass()) {
            fieldList.addAll(Arrays.asList(clazz.getDeclaredFields()));
        }

        return fieldList;
    }

    public static Field getDeclaredField(Object object, String fieldName) {
        Class<?> clazz = object.getClass();
        String key = clazz.getName() + "." + fieldName;
        Object obj = classFieldMap.get(key);
        if (obj == null) {
            while(true) {
                if (clazz == Object.class) {
                    classFieldMap.put(key, Boolean.FALSE);
                    break;
                }

                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    classFieldMap.put(key, field);
                    return field;
                } catch (Exception var6) {
                    clazz = clazz.getSuperclass();
                }
            }
        }

        return obj != null && obj instanceof Field ? (Field)obj : null;
    }

    public static void setFieldValue(Object object, String fieldName, Object value) {
        Field field = getDeclaredField(object, fieldName);
        if (field != null) {
            field.setAccessible(true);

            try {
                field.set(object, value);
            } catch (Exception var5) {
                log.error("set field value error", var5);
            }
        }

    }

    public static Object getFieldValue(Object object, String fieldName) {
        Field field = getDeclaredField(object, fieldName);
        if (field != null) {
            field.setAccessible(true);

            try {
                return field.get(object);
            } catch (Exception var4) {
                ;
            }
        }

        return null;
    }

    public static Class getClassGenericType(Class clazz, int index) {
        ParameterizedType parameterizedType = (ParameterizedType)clazz.getGenericSuperclass();
        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
        return actualTypeArguments != null && actualTypeArguments.length > index ? (Class)actualTypeArguments[index] : null;
    }

    public static Class getFieldGenericClass(Field f) {
        Type genericType = f.getGenericType();
        if (genericType != null && genericType instanceof ParameterizedType) {
            ParameterizedType pt = (ParameterizedType)genericType;
            Class<?> genericClazz = (Class)pt.getActualTypeArguments()[0];
            return genericClazz;
        } else {
            return null;
        }
    }

    public static Object parseFieldObject(String strVal, Field field) {
        Class clazz = field.getType();
        if (String.class.isAssignableFrom(clazz)) {
            return strVal;
        } else {
            if (List.class.isAssignableFrom(clazz)) {
                Class genericType = getFieldGenericClass(field);
                if (genericType != null) {
                    return JSON.parseArray(strVal, genericType);
                }
            }

            return JSON.parseObject(strVal, clazz);
        }
    }

    public static Object getProxyTarget(Object proxy) throws Exception {
        if (!AopUtils.isAopProxy(proxy)) {
            return proxy;
        } else {
            return AopUtils.isJdkDynamicProxy(proxy) ? getJdkDynamicProxyTargetObject(proxy) : getCglibProxyTargetObject(proxy);
        }
    }

    private static Object getCglibProxyTargetObject(Object proxy) throws Exception {
        Field h = proxy.getClass().getDeclaredField("CGLIB$CALLBACK_0");
        h.setAccessible(true);
        Object dynamicAdvisedInterceptor = h.get(proxy);
        Field advised = dynamicAdvisedInterceptor.getClass().getDeclaredField("advised");
        advised.setAccessible(true);
        Object target = ((AdvisedSupport)advised.get(dynamicAdvisedInterceptor)).getTargetSource().getTarget();
        return target;
    }

    private static Object getJdkDynamicProxyTargetObject(Object proxy) throws Exception {
        Field h = proxy.getClass().getSuperclass().getDeclaredField("h");
        h.setAccessible(true);
        AopProxy aopProxy = (AopProxy)h.get(proxy);
        Field advised = aopProxy.getClass().getDeclaredField("advised");
        advised.setAccessible(true);
        Object target = ((AdvisedSupport)advised.get(aopProxy)).getTargetSource().getTarget();
        return target;
    }

    public static String getInterfaceName(String className) {
        if (StringUtils.isBlank(className)) {
            return className;
        } else if (className.indexOf(":") > 0) {
            return className;
        } else {
            try {
                String interfaceName = (String)interfaceMap.get(className);
                if (StringUtils.isBlank(interfaceName)) {
                    Class clazz = Class.forName(className);
                    Class[] interfaces = clazz.getInterfaces();
                    if (interfaces != null && interfaces.length > 0) {
                        interfaceName = interfaces[0].getName();
                    } else {
                        interfaceName = className;
                    }

                    interfaceMap.put(className, interfaceName);
                }

                return interfaceName;
            } catch (Exception var4) {
                log.error("getInterfaceName error, className={}", className);
                interfaceMap.put(className, className);
                return className;
            }
        }
    }
}