package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.domain.seocopilot.constant.SeoEntityStatus;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoBlogArticle;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoBlogArticleSearchParam;

import java.util.List;

public interface SeoBlogArticleRepository {

    List<SeoBlogArticle> getByIds(List<Long> ids);

    SeoBlogArticle getById(Long id);

    List<SeoBlogArticle> getByArticleId(Long articleId);

    List<SeoBlogArticle> getByArticleId(Long shopId, Long articleId);

    List<SeoBlogArticle> getByShopifyArticleIdIgnoreDeleted(Long shopId, Long shopifyArticleId);

    List<SeoBlogArticle> getByShopId(Long shopId);

    List<SeoBlogArticle> getByShopIdOneDay(Long shopId);

    /**
     * updateAt 在14天内的记录
     * @param shopId
     * @return
     */
    List<SeoBlogArticle> getByShopIdIn14Day(Long shopId);

    PageWrapper<SeoBlogArticle> queryArticles(Long shopId, SeoBlogArticleSearchParam param);

    Long save(SeoBlogArticle seoBlogArticle);

    Integer updateStatusById(Long id, SeoEntityStatus status);

    Integer deleteByShopId(Long shopId);

    Integer deleteById(Long id);

    Integer deleteByNotInShopifyArticleId(Long shopId, List<Long> notInShopifyArticleId);

    List<SeoBlogArticle> getArticlesByNotInShopifyArticleId(Long shopId, List<Long> notInShopifyArticleId);

    Integer deleteSeoBlogArticle(SeoBlogArticle seoBlogArticle);

    List<SeoBlogArticle> getArticleByHandleAndPublishStatus(String handle, boolean publishStatus);

    List<SeoBlogArticle> getArticlesByHandlesAndPublishStatus(List<String> handle, Long shopId, boolean publishStatus);

    Long getTotalCountIncludeDeleted(Long shopId);

    Long getTodayCountIncludeDeleted(Long shopId);

    List<SeoBlogArticle> getByTitle(String title,Long shopId);

    List<SeoBlogArticle> getByRelatedArticleId(Long shopId, Long relatedArticleId);

    List<SeoBlogArticle> getByRelatedArticleIdAndLang(Long shopId, Long relatedArticleId, String language);

    List<SeoBlogArticle> getArticlesWithGraphQLIdIsNotNUll();
}
