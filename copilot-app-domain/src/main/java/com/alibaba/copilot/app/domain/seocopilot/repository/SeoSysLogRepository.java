package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoSysLog;

/**
 * SeoSysLogRepository
 */
public interface SeoSysLogRepository {

    /**
     * 保存seoSysLog
     */
    Long saveSeoSysLog(SeoSysLog seoSysLog);

    /**
     * 根据id查询seoSysLog
     */
    SeoSysLog getSeoSysLogById(Long id);

    /**
     * 分页查询sysLog
     *
     * @param shopId
     * @param dateStart
     * @param dateEnd
     * @param pageNum
     * @param pageSize
     * @return
     */
    PageWrapper<SeoSysLog> getSysLogsByShopId(Long shopId, String dateStart, String dateEnd, Integer pageNum, Integer pageSize);

    /**
     * 查询sysLog
     *
     * @param shopId
     * @param taskId
     * @return
     */
    SeoSysLog getSysLogByShopIdAndTaskId(Long shopId, Long taskId);

    /**
     * 查询sysLog
     *
     * @param shopId
     * @return
     */
    SeoSysLog getSysLogByShopIdOneDay(Long shopId);

    /**
     * 删除sysLog
     *
     * @param shopId
     * @return
     */
    Integer deleteByShopId(Long shopId);
}
