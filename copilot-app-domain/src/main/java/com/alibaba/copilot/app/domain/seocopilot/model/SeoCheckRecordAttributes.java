package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.boot.basic.data.Attributes;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SeoCheckRecordAttributes
 */
public class SeoCheckRecordAttributes extends Attributes {

    public SeoCheckRecordAttributes(String json) {
        super(json);
    }

    /**
     * 多轮生成
     */
    public static final String MULTITURN = "multiturn";
    /**
     * 多轮优化
     */
    public static final String MULTITURN_OPTIMIZE = "multiturnOptimize";

    /**
     * Google返回的具体问题
     */
    private static final String HOMEPAGE_AUDIT_DETAIL_ITEMS = "homepageAuditDetailItems";

    /**
     * 原文keywords
     */
    private static final String ORIGIN_KEYWORDS = "originKeywords";
    /**
     * 优化建议的keywords
     */
    private static final String OPTIMIZED_KEYWORDS = "optimizedKeywords";

    /**
     * OSS存储 URL
     */
    private static final String OSS_URL = "ossUrl";

    /**
     * 问题的权重（方便按照重要程度排序）
     */
    private static final String WEIGHT = "weight";

    /**
     * 优化建议的列表
     */
    private static final String ADVICE_LIST = "adviceList";
    /**
     * title优化建议的列表
     */
    private static final String TITLE_ADVICE_LIST = "titleAdviceList";
    /**
     * description优化建议的列表
     */
    private static final String DESCRIPTION_ADVICE_LIST = "descriptionAdviceList";
    /**
     * image优化建议的列表
     */
    private static final String IMAGE_ADVICE_LIST = "imageAdviceList";

    /**
     * 优化总结
     */
    private static final String IMAGE_URL_2_CHECK_ITEM = "imageUrl2CheckItem";

    /**
     * 优化总结
     */
    private static final String CONCLUSION_LIST = "conclusionList";
    /**
     * title优化总结
     */
    private static final String TITLE_CONCLUSION_LIST = "titleConclusionList";
    /**
     * description优化总结
     */
    private static final String DESCRIPTION_CONCLUSION_LIST = "descriptionConclusionList";
    /**
     * image优化总结
     */
    private static final String IMAGE_CONCLUSION_LIST = "imageConclusionList";

    private static final String PRODUCE_BY = "produceBy";

    private static final String APPLY_MODE = "applyMode";

    private static final String CHECK_GEN_BLOG_ENTITLEMENT = "checkGenBlogEntitlement";
    private static final String GEN_BLOG_DEDUCTION = "genBlogDeduction";

    private static final String CHECK_OPT_BLOG_ENTITLEMENT = "checkOptBlogEntitlement";
    private static final String OPT_BLOG_DEDUCTION = "optBlogDeduction";

    private static final String CHECK_OPT_PRODUCT_ENTITLEMENT = "checkOptProductEntitlement";
    private static final String OPT_PRODUCT_DEDUCTION = "optProductDeduction";

    private static final String CHECK_OPT_COLLECTION_ENTITLEMENT = "checkOptCollectionEntitlement";
    private static final String OPT_COLLECTION_DEDUCTION = "optCollectionDeduction";

    /**
     * 实体图片问题总结
     *
     * @param
     */
    private static final String ENTITY_IMAGE_OPTIMIZE_MAP = "entityImageOptimizeMap";

    public void setEntityImageOptimizeMap(Map<String, List<String>> imageMap) {
        put(ENTITY_IMAGE_OPTIMIZE_MAP, imageMap);
    }

    public Map<String, List<String>> getEntityImageOptimizeMap() {
        if (!containsKey(ENTITY_IMAGE_OPTIMIZE_MAP)) {
            return new HashMap<>();
        }
        return get(ENTITY_IMAGE_OPTIMIZE_MAP, Map.class);
    }

    public void setAdviceList(List<String> adviceList) {
        put(ADVICE_LIST, adviceList);
    }

    public List<String> getAdviceList() {
        if (!containsKey(ADVICE_LIST)) {
            return new ArrayList<>();
        }
        return get(ADVICE_LIST, List.class);
    }

    public void setTitleAdviceList(List<String> titleAdviceList) {
        put(TITLE_ADVICE_LIST, titleAdviceList);
    }

    public List<String> getTitleAdviceList() {
        return get(TITLE_ADVICE_LIST, List.class);
    }

    public void setDescriptionAdviceList(List<String> descriptionAdviceList) {
        put(DESCRIPTION_ADVICE_LIST, descriptionAdviceList);
    }

    public List<String> getDescriptionAdviceList() {
        return get(DESCRIPTION_ADVICE_LIST, List.class);
    }

    public void setImageAdviceList(List<String> imageAdviceList) {
        put(IMAGE_ADVICE_LIST, imageAdviceList);
    }

    public List<String> getImageAdviceList() {
        return get(IMAGE_ADVICE_LIST, List.class);
    }

    public void setConclusionList(List<String> conclusionList) {
        put(CONCLUSION_LIST, conclusionList);
    }

    public List<String> getConclusionList() {
        return get(CONCLUSION_LIST, List.class);
    }

    public void setProduceBy(String produceBy) {
        put(PRODUCE_BY, produceBy);
    }

    public String getProduceBy() {
        return get(PRODUCE_BY, String.class);
    }

    public void setApplyMode(String applyMode) {
        put(APPLY_MODE, applyMode);
    }

    public String getApplyMode() {
        return get(APPLY_MODE, String.class);
    }

    public void setCheckGenBlogEntitlement(Boolean checkGenBlogEntitlement) {
        put(CHECK_GEN_BLOG_ENTITLEMENT, checkGenBlogEntitlement);
    }

    public Boolean getCheckGenBlogEntitlement() {
        return get(CHECK_GEN_BLOG_ENTITLEMENT, Boolean.class);
    }

    public void setGenBlogDeduction(Integer genBlogDeduction) {
        put(GEN_BLOG_DEDUCTION, genBlogDeduction);
    }

    public Integer getGenBlogDeduction() {
        return get(GEN_BLOG_DEDUCTION, Integer.class);
    }

    public void setCheckOptBlogEntitlement(Boolean checkOptBlogEntitlement) {
        put(CHECK_OPT_BLOG_ENTITLEMENT, checkOptBlogEntitlement);
    }

    public Boolean getCheckOptBlogEntitlement() {
        return get(CHECK_OPT_BLOG_ENTITLEMENT, Boolean.class);
    }

    public void setOptBlogDeduction(Integer optBlogDeduction) {
        put(OPT_BLOG_DEDUCTION, optBlogDeduction);
    }

    public Integer getOptBlogDeduction() {
        return get(OPT_BLOG_DEDUCTION, Integer.class);
    }

    public void setCheckOptProductEntitlement(Boolean checkOptProductEntitlement) {
        put(CHECK_OPT_PRODUCT_ENTITLEMENT, checkOptProductEntitlement);
    }

    public Boolean getCheckOptProductEntitlement() {
        return get(CHECK_OPT_PRODUCT_ENTITLEMENT, Boolean.class);
    }

    public void setOptProductDeduction(Integer optProductDeduction) {
        put(OPT_PRODUCT_DEDUCTION, optProductDeduction);
    }

    public Integer getOptProductDeduction() {
        return get(OPT_PRODUCT_DEDUCTION, Integer.class);
    }

    public void setCheckOptCollectionEntitlement(Boolean checkOptCollectionEntitlement) {
        put(CHECK_OPT_COLLECTION_ENTITLEMENT, checkOptCollectionEntitlement);
    }

    public Boolean getCheckOptCollectionEntitlement() {
        return get(CHECK_OPT_COLLECTION_ENTITLEMENT, Boolean.class);
    }

    public void setOptCollectionDeduction(Integer optCollectionDeduction) {
        put(OPT_COLLECTION_DEDUCTION, optCollectionDeduction);
    }

    public Integer getOptCollectionDeduction() {
        return get(OPT_COLLECTION_DEDUCTION, Integer.class);
    }

    public void setTitleConclusionList(List<String> titleConclusionList) {
        put(TITLE_CONCLUSION_LIST, titleConclusionList);
    }

    public List<String> getTitleConclusionList() {
        return get(TITLE_CONCLUSION_LIST, List.class);
    }

    public void setDescriptionConclusionList(List<String> descriptionConclusionList) {
        put(DESCRIPTION_CONCLUSION_LIST, descriptionConclusionList);
    }

    public List<String> getDescriptionConclusionList() {
        return get(DESCRIPTION_CONCLUSION_LIST, List.class);
    }

    public void setImageConclusionList(List<String> imageConclusionList) {
        put(IMAGE_CONCLUSION_LIST, imageConclusionList);
    }

    public List<String> getImageConclusionList() {
        return get(IMAGE_CONCLUSION_LIST, List.class);
    }

    public void setHomepageAuditDetailItems(String items) {
        put(HOMEPAGE_AUDIT_DETAIL_ITEMS, items);
    }

    public String getHomepageAuditDetailItems() {
        return getAsString(HOMEPAGE_AUDIT_DETAIL_ITEMS);
    }

    public void setOptimizedKeywords(List<String> keywords) {
        put(OPTIMIZED_KEYWORDS, keywords);
    }

    public List<String> getOptimizedKeywords() {
        return get(OPTIMIZED_KEYWORDS, List.class);
    }

    public void setImageUrl2CheckItem(Map<String, String> imageUrl2CheckItemMap) {
        put(IMAGE_URL_2_CHECK_ITEM, imageUrl2CheckItemMap);
    }

    public Map<String, String> getImageUrl2CheckItem() {
        if (!containsKey(IMAGE_URL_2_CHECK_ITEM)) {
            return new HashMap<>();
        }
        return get(IMAGE_URL_2_CHECK_ITEM, Map.class);
    }

    public void setOriginKeywords(List<String> keywords) {
        put(ORIGIN_KEYWORDS, keywords);
    }

    public List<String> getOriginKeywords() {
        return get(ORIGIN_KEYWORDS, List.class);
    }

    public void setOssUrl(String url) {
        put(OSS_URL, url);
    }

    public String getOssUrl() {
        return getAsString(OSS_URL);
    }

    public void setWeight(BigDecimal weight) {
        put(WEIGHT, weight);
    }

    public BigDecimal getWeight() {
        String weight = getAsString(WEIGHT);
        if (StringUtils.isBlank(weight)) {
            return null;
        }
        return new BigDecimal(weight);
    }
}
