package com.alibaba.copilot.app.domain.seocopilot.request;

import java.util.List;
import java.util.Map;

import com.alibaba.copilot.app.domain.seocopilot.constant.SeoBizNameEnum;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @des 关键词算法生成入参
 */
@Data
@Builder
public class KeywordGenerateRequest {

    /**
     * 场景 Collection/Product/Blog/Homepage
     */
    private SeoBizNameEnum seoBizNameEnum;

    /**
     * 算法业务参数
     */
    private KeywordGenerateParam keywordGenerateParam;

    /**
     * 算法业务参数
     */
    @Data
    public static class KeywordGenerateParam {
        /**
         * 用户输入，如果没有就纯ai生成
         */
        private String user_input;
        /**
         * 原始标题   必传
         */
        private String item_title;
        /**
         * pv属性，规格等，没有就空
         * 格式：   a:b;c:d
         */
        private String item_spec;
        /**
         * 原始描述
         */
        private String item_detail;
        /**
         * sourceLanguage
         * 默认传： en
         */
        private String target_language;
        /**
         * Category类目  分等级，有多少写多少
         * cate1,cate2,cate3
         */
        private List<String> pcate_list;
        /**
         * shopifyShopId
         */
        private Long shopify_shop_id;
        /**
         * 店铺里所有商品的类目及频次
         * key:商品类目名  value:频次
         */
        private Map<String, Integer> product_category_map;
        /**
         * blog提取关键词
         */
        private List<String> keywords;
    }
}
