package com.alibaba.copilot.app.domain.seocopilot.service.subscription;

import com.alibaba.copilot.app.client.seocopilot.request.WebSubscribePlanRequest;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShop;

/**
 * Alpharank订阅服务
 *
 * <AUTHOR>
 * @date 2024/9/18 5:47 下午
 */
public interface WebSubscriptionService {

    /**
     * Stripe订阅
     *
     * @param shopId
     * @param request
     * @return
     */
    String subscribePlanByStripe(Long shopId, WebSubscribePlanRequest request);

    /**
     * 订阅
     *
     * @param shopId
     * @param request
     * @return
     */
    String subscribePlan(Long shopId, WebSubscribePlanRequest request);

    /**
     * 取消订阅
     *
     * @param shopId
     * @param request
     * @return
     */
    Boolean cancelSubscribedPlan(Long shopId, WebSubscribePlanRequest request);
}
