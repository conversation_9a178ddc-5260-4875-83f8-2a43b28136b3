package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * ImageDTO
 *
 * <AUTHOR>
 * @date 2024/7/8 4:12 下午
 */
@Data
public class ImageDTO {

    /**
     * 图片ID
     */
    private String id;

    /**
     * 图片链接
     */
    private String src;

    /**
     * 图片位置
     */
    private Integer position;

    /**
     * 商品ID
     */
    @JSONField(name = "product_id")
    private String productId;

    /**
     * SKU ids
     */
    private List<String> variantIds;

    /**
     * ALT标签
     */
    private String alt;
}
