package com.alibaba.copilot.app.domain.seocopilot.model;

import lombok.Data;

import java.util.List;

@Data
public class SeoBlogArticleSearchParam {
    /**
     * 按时间排序 0:从近到远 1:从远到近
     */
    Boolean timeOrder;

    /**
     * 博客类型ID
     */
    Long typeId;

    /**
     * 博客评分级别 bad/average/good
     */
    String range;

    /**
     * 博客状态 NoOptimizeNeeded/Optimizing/Optimized/NeedsOptimize
     */
    List<String> statusList;

    /**
     * 博客状态 NoOptimizeNeeded/Optimizing/Optimized/NeedsOptimize
     */
    List<String> statusNotInList;

    /**
     * 日期 2023-10-11
     */
    String dateStart;

    /**
     * 日期 2023-10-11 23:59:59
     */
    String dateEnd;

    /**
     * 文章是否为AI生成  true(只看AI生成的)/false(所有的)
     */
    Boolean IsAIGenerated;

    Integer pageNum;
    Integer pageSize;
    long scrollStart;
    String title;
    String keywords;

    /**
     * 语种
     */
    String language;
}
