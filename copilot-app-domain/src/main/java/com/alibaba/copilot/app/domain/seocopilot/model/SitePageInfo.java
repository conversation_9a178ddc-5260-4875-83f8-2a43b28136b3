package com.alibaba.copilot.app.domain.seocopilot.model;


import com.alibaba.copilot.app.domain.seocopilot.constant.SitePageStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * seo_site_page
 *
 * <AUTHOR>
@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class SitePageInfo {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 站点ID
     */
    private Long siteId;

    /**
     * 页面url
     */
    private String url;

    /**
     * 页面类型
     */
    private String type;

    /**
     * 页面文字信息
     */
    private String content;

    /**
     * 清洗后的页面文字信息
     */
    private String cleanContent;

    /**
     * 页面html oss url
     */
    private String ossUrl;

    /**
     * 页面状态
     */
    private SitePageStatusEnum status;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

}
