package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.domain.seocopilot.model.PromptTemplate;
import com.alibaba.copilot.app.domain.seocopilot.request.PromptTemplateQuery;

import java.util.List;

public interface PromptTemplateRepository {
    PromptTemplate getById(Long templateId);
    Long save(PromptTemplate promptTemplate);
    PromptTemplate getActivateByCode(String code);
    
    /**
     * 根据查询条件获取模板列表
     * @param query 查询条件
     * @return 模板列表
     */
    List<PromptTemplate> listPromptTemplate(PromptTemplateQuery query);
}
