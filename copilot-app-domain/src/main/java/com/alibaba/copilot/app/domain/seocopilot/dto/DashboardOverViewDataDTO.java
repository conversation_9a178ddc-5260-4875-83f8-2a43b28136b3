package com.alibaba.copilot.app.domain.seocopilot.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
public class DashboardOverViewDataDTO {
    /**
     * 数据截止时间
     */
    private String dataCutoffTime;
    /**
     * 平均排名
     */
    private BigDecimal position;
    /**
     * 平均排名环比 -31.25%
     */
    private String positionGrowth;
    /**
     * 曝光量
     */
    private BigDecimal impressions;
    /**
     * 未转换单位的实际曝光量
     */
    private Long realImpressions;
    /**
     * 曝光量单位 k m等
     */
    private String impressionsUnit;
    /**
     * 曝光量环比 -31.25%
     */
    private String impressionsGrowth;
    /**
     * 点击量
     */
    private BigDecimal click;
    /**
     * 未转换单位的实际点击量
     */
    private Long realClicks;
    /**
     * 点击数单位 k m等
     */
    private String clicksUnit;
    /**
     * 点击量环比 -31.25%
     */
    private String clickGrowth;
    /**
     * ctr
     */
    private BigDecimal ctr;
    /**
     * ctr环比 -31.25%
     */
    private String ctrGrowth;
    /**
     * 站点当天的新用户数
     */
    private BigDecimal newUsers;
    /**
     * 未转换单位的实际新用户数
     */
    private Long realNewUsers;
    /**
     * 新用户数单位 k m等
     */
    private String newUsersUnit;
    /**
     * 新用户增长环比 24.2%
     */
    private String newUsersGrowth;
    /**
     * 感兴趣话题比例。
     */
    private BigDecimal engagementRate;
    /**
     * 感兴趣话题比例环比 24.2%
     */
    private String engagementRateGrowth;
    /**
     * 过去30天的平均排名统计
     */
    private List<DashboardDateGroupByDataDTO> positionStatistics;
    /**
     * 过去30天曝光量统计
     */
    private List<DashboardDateGroupByDataDTO> impressionsStatistics;
    /**
     * 过去30天的ctr统计
     */
    private List<DashboardDateGroupByDataDTO> ctrStatistics;
    /**
     * 过去30天的点击量统计
     */
    private List<DashboardDateGroupByDataDTO> clickStatistics;
    /**
     * 新用户统计
     */
    private List<DashboardDateGroupByDataDTO> newUsersStatistics;
    /**
     * 过去30天的网站或应用在用户设备前台运行的总时长（以秒为单位）。
     */
    private List<DashboardDateGroupByDataDTO> engagementRateStatistics;
    /**
     * gsc有效性
     */
    private Boolean gscValid;
    /**
     * ga有效性
     */
    private Boolean gaValid;

    public static DashboardOverViewDataDTO defaultInstance(){
        return DashboardOverViewDataDTO.builder()
                .position(BigDecimal.ZERO)
                .positionGrowth("-")
                .ctr(BigDecimal.ZERO)
                .ctrGrowth("-")
                .newUsers(BigDecimal.valueOf(0))
                .realNewUsers(0L)
                .newUsersUnit("")
                .newUsersGrowth("-")
                .impressions(BigDecimal.ZERO)
                .realImpressions(0L)
                .impressionsUnit("")
                .impressionsGrowth("-")
                .click(BigDecimal.ZERO)
                .realClicks(0L)
                .clicksUnit("")
                .clickGrowth("-")
                .engagementRate(BigDecimal.ZERO)
                .engagementRateGrowth("-")
                .gscValid(Boolean.FALSE)
                .gaValid(Boolean.FALSE)
                .build();
    }

}
