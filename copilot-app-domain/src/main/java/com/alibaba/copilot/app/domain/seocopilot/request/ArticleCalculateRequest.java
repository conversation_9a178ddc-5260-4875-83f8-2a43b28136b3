package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.copilot.app.domain.seocopilot.constant.BlogArticleCheckItem;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoBlogArticleAttributes;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class ArticleCalculateRequest {
    /**
     * 文章内容
     */
    private SeoBlogArticleAttributes articleAttributes;
    /**
     * 文章keywords
     */
    private List<String> keywords;
    /**
     * 校验项
     */
    private BlogArticleCheckItem checkItem;
}
