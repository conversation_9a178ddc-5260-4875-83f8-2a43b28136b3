package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.copilot.app.client.seocopilot.dto.SeoContentGenProductDTO;
import com.alibaba.copilot.app.domain.seocopilot.constant.PromptVersionEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @des Blog Content Ai Prompt
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BlogContentAiPromptRequest {

    /**
     * 关键词
     * 格式：a,b,c,d
     */
    private String keywords;

    /**
     * title
     */
    private String title;

    /**
     * style
     */
    private String style;

    /**
     * blogCategory
     */
    private String blogCategory;

    /**
     * link
     */
    private String link;

    /**
     * productCategory
     */
    private String productCategory;

    /**
     * productTitle
     */
    private String productTitle;

    /**
     * productDescription
     */
    private String productDescription;

    /**
     * outline  整体大纲（不为空）
     */
    private String outline;

    /**
     * 当前大纲标题列表 (不为空）
     */
    private List<CurrentOutline> currentOutlines;

    /**
     * country
     */
    private String country;

    /**
     * retrievalText
     */
    private String retrievalText;

    /**
     * scene
     */
    private String scene;

    /**
     * 当前大纲标题列表
     */
    private List<String> outlines;

    /**
     * productInformation
     */
    private String productInformation;

    /**
     * comparisonInformation
     */
    private String comparisonInformation;

    /**
     * productCount
     */
    private String productCount;

    /**
     * 商品
     */
    private List<SeoContentGenProductDTO> seoContentGenProductDTOs;

    /**
     * 商品信息爬取
     */
    private String productInfoCraw;

    /**
     * 商品信息输入
     */
    private String productInfoInput;

    /**
     * 前文
     */
    private String previousText;

    /**
     * 提纲
     */
    private String currentOutline;

    /**
     * language
     */
    private String language;

    /**
     * promptVersion
     */
    private PromptVersionEnum promptVersion;

    /**
     * tendency
     */
    private String tendency;

    @Data
    public static class CurrentOutline {

        /**
         * currentOutlineH2  当前大纲标题(H2）(不为空）
         */
        private String currentOutlineH2;
        /**
         * currentOutlineH3  子标题(H3）（不为空）
         */
        private String currentOutlineH3;
    }
}
