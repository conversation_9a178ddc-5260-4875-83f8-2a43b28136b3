package com.alibaba.copilot.app.domain.aynctask.worker;

import com.alibaba.copilot.app.domain.aynctask.model.AsyncTask;
import com.alibaba.copilot.app.domain.aynctask.model.AsyncTaskStatus;
import com.alibaba.copilot.app.domain.aynctask.repository.AsyncTaskRepository;
import com.alibaba.copilot.boot.basic.result.Result;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import com.alibaba.copilot.boot.monitor.annotation.MonitorResult;
import com.alibaba.fastjson.JSON;
import com.taobao.eagleeye.EagleEye;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-06-26
 **/
@Slf4j
@Component
public class AsyncTaskWorkerExecutor {

    @Resource
    private AsyncTaskWorkerWatcher asyncTaskWorkerWatcher;

    @Resource
    private AsyncTaskWorkerRegistry asyncTaskWorkerRegistry;

    @Resource
    private AsyncTaskRepository asyncTaskRepository;

    @Monitor(name = "异步任务执行", level = Monitor.Level.P1, layer = Monitor.Layer.OTHER)
    @MonitorResult
    public Result execute(AsyncTask task) {
        log.info("execute, task={}", JSON.toJSONString(task));
        AsyncTaskWorker worker = asyncTaskWorkerRegistry.getWorker(task.getType());
        if (worker == null) {
            log.warn("execute, can not found worker for task, taskId={}, type={}", task.getId(), task.getType());
            return handleNoWorkerTask(task);
        }

        if (task.isExpired()) {
            log.info("execute, task is expired, taskId={}", task.getId());
            return handleExpireTask(task);
        }

        if (task.isExceedRetryTimes()) {
            log.info("execute, task is exceed retry times, taskId={}", task.getId());
            return handleFailTask(task);
        }

        return doExecuteTask(task, worker);
    }

    private Result doExecuteTask(AsyncTask task, AsyncTaskWorker worker) {
        log.info("doExecuteTask, task={}, worker={}", JSON.toJSONString(task), JSON.toJSONString(worker));

        // 更新任务为执行中
        if (!asyncTaskRepository.updateTaskStatus(task, AsyncTaskStatus.RUNNING)) {
            return Result.buildFailure("doExecuteTask, update task status to running fail");
        }

        try {
            SingleResult result = worker.execute(task);
            task.setResult(result);
            task.getAttributes().setExeTraceId(EagleEye.getTraceId());
            AsyncTaskStatus updateStatus = result.isSuccess() ? AsyncTaskStatus.SUCCESS : AsyncTaskStatus.FAILURE;
            boolean ret = asyncTaskRepository.updateTaskStatusAndResult(task, updateStatus);
            log.info(
                    "doExecuteTask, worker execute finish, taskId={}, updateRet={}, updateStatus={}, result={}",
                    task.getId(), ret, updateStatus, task.getResult()
            );
            return Result.buildSuccess();
        } catch (Exception e) {
            log.error("doExecuteTask, worker execute error, taskId=" + task.getId(), e);
            SingleResult result = SingleResult.buildFailure(e.getClass().getSimpleName(), e.getMessage());
            task.setResult(result);
            task.getAttributes().setExeTraceId(EagleEye.getTraceId());
            asyncTaskRepository.updateTaskStatusAndResult(task, AsyncTaskStatus.FAILURE);
            return Result.buildSuccess();
        }
    }

    private Result handleNoWorkerTask(AsyncTask task) {
        asyncTaskRepository.updateTaskStatus(task, AsyncTaskStatus.SUSPEND);
        return Result.buildSuccess();
    }

    private Result handleExpireTask(AsyncTask task) {
        if (task.getExecuteCount() == 0) {
            asyncTaskRepository.updateTaskStatus(task, AsyncTaskStatus.EXPIRED);
        } else {
            asyncTaskRepository.updateTaskStatus(task, AsyncTaskStatus.FAILURE);
        }
        return Result.buildSuccess();
    }

    private Result handleFailTask(AsyncTask task) {
        asyncTaskRepository.updateTaskStatus(task, AsyncTaskStatus.FAILURE);
        return Result.buildSuccess();
    }
}
