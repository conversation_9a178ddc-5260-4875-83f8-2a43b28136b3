package com.alibaba.copilot.app.domain.n8n.adapter;

import com.alibaba.fastjson.JSONObject;
import com.taobao.hsf.app.api.util.HSFApiConsumerBean;
import com.taobao.hsf.model.metadata.MethodSpecial;
import com.taobao.hsf.remoting.service.GenericService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/05/17
 */
@Service
public class GenericHsfInvoker {

    private static final String COLON = ":";
    public static final String DEFAULT_GROUP = "HSF";
    private static final String DEFAULT_VERSION = "1.0.0";
    private static final long CLIENT_TIMEOUT = 3000;
    private static final int MAX_WAIT_TIME_FOR_CS_ADDRESS = 3000;



    private static Map<String, HSFApiConsumerBean> comsumerBeanMap = new ConcurrentHashMap<>();

    private HSFApiConsumerBean createConsumerBean(String interfaceName, String version, String group)
            throws Exception {
        if (StringUtils.isEmpty(group)) {
            group = DEFAULT_GROUP;
        }
        if (StringUtils.isEmpty(version)) {
            version = DEFAULT_VERSION;
        }
        String key = interfaceName + COLON + version + COLON + group;
        if (comsumerBeanMap.containsKey(key)) {
            return comsumerBeanMap.get(key);
        }
        HSFApiConsumerBean consumerBean = new HSFApiConsumerBean();
        consumerBean.setInterfaceName(interfaceName);
        consumerBean.setGeneric("true");
        consumerBean.setGroup(group);
        consumerBean.setVersion(version);

        MethodSpecial methodSpecial = new MethodSpecial();
        methodSpecial.setClientTimeout(CLIENT_TIMEOUT);
        methodSpecial.setMethodName("consume");

        consumerBean.setMethodSpecials(new MethodSpecial[]{methodSpecial});
        consumerBean.setMaxWaitTimeForCsAddress(MAX_WAIT_TIME_FOR_CS_ADDRESS);
        consumerBean.init();
        comsumerBeanMap.put(key, consumerBean);
        return consumerBean;
    }

    public  Object invoke(String interfaceName, String version, String group, String methodName, String[] parameterTypes, Object... args) {
        Long startTime = System.currentTimeMillis();
        try {
            HSFApiConsumerBean consumerBean = createConsumerBean(interfaceName, version, group);
            GenericService genericService = (GenericService) consumerBean.getObject();
            Object obj = genericService.$invoke(methodName, parameterTypes, args);
            return obj;
        } catch (Exception e) {

            JSONObject result = new JSONObject();
            result.put("success", false);
            result.put("msgCode", "-1");
            result.put("msgInfo", e.getMessage());
            result.put("data", null);
            return result;
        }finally {
            Long endTime = System.currentTimeMillis();
        }
    }

}