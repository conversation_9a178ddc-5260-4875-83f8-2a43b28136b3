package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * KeywordRecallDTO
 *
 * <AUTHOR>
 * @date 2024/6/20 4:33 下午
 */
@Data
public class KeywordRecallDTO {

    /**
     * keywordList
     */
    @JSONField(name = "keyword_list")
    private List<KeywordRecallDataDTO> keywordList;

    /**
     * mainContent
     */
    @JSONField(name = "main_content")
    private String mainContent;

    /**
     * modelPredict
     */
    @JSONField(name = "model_predict")
    private List<String> modelPredict;
}
