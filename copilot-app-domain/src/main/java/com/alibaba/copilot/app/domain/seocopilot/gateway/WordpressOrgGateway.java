package com.alibaba.copilot.app.domain.seocopilot.gateway;

import com.alibaba.copilot.app.domain.seocopilot.dto.WordpressOrgTranslationsDTO;
import com.alibaba.copilot.boot.wordpressorg.web.data.categories.WordpressOrgCategory;
import com.alibaba.copilot.boot.wordpressorg.web.data.categories.WordpressOrgGetCategoriesQuery;
import com.alibaba.copilot.boot.wordpressorg.web.data.media.WordpressOrgCreateMediaResponse;
import com.alibaba.copilot.boot.wordpressorg.web.data.posts.WordpressOrgCreatePostsRequest;
import com.alibaba.copilot.boot.wordpressorg.web.data.posts.WordpressOrgCreatePostsResponse;
import com.alibaba.copilot.boot.wordpressorg.web.data.posts.WordpressOrgGetMeResponse;
import com.alibaba.copilot.boot.wordpressorg.web.data.setting.WordpressOrgGetSiteSettingResponse;
import okhttp3.MultipartBody;

import java.util.List;

/**
 * wordpress.org 服务
 */
public interface WordpressOrgGateway {

    WordpressOrgGetMeResponse getMe(String site, String accessToken);

    WordpressOrgCreatePostsResponse createPost(WordpressOrgCreatePostsRequest request, String site, String accessToken);

    WordpressOrgCreateMediaResponse createMedia(String site, String accessToken, MultipartBody.Part file, MultipartBody.Part... param);

    List<WordpressOrgCategory> getCategories(WordpressOrgGetCategoriesQuery request, String site, String accessToken);

    WordpressOrgGetSiteSettingResponse getSiteSetting(String site, String accessToken);

    WordpressOrgTranslationsDTO getTranslations();

}
