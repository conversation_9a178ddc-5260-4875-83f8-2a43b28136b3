package com.alibaba.copilot.app.domain.aiIistmaster.enums;

import java.util.Arrays;

/**
 * @ClassName TemplateTypeEnum
 * <AUTHOR>
 * @Date 2023/8/21 15:47
 */
public enum TemplateTypeEnum {
    /**
     * 标题
     */
    TITLE(100, "title"),

    /**
     * 短卖点
     */
    SHORT_SELL_POINT(101, "ShortSellPoint"),

    /**
     * 商详
     */
    DESCRIPTION(102, "Description");

    private Integer code;
    private String type;

    TemplateTypeEnum(Integer code, String type) {
        this.code = code;
        this.type = type;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public static TemplateTypeEnum parseByType(String originalType) {
        return Arrays.stream(TemplateTypeEnum.values()).filter(o -> originalType.equals(o.type)).findFirst().get();
    }

}
