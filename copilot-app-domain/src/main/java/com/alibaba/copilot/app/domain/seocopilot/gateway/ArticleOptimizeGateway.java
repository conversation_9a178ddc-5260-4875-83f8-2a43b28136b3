package com.alibaba.copilot.app.domain.seocopilot.gateway;

import com.alibaba.copilot.app.domain.seocopilot.dto.ArticleGenerateDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.ArticleKeywordsResultDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.ArticleTextOptimizeDTO;
import com.alibaba.copilot.app.domain.seocopilot.request.ArticleGenerateGatewayRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.ArticleOptimizeRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.KeywordsOptimizeRequest;
import com.alibaba.copilot.boot.shopify.web.data.product.ShopifyProduct;

import java.util.List;

public interface ArticleOptimizeGateway {

    /**
     * 根据blog文章，提供优化keywords
     *
     * @param request
     * @return
     */
    ArticleKeywordsResultDTO optimizeKeywords(KeywordsOptimizeRequest request);

    /**
     * 优化文章文本内容
     *
     * @param request
     * @return
     */
    ArticleTextOptimizeDTO optimizeText(ArticleOptimizeRequest request);

    /**
     * 为文章生成图片
     *
     * @param request
     * @param count
     * @return
     */
    List<String> generateImages(ArticleOptimizeRequest request, int count);

    /**
     * 生成一篇新的文章
     *
     * @param request
     * @return
     */
    ArticleGenerateDTO generateArticle(ArticleGenerateGatewayRequest request);

    /**
     * 查询推荐关键词
     * @return
     */
    List<String> recommendKeywords(String query, int count);
}
