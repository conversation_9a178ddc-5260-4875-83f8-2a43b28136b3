package com.alibaba.copilot.app.domain.aiIistmaster.diamond.spring;

import java.io.ByteArrayInputStream;
import java.lang.reflect.Field;
import java.nio.charset.Charset;
import java.nio.charset.CharsetEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;
import java.util.concurrent.Executor;

import com.alibaba.copilot.app.domain.aiIistmaster.diamond.annotation.DiamondValue;
import com.alibaba.copilot.app.domain.aiIistmaster.diamond.annotation.DiamondKey;
import com.alibaba.copilot.app.domain.aiIistmaster.utils.ReflectionUtils;

import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListener;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.support.AopUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.env.CompositePropertySource;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MutablePropertySources;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @date 2023/8/10 11:40 AM
 */
@Component
public class ValuePostProcessor implements BeanPostProcessor, ApplicationContextAware {
    private static final Logger log = LoggerFactory.getLogger(ValuePostProcessor.class);
    private static String DIAMOND_PROPERTIES = "diamondProperties";
    private static ApplicationContext applicationContext;
    private static Map<String, List<ValueHolder>> holderMap = new HashMap();

    public ValuePostProcessor() {
    }

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        Class<?> cls = AopUtils.getTargetClass(bean);
        List<Field> fields = ReflectionUtils.getDeclaredFields(cls);
        Iterator var5 = fields.iterator();

        while(var5.hasNext()) {
            final Field field = (Field)var5.next();
            String key;
            if (field.isAnnotationPresent(DiamondValue.class)) {
                field.setAccessible(true);

                try {
                    DiamondValue diamondValue = (DiamondValue)field.getAnnotation(DiamondValue.class);
                    key = diamondValue.value();
                    List<ValueHolder> holders = (List)holderMap.get(key);
                    if (holders == null) {
                        holders = new ArrayList();
                        holderMap.put(key, holders);
                    }

                    ValueHolder holder = new ValueHolder(bean, diamondValue, field, key);
                    ((List)holders).add(holder);
                    holder.injectValue();
                } catch (Exception var13) {
                    log.error("inject value error {}", var13.getMessage());
                }
            } else if (field.isAnnotationPresent(DiamondKey.class)) {
                field.setAccessible(true);

                try {
                    DiamondKey service = (DiamondKey)field.getAnnotation(DiamondKey.class);
                    key = service.value();
                    String groupId = service.group();
                    final Object targetBean = ReflectionUtils.getProxyTarget(bean);
                    Diamond.addListener(key, groupId, new ManagerListener() {
                        @Override
                        public Executor getExecutor() {
                            return null;
                        }

                        @Override
                        public void receiveConfigInfo(String configInfo) {
                            try {
                                Object convertValue = ReflectionUtils.parseFieldObject(configInfo, field);
                                field.set(targetBean, convertValue);
                            } catch (Exception var3) {
                                ValuePostProcessor.log.error("fail to set field", var3);
                            }

                        }
                    });
                    String strVal = Diamond.getConfig(key, groupId, 10000L);
                    Object convertValue = ReflectionUtils.parseFieldObject(strVal, field);
                    field.set(targetBean, convertValue);
                } catch (Exception var14) {
                    throw new RuntimeException("inject  key error", var14);
                }
            }
        }

        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    private PropertiesPropertySource extractPropertiesPropertySource(PropertySource propertySource) {
        if (propertySource instanceof PropertiesPropertySource) {
            return (PropertiesPropertySource)propertySource;
        } else if (propertySource instanceof CompositePropertySource) {
            CompositePropertySource compositePropertySource = (CompositePropertySource)propertySource;
            propertySource = (PropertySource)compositePropertySource.getPropertySources().stream().findFirst().orElse((PropertySource<?>)null);
            return this.extractPropertiesPropertySource(propertySource);
        } else {
            return null;
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        applicationContext = context;
        this.addDiamondListener();
    }

    private void addDiamondListener() {
        ConfigurableEnvironment environment = (ConfigurableEnvironment)applicationContext.getEnvironment();
        CompositePropertySource composite = getDiamondPropertySource(environment);
        if (composite != null) {
            Iterator var3 = composite.getPropertySources().iterator();

            while(var3.hasNext()) {
                PropertySource propertySource = (PropertySource)var3.next();
                PropertiesPropertySource propertiesPropertySource = this.extractPropertiesPropertySource(propertySource);
                if (propertiesPropertySource != null) {
                    String name = propertySource.getName();
                    if (StringUtils.isNotBlank(name)) {
                        String[] parts = name.split("/");
                        String dataId = parts[2];
                        String groupId = parts[1];
                        Map<String, Object> source = (Map)propertiesPropertySource.getSource();
                        List<ManagerListener> listeners = Diamond.getListeners(dataId, groupId);
                        if (CollectionUtils.isEmpty(listeners)) {
                            Optional<ManagerListener> listener = listeners.stream().filter((l) -> {
                                return l instanceof ValueListener;
                            }).findFirst();
                            if (!listener.isPresent()) {
                                Diamond.addListener(dataId, groupId, new ValueListener(source));
                            }
                        }
                    }
                }
            }
        }

    }

    public static CompositePropertySource getDiamondPropertySource(ConfigurableEnvironment environment) {
        MutablePropertySources propertySources = environment.getPropertySources();
        PropertySource<?> propertySource = propertySources.get(DIAMOND_PROPERTIES);
        CompositePropertySource composite = (CompositePropertySource)propertySource;
        return composite;
    }

    public static String encode(String unicodeString) {
        CharsetEncoder asciiEncoder = Charset.forName("US-ASCII").newEncoder();
        StringBuilder result = new StringBuilder();
        char[] var3 = unicodeString.toCharArray();
        int var4 = var3.length;

        for(int var5 = 0; var5 < var4; ++var5) {
            Character character = var3[var5];
            if (asciiEncoder.canEncode(character)) {
                result.append(character);
            } else {
                result.append("\\u");
                result.append(Integer.toHexString(65536 | character).substring(1).toUpperCase());
            }
        }

        return result.toString();
    }

    public static class ValueHolder {
        private Object bean;
        private Field field;
        private String key;
        private DiamondValue value;

        public ValueHolder(Object bean, DiamondValue service, Field field, String key) throws Exception {
            this.bean = ReflectionUtils.getProxyTarget(bean);
            this.field = field;
            if (key.startsWith(service.startToken()) && key.endsWith(service.endToken())) {
                this.key = key;
            } else {
                this.key = service.startToken() + key + service.endToken();
            }

            this.value = service;
        }

        public void injectValue() throws Exception {
            DefaultListableBeanFactory beanFactory = (DefaultListableBeanFactory)ValuePostProcessor.applicationContext.getAutowireCapableBeanFactory();
            String strVal = beanFactory.resolveEmbeddedValue(this.key);
            if (this.value.blankIsNull() && StringUtils.isBlank(strVal)) {
                this.field.set(this.bean, (Object)null);
            } else {
                Object convertValue = ReflectionUtils.parseFieldObject(strVal, this.field);
                this.field.set(this.bean, convertValue);
            }

        }

        public Object getBean() {
            return this.bean;
        }

        public Field getField() {
            return this.field;
        }

        public String getKey() {
            return this.key;
        }

        public DiamondValue getValue() {
            return this.value;
        }

        public void setBean(Object bean) {
            this.bean = bean;
        }

        public void setField(Field field) {
            this.field = field;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public void setValue(DiamondValue value) {
            this.value = value;
        }

        @Override
        public boolean equals(Object o) {
            if (o == this) {
                return true;
            } else if (!(o instanceof ValueHolder)) {
                return false;
            } else {
                ValueHolder other = (ValueHolder)o;
                if (!other.canEqual(this)) {
                    return false;
                } else {
                    label59: {
                        Object this$bean = this.getBean();
                        Object other$bean = other.getBean();
                        if (this$bean == null) {
                            if (other$bean == null) {
                                break label59;
                            }
                        } else if (this$bean.equals(other$bean)) {
                            break label59;
                        }

                        return false;
                    }

                    Object this$field = this.getField();
                    Object other$field = other.getField();
                    if (this$field == null) {
                        if (other$field != null) {
                            return false;
                        }
                    } else if (!this$field.equals(other$field)) {
                        return false;
                    }

                    Object this$key = this.getKey();
                    Object other$key = other.getKey();
                    if (this$key == null) {
                        if (other$key != null) {
                            return false;
                        }
                    } else if (!this$key.equals(other$key)) {
                        return false;
                    }

                    Object this$value = this.getValue();
                    Object other$value = other.getValue();
                    if (this$value == null) {
                        if (other$value != null) {
                            return false;
                        }
                    } else if (!this$value.equals(other$value)) {
                        return false;
                    }

                    return true;
                }
            }
        }

        protected boolean canEqual(Object other) {
            return other instanceof ValueHolder;
        }



        @Override
        public String toString() {
            return "ValuePostProcessor.ValueHolder(bean=" + this.getBean() + ", field=" + this.getField() + ", key=" + this.getKey() + ", value=" + this.getValue() + ")";
        }
    }

    public static class ValueListener implements ManagerListener {
        Map<String, Object> source;

        public ValueListener(Map<String, Object> source) {
            this.source = source;
        }

        @Override
        public Executor getExecutor() {
            return null;
        }

        @Override
        public void receiveConfigInfo(String content) {
            try {
                Properties properties = new Properties();
                properties.load(new ByteArrayInputStream(ValuePostProcessor.encode(content).getBytes()));
                Iterator var3 = properties.keySet().iterator();

                while(true) {
                    String strKey;
                    Object oldValue;
                    Object value;
                    do {
                        if (!var3.hasNext()) {
                            return;
                        }

                        Object key = var3.next();
                        strKey = key.toString();
                        oldValue = this.source.get(strKey);
                        value = properties.get(key);
                    } while(oldValue != null && oldValue.equals(value));

                    this.source.put(strKey, value);
                    String holderKey = "${" + strKey + "}";
                    List<ValueHolder> valueHolders = (List)ValuePostProcessor.holderMap.get(holderKey);
                    if (valueHolders == null) {
                        valueHolders = (List)ValuePostProcessor.holderMap.get(strKey);
                    }

                    if (valueHolders != null) {
                        String finalStrKey = strKey;
                        Object finalOldValue = oldValue;
                        Object finalValue = value;
                        valueHolders.stream().forEach((item) -> {
                            try {
                                item.injectValue();
                            } catch (Exception var5) {
                                ValuePostProcessor.log.error("attribute {} changed, old={}, new={}, err={}", new Object[]{finalStrKey, finalOldValue, finalValue, var5});
                            }

                        });
                    }
                }
            } catch (Exception var10) {
                ValuePostProcessor.log.error("ValuePostProcessor diamond listener error", var10);
            }
        }
    }
}