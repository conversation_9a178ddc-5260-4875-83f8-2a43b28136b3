package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.app.domain.seocopilot.constant.ArticleTypeEnum;
import com.alibaba.copilot.app.domain.seocopilot.constant.LanguageEnum;
import com.alibaba.copilot.app.domain.seocopilot.constant.SeoEntityStatus;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;

/**
 * SeoBlogArticle 实体
 */
@Slf4j
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class SeoBlogArticle {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 优化状态
     */
    private SeoEntityStatus status;

    /**
     * shopify店铺ID
     */
    private Long shopId;

    /**
     * 批次id
     */
    private String batchId;

    /**
     * seo_blog表店铺ID
     */
    private Long seoBlogId;

    /**
     * shopify文章ID
     */
    private Long shopifyArticleId;

    /**
     * 发布平台对应的ID
     */
    private String platformArticleId;

    /**
     * 文章评分
     */
    private BigDecimal score;

    /**
     * shopify文章更新时间
     */
    private Date updateAt;

    private String title;

    private String content;

    /**
     * shopify的blogId
     */
    private Long shopifyBlogId;

    private String handle;

    /**
     * 文章相关内容
     */
    private SeoBlogArticleAttributes attributes = new SeoBlogArticleAttributes();

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 文章语种
     */
    private LanguageEnum language;

    /**
     * 文章类型
     */
    private ArticleTypeEnum articleType;

    /**
     * 关联文章ID
     */
    private Long relatedArticleId;

    /**
     * graphql api id
     */
    private String adminGraphqlApiId;
}
