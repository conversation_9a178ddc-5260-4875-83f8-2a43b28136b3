package com.alibaba.copilot.app.domain.base.bizlog;

import com.alibaba.fastjson.JSON;
import com.taobao.eagleeye.EagleEye;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * .
 *
 * <AUTHOR>
 * @date 2023/08/10
 */
@Slf4j
public class BizLogMonitor {

    private final Logger bizLogger = LoggerFactory.getLogger("biz_monitor");

    private final static String LOG_DELIMITER = "@@@";

    private long startTime;

    private String logType;
    private Boolean success;
    private String errorMsg;


    private BizLogMonitor() {

    }


    public static BizLogMonitor of(BizLogTypeEnum type) {
        BizLogMonitor monitor = new BizLogMonitor();
        monitor.logType = type.getCode();
        monitor.startTime = System.currentTimeMillis();
        monitor.success = Boolean.TRUE;
        monitor.errorMsg = "OK";
        return monitor;
    }

    public static BizLogMonitor of(String type) {
        BizLogMonitor monitor = new BizLogMonitor();
        monitor.logType = type;
        monitor.startTime = System.currentTimeMillis();
        monitor.success = Boolean.TRUE;
        monitor.errorMsg = "";
        return monitor;
    }


    public void error(String errorMsg) {
        this.success = false;
        this.errorMsg = errorMsg;
    }


    public void recordLog(Object... params) {
        try {
            internalRecordLog(params);
        } catch (Throwable e) {
            log.error("record log error", e);
        }
    }

    private void internalRecordLog(Object... params) {
        List<String> logList = new ArrayList<>();

        addValue(logList, EagleEye.getTraceId());
        addValue(logList, logType);
        addValue(logList, success);
        addValue(logList, errorMsg);
        addValue(logList, System.currentTimeMillis() - startTime);

        if (Objects.nonNull(params)) {
            for (Object param : params) {
                addValue(logList, param);
            }
        }

        String logStr = String.join(LOG_DELIMITER, logList) + LOG_DELIMITER;
        bizLogger.warn(logStr);
    }

    private void addValue(List<String> list, Object value) {
        String valueStr;
        if (Objects.nonNull(value)) {
            if (isBasicObjectType(value)) {
                valueStr = String.valueOf(value);
            } else {
                valueStr = JSON.toJSONString(value);
            }
        } else {
            valueStr = "-";
        }
        list.add(valueStr);
    }


    private boolean isBasicObjectType(Object value) {
        if (Objects.isNull(value)) {
            return false;
        }
        return value.getClass().isPrimitive()
                || value instanceof String
                || value instanceof Boolean
                || value instanceof Integer
                || value instanceof Long
                || value instanceof Float
                || value instanceof Double
                || value instanceof Byte;
    }

}