package com.alibaba.copilot.app.domain.rewrite.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RewriteDocument {
    private Long id;
    private Date gmtCreate;
    private Date gmtModified;
    private String outerUserId;
    private String title;
    private String source;
    private String type;
    private Boolean deleted;
    private String previewContent;
}
