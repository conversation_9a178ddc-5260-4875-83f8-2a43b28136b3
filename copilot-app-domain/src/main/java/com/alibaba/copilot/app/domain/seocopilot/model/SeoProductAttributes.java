package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.boot.basic.data.Attributes;

import java.util.ArrayList;
import java.util.List;

/**
 * SeoProductAttributes
 */
public class SeoProductAttributes extends Attributes {

    public static final String KEYWORDS = "keywords";

    public static final String SHOWKEYWORDS = "showKeywords";

    public static final String CHECK_TASK_ID = "checkTaskId";

    public static final String SEO_HANDLE_UPDATED = "seoHandleUpdated";

    public static final String SEO_HANDLE_APPLIED = "seoHandleApplied";

    public SeoProductAttributes(String json) {
        super(json);
    }

    public static final String SHOPIFY_PRODUCT_URL = "shopifyProductUrl";

    public void setShopifyProductUrl(String shopifyUrl) {
        put(SHOPIFY_PRODUCT_URL, shopifyUrl);
    }

    public String getShopifyProductUrl() {
        return getAsString(SHOPIFY_PRODUCT_URL);
    }

    public void setKeywords(List<String> keywords) {
        put(KEYWORDS, keywords);
    }

    public List<String> getKeywords() {
        if (!containsKey(KEYWORDS)) {
            return new ArrayList<>();
        }
        return get(KEYWORDS, List.class);
    }

    public boolean getShowKeywords() {
        if (!containsKey(SHOWKEYWORDS)) {
            return false;
        }
        return get(SHOWKEYWORDS, Boolean.class);
    }

    public void setShowKeywords(boolean show) {
        put(SHOWKEYWORDS, show);
    }

    public void setCheckTaskId(Long checkTaskId) {
        put(CHECK_TASK_ID, checkTaskId);
    }

    public Long getCheckTaskId() {
        if (!containsKey(CHECK_TASK_ID)) {
            return null;
        }
        return getAsLong(CHECK_TASK_ID);
    }
}
