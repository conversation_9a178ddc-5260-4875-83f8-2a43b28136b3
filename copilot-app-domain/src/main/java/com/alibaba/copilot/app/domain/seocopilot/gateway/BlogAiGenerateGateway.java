package com.alibaba.copilot.app.domain.seocopilot.gateway;

import com.alibaba.copilot.app.domain.seocopilot.model.AlgorithmGenerateConfig;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShop;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.llm.openai.data.completion.chat.ChatMessage;
import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * <AUTHOR>
 * @des Blog多轮交互AI生成调用
 */
public interface BlogAiGenerateGateway {


    /**
     * Blog多轮交互 - AI生成
     *
     * @param seoShop
     * @param prompt
     * @param algorithmGenerateConfig
     * @return
     */
    SingleResult<JSONObject> blogGenFromAi(SeoShop seoShop, String prompt, AlgorithmGenerateConfig algorithmGenerateConfig);


    /**
     * Blog多轮交互 - AI生成
     * @param messages
     * @return
     */
    SingleResult<JSONObject> blogGenFromAiWithMessages(List<ChatMessage> messages);
}
