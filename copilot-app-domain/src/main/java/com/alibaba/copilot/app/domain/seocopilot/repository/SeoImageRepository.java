package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.domain.seocopilot.model.SeoImage;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoWebPage;

import java.util.List;

public interface SeoImageRepository {

    List<SeoImage> getByShopId(Long shopId);

    Integer save(SeoImage seoImage);

    List<SeoImage> getByShopifyImageIdAndUrl(Long shopId, String shopifyImageId, String shopifyImageUrl);

    Integer removeImage(Long id);
}
