package com.alibaba.copilot.app.domain.seocopilot.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SeoGoogleAnalyticsData {
    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * shopify店铺ID
     */
    private Long shopId;

    /**
     * 分区（天）
     */
    private String ds;

    /**
     * pageUrl  all 表示全站数据
     */
    private String pageUrl;

    /**
     * 活跃用户数
     */
    private Long users;
    /**
     * pageIndexRate
     */
    private BigDecimal pageIndexRate;
    /**
     * 新用户数
     */
    private Long newUsers;

    /**
     * 点击数
     */
    private Long views;

    /**
     * 会话数
     */
    private Long sessions;

    /**
     * 点击数
     */
    private Long clicks;
    /**
     * 曝光量
     */
    private Long impressions;
    /**
     * ctr
     */
    private BigDecimal ctr;
    /**
     * position
     */
    private BigDecimal position;
    /**
     * 停留总时长 停留总时长/活跃用户数=平均停留时长
     */
    private BigDecimal userEngagementDuration;

    /**
     * 跳失率 会话数*跳失率=跳失次数
     */
    private BigDecimal bounceRate;
    /**
     * 感兴趣话题占比
     */
    private BigDecimal engagementRate;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 扩展字段
     */
    private SeoGoogleAnalyticsDataAttributes attributes = new SeoGoogleAnalyticsDataAttributes(null);

    public static SeoGoogleAnalyticsData defaultInstance(){
        SeoGoogleAnalyticsData defaultInstance = new SeoGoogleAnalyticsData();
        defaultInstance.setShopId(0L);
        defaultInstance.setPageUrl("all");
        defaultInstance.setDs("");
        defaultInstance.setUsers(0L);
        defaultInstance.setNewUsers(0L);
        defaultInstance.setViews(0L);
        defaultInstance.setSessions(0L);
        defaultInstance.setClicks(0L);
        defaultInstance.setImpressions(0L);
        defaultInstance.setCtr(new BigDecimal(0));
        defaultInstance.setPosition(new BigDecimal(0));
        defaultInstance.setUserEngagementDuration(new BigDecimal(0));
        defaultInstance.setBounceRate(new BigDecimal(0));
        defaultInstance.setDeleted(false);
        defaultInstance.setEngagementRate(BigDecimal.valueOf(0));
        defaultInstance.setAttributes(new SeoGoogleAnalyticsDataAttributes(null));
        return defaultInstance;
    }
}
