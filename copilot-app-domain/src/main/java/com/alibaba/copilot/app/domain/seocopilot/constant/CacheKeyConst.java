package com.alibaba.copilot.app.domain.seocopilot.constant;

public class CacheKeyConst {

    public static String getPhraseQuestionsCacheKey(Long seoShopId) {
        return "seo:phrase:questions:" + seoShopId;
    }

    public static String getFinishedAnalysisStepCacheKey(Long seoShopId) {
        return "seo:shop:first:" + seoShopId;
    }

    public static String getFirstBlogKey(Long seoShopId) {
        return "seo:shop:first:blog" + seoShopId;
    }

    public static String getFinishedSeoKitStepCacheKey(Long seoShopId) {
        return "seo:shop:finishedSeoKitStep:" + seoShopId;
    }

    public static String getNoNeedSecondGuideCacheKey(Long seoShopId) {
        return "seo:shop:noNeedSecondGuide:" + seoShopId;
    }

    public static String getNoNeedUpdateSeoScoreCacheKey(Long seoShopId) {
        return "seo:shop:noNeedUpdateSeoScore:" + seoShopId;
    }

    public static String getSubscribeIpCacheKey(Long subscribeOrderId) {
        return "seo:shop:subscription:ip" + subscribeOrderId;
    }

    public static String getUserAppliedDiscount(String userId, String discountCode) {
        return "rewrite:discount:user:" + discountCode + userId;
    }
}
