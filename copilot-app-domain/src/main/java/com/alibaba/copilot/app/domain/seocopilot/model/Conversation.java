package com.alibaba.copilot.app.domain.seocopilot.model;

import lombok.*;

import java.util.Date;

@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class Conversation {
    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 标题
     */
    private String title;

    /**
     * shopId
     */
    private Long shopId;

    /**
     * 业务类型
     */
    private String businessType;

    private String businessId;


    /**
     * 逻辑删除
     */
    private Boolean deleted;
}
