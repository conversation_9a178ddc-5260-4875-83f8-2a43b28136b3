package com.alibaba.copilot.app.domain.aiIistmaster.enums;

import lombok.Getter;

/**
 * @ClassName GenerateTaskType
 * <AUTHOR>
 * @Date 2023/8/21 19:52
 */
@Getter
public enum GenerateTaskTypeEnum {

    /**
     * 更换背景
     */
    UPDATE_BACKGROUND(100, "UPDATE_BACKGROUND"),

    /**
     * 抠图
     */
    UPDATE_MATTING(101, "UPDATE_MATTING"),

    /**
     * 去水印
     */
    REMOVE_WATERMARK(102, "REMOVE_WATERMARK"),

    /**
     * 文本优化-标题
     */
    TEXT_OPTIMIZATION_TITLE(103, "TEXT_OPTIMIZATION_TITLE"),
    /**
     * 文本优化-短卖点
     */
    SHORT_OPTIMIZATION_SELL_POINT(104, "SHORT_OPTIMIZATION_SELL_POINT"),
    /**
     * 文本优化-描述
     */
    TEXT_OPTIMIZATION_DESCRIPTION(105, "TEXT_OPTIMIZATION_DESCRIPTION");

    private Integer code;
    private String type;

    GenerateTaskTypeEnum(Integer code, String type) {
        this.code = code;
        this.type = type;
    }
}
