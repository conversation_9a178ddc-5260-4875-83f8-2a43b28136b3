package com.alibaba.copilot.app.domain.seocopilot.model;


import com.alibaba.copilot.app.domain.seocopilot.constant.SeoOptimizingType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * seo_optimize_proposal
 *
 * <AUTHOR>
 */
@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class SeoOptimizeProposal {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * seo copilot店铺ID
     */
    private Long shopId;

    /**
     * batch_id
     */
    private String batchId;

    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 关联诊断对象ID
     */
    private Long entityId;

    /**
     * seo_check_list表主键ID
     */
    private Long checkRecordId;

    /**
     * 优化类型（手工编辑/AI自动生成/优化建议）
     */
    private SeoOptimizingType optimizingType;

    /**
     * 生成的内容
     */
    private String optimizingContent;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 扩展字段
     */
    private SeoOptimizeProposalAttributes attributes = new SeoOptimizeProposalAttributes("{}");

}
