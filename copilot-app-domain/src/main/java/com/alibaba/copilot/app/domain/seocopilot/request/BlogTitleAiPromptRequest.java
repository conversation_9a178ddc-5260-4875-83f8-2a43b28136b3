package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.copilot.app.domain.seocopilot.constant.PromptVersionEnum;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @des Blog Title Ai Prompt
 */
@Data
@Builder
public class BlogTitleAiPromptRequest {

    /**
     * scene
     * Enum: KEYWORD\TOPIC\PRODUCT\PRODUCT_RANK\BRAND_COMPARISON\CATEGORY_COMPARISON
     * com.alibaba.copilot.app.domain.seocopilot.constant.SeoContentGenSceneEnum
     */
    private String scene;

    /**
     * 话题
     */
    private String topic;

    /**
     * 关键词
     * 格式：a,b,c,d
     */
    private String keywords;

    /**
     * style
     */
    private String style;

    /**
     * blogCategory
     */
    private String blogCategory;

    /**
     * link
     */
    private String link;

    /**
     * country
     */
    private String country;

    /**
     * retrievalText
     */
    private String retrievalText;

    /**
     * prompt version
     */
    private PromptVersionEnum promptVersion;

    /**
     * language
     */
    private String language;

    /**
     * currentYear
     */
    private String currentYear;

    /**
     * productInformation
     */
    private String productInformation;

    /**
     * productCount
     */
    private String productCount;

    /**
     * comparisonInformation
     */
    private String comparisonInformation;

    /**
     * tendency
     */
    private String tendency;

    private Long articleId;
}
