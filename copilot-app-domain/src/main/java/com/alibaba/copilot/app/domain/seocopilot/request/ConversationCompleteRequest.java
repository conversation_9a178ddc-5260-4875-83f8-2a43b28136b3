package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.copilot.app.client.seocopilot.constant.ChatMessageRoleEnum;
import com.alibaba.copilot.boot.llm.openai.data.completion.ResponseFormat;
import lombok.Data;

@Data
public class ConversationCompleteRequest {
    private Long shopId;
    private String businessType;
    private String businessId;
    private ChatMessageRoleEnum role;
    private String content;
    private ResponseFormat responseFormat;
}
