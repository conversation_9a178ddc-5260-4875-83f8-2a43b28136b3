package com.alibaba.copilot.app.domain.conversation.service;

import com.alibaba.copilot.app.domain.conversation.request.ConversationLog;
import com.alibaba.copilot.app.domain.seocopilot.repository.ConversationLogRepository;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class ConversationLogService {
    @Resource
    ConversationLogRepository conversationLogRepository;

    @Async
    public void logUsage(ConversationLog log) {
        conversationLogRepository.save(log);
    }
}
