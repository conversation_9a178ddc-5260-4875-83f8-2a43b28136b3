package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FireCrawSinglePageResponseDTO {
    private Boolean success;
    private Data data;


    @lombok.Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Data {
        private String markdown;
        private String content;
        private String html;
        private String rawHtml;
        private Metadata metadata;

        @J<PERSON><PERSON><PERSON>(name = "llm_extraction")
        private Map<String, Object> llmExtraction;
        private String warning;
    }

    @lombok.Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Metadata {
        private String title;
        private String description;
        private String language;
        private String sourceURL;

        @J<PERSON><PERSON>ield(name = "any_other_metadata")
        private Map<String, String> anyOtherMetadata;
        private int pageStatusCode;
        private String pageError;

    }
}
