package com.alibaba.copilot.app.domain.seocopilot.gateway;

import com.alibaba.copilot.app.domain.seocopilot.dto.ShopLocalesDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.TranslatableResourcesByIdsDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.TranslationsRegisterDTO;
import com.alibaba.copilot.app.domain.seocopilot.request.TranslationsRegisterRequest;
import com.alibaba.copilot.boot.shopify.web.data.asset.PutAssetRequest;
import com.alibaba.copilot.boot.shopify.web.data.asset.ShopifyAsset;
import com.alibaba.copilot.boot.shopify.web.data.blog.*;
import com.alibaba.copilot.boot.shopify.web.data.graphql.file.ImageFileQueryGraphQLQuery;
import com.alibaba.copilot.boot.shopify.web.data.graphql.file.ImageFileQueryGraphQLResult;
import com.alibaba.copilot.boot.shopify.web.data.graphql.file.ImageFileUpdateGraphQLMutation;
import com.alibaba.copilot.boot.shopify.web.data.graphql.file.ImageFileUpdateGraphQLResult;
import com.alibaba.copilot.boot.shopify.web.data.metafield.MetaFieldCreateRequest;
import com.alibaba.copilot.boot.shopify.web.data.metafield.MetaFieldUpdateRequest;
import com.alibaba.copilot.boot.shopify.web.data.metafield.ShopifyMetaField;
import com.alibaba.copilot.boot.shopify.web.data.metafield.ShopifyMetaFieldsResponse;
import com.alibaba.copilot.boot.shopify.web.data.oauth.ShopifyAccessScopeResponse;
import com.alibaba.copilot.boot.shopify.web.data.page.ShopifyPage;
import com.alibaba.copilot.boot.shopify.web.data.product.*;
import com.alibaba.copilot.boot.shopify.web.data.shop.ShopifyShop;
import com.alibaba.copilot.boot.shopify.web.data.store.StoreRedirect;
import com.alibaba.copilot.boot.shopify.web.data.theme.ShopifyTheme;

import java.util.List;
import java.util.Map;

/**
 * Shopify 服务
 */
public interface ShopifyGateway {

    /**
     * 获取主题列表（Retrieves a list of themes）
     *
     * @param shopDomain
     * @param accessToken
     * @return
     */
    List<ShopifyTheme> getThemes(String shopDomain, String accessToken);

    /**
     * 获取主题下的一个资产（Retrieves a single asset for a theme）
     *
     * @param shopDomain
     * @param accessToken
     * @param themeId
     * @param assetKey
     * @return
     */
    ShopifyAsset getAsset(String shopDomain, String accessToken, Long themeId, String assetKey);

    /**
     * 创建或修改主题
     *
     * @param shopDomain
     * @param accessToken
     * @param themeId
     * @param putAssetRequest
     * @return
     */
    ShopifyAsset putAsset(String shopDomain, String accessToken, Long themeId, PutAssetRequest putAssetRequest);

    /**
     * 删除Asset
     */
    String deleteAsset(String shopDomain, String accessToken, Long themeId, String assetKey);

    /**
     * 创建blog
     *
     * @param shopDomain
     * @param accessToken
     * @param createRequest
     * @return
     */
    ShopifyBlog createBlog(String shopDomain, String accessToken, BlogCreateRequest createRequest);

    /**
     * 查询blog列表
     *
     * @param shopDomain
     * @param accessToken
     * @return
     */
    List<ShopifyBlog> getBlogs(String shopDomain, String accessToken);

    /**
     * 查询blog列表
     *
     * @param shopDomain
     * @param accessToken
     * @return
     */
    List<ShopifyBlog> getBlogs(String shopDomain, String accessToken, Map<String, Object> parameters);

    /**
     * 创建文章
     *
     * @param shopDomain
     * @param accessToken
     * @param blogId
     * @param createRequest
     * @return
     */
    ShopifyArticle createArticle(String shopDomain, String accessToken, Long blogId, ArticleCreateRequest createRequest);

    /**
     * 创建文章metaFields
     *
     * @param shopDomain
     * @param accessToken
     * @param createRequest
     * @return
     */
    ShopifyMetaField createArticleMetaField(String shopDomain, String accessToken, Long articleId, MetaFieldCreateRequest createRequest);

    /**
     * 查询单个article
     *
     * @param shopDomain
     * @param accessToken
     * @param blogId
     * @param articleId
     * @return
     */
    ShopifyArticle getArticle(String shopDomain, String accessToken, Long blogId, Long articleId);

    /**
     * 查询article列表
     *
     * @param shopDomain
     * @param accessToken
     * @param blogId
     * @return
     */
    List<ShopifyArticle> getArticles(String shopDomain, String accessToken, Long blogId);

    /**
     * 查询article列表
     *
     * @param shopDomain
     * @param accessToken
     * @param blogId
     * @param parameters
     * @return
     */
    List<ShopifyArticle> getArticles(String shopDomain, String accessToken, Long blogId, Map<String, Object> parameters);

    /**
     * 查询文章metaFields
     *
     * @param shopDomain
     * @param accessToken
     * @param articleId
     * @return
     */
    List<ShopifyMetaField> getArticleMetaFields(String shopDomain, String accessToken, Long articleId);

    /**
     * 更新文章
     *
     * @param shopDomain
     * @param accessToken
     * @param blogId
     * @param articleId
     * @param updateRequest
     * @return
     */
    ShopifyArticle updateArticle(String shopDomain, String accessToken, Long blogId, Long articleId, ArticleUpdateRequest updateRequest);

    /**
     * 更新文章metaFields
     *
     * @param shopDomain
     * @param accessToken
     * @param articleId
     * @param metaFieldId
     * @param updateRequest
     * @return
     */
    ShopifyMetaField updateArticleMetaField(String shopDomain, String accessToken, Long articleId, Long metaFieldId, MetaFieldUpdateRequest updateRequest);

    /**
     * 删除文章指定metaField
     *
     * @param shopDomain
     * @param accessToken
     * @param articleId
     * @param metaFieldId
     */
    void deleteArticleMetaField(String shopDomain, String accessToken, Long articleId, Long metaFieldId);

    /**
     * 设置product metaField
     *
     * @param shopDomain
     * @param accessToken
     * @param productId
     * @param request
     * @return
     */
    ShopifyMetaField createProductMetaField(String shopDomain, String accessToken, Long productId, MetaFieldCreateRequest request);

    /**
     * 查询product列表
     *
     * @param shopDomain
     * @param accessToken
     * @return
     */
    List<ShopifyProduct> getProducts(String shopDomain, String accessToken);

    /**
     * 查询page列表
     *
     * @param shopDomain
     * @param accessToken
     * @return
     */
    List<ShopifyPage> getPages(String shopDomain, String accessToken);

    /**
     * 根据shopifyProductId查询product
     *
     * @param shopDomain
     * @param accessToken
     * @param shopifyProductId
     * @return
     */
    ShopifyProduct getProduct(String shopDomain, String accessToken, Long shopifyProductId);

    /**
     * 获取商品列表
     *
     * @param shopDomain
     * @param accessToken
     * @param parameter
     * @return
     */
    List<ShopifyProduct> getProductsByParameter(String shopDomain, String accessToken, Map<String, Object> parameter);

    /**
     * 获取product metaField
     *
     * @param shopDomain
     * @param accessToken
     * @param productId
     * @return
     */
    List<ShopifyMetaField> getProductMetaFields(String shopDomain, String accessToken, Long productId);

    /**
     * 查询product image列表
     *
     * @param shopDomain
     * @param accessToken
     * @param productId
     * @return
     */
    List<ShopifyProductImage> getProductImages(String shopDomain, String accessToken, Long productId);

    /**
     * 查询collection列表
     *
     * @param shopDomain
     * @param accessToken
     * @param parameter
     * @return
     */
    List<ShopifyCollection> getCustomCollectionsByParameter(String shopDomain, String accessToken, Map<String, Object> parameter);

    /**
     * 查询collection列表
     *
     * @param shopDomain
     * @param accessToken
     * @return
     */
    List<ShopifyCollection> getCollections(String shopDomain, String accessToken, ShopifyCollectionsQuery shopifyCollectionsQuery);

    /**
     * 查询custom collection列表
     *
     * @param shopDomain
     * @param accessToken
     * @return
     */
    List<ShopifyCollection> getCustomCollections(String shopDomain, String accessToken, ShopifyCollectionsQuery shopifyCollectionsQuery);

    /**
     * 查询smart collection列表
     *
     * @param shopDomain
     * @param accessToken
     * @return
     */
    List<ShopifyCollection> getSmartCollections(String shopDomain, String accessToken, ShopifyCollectionsQuery shopifyCollectionsQuery);

    /**
     * 更新collection
     *
     * @param shopDomain
     * @param accessToken
     * @return
     */
    ShopifyCollection putCollection(String shopDomain, String accessToken, Long collectionId, PutCustomCollectionRequest putCustomCollectionRequest);

    /**
     * 更新collection
     *
     * @param shopDomain
     * @param accessToken
     * @return
     */
    ShopifyCollection putSmartCollection(String shopDomain, String accessToken, Long collectionId, PutSmartCollectionRequest putSmartCollectionRequest);


    /**
     * 更新product
     *
     * @param shopDomain
     * @param accessToken
     * @return
     */
    ShopifyProduct putProduct(String shopDomain, String accessToken, Long productId, PutProductRequest putProductRequest);

    /**
     * 获取店铺信息
     *
     * @param shopDomain
     * @param accessToken
     * @return
     */
    ShopifyShop getShop(String shopDomain, String accessToken);

    /**
     * 获取店铺图片信息
     *
     * @param shopDomain
     * @param accessToken
     * @return
     */
    ImageFileQueryGraphQLResult getImages(String shopDomain, String accessToken, ImageFileQueryGraphQLQuery imageFileQueryGraphQLQuery);

    /**
     * 更新店铺图片信息
     *
     * @param shopDomain
     * @param accessToken
     * @return
     */
    ImageFileUpdateGraphQLResult updateImage(String shopDomain, String accessToken, ImageFileUpdateGraphQLMutation imageFileUpdateGraphQLMutation);

    /**
     * 创建Shopify Metafeild
     *
     * @param shopDomain
     * @param accessToken
     * @param request
     * @return
     */
    ShopifyMetaFieldsResponse createShopMetaFeild(String shopDomain, String accessToken, MetaFieldCreateRequest request);

    /**
     * 获取collection的metafield
     *
     * @param shopDomain
     * @param accessToken
     * @param collectionId
     * @return
     */
    List<ShopifyMetaField> getCollectionMetaFields(String shopDomain, String accessToken, Long collectionId);

    /**
     * 创建collection的metafield
     *
     * @param shopDomain
     * @param accessToken
     * @param collectionId
     * @param request
     * @return
     */
    ShopifyMetaField createCollectionMetaField(String shopDomain, String accessToken, Long collectionId, MetaFieldCreateRequest request);

    /**
     * 删除文章
     *
     * @param shopDomain
     * @param accessToken
     * @param blogId
     * @param articleId
     * @return
     */
    Boolean deleteArticle(String shopDomain, String accessToken, Long blogId, Long articleId);

    /**
     * 删除product metaField
     *
     * @param shopDomain
     * @param accessToken
     * @param productId
     * @param metaFieldId
     * @return
     */
    void deleteProductMetaField(String shopDomain, String accessToken, Long productId, Long metaFieldId);


    /**
     * 删除collection metaField
     *
     * @param shopDomain
     * @param accessToken
     * @param collectionId
     * @param metaFieldId
     * @return
     */
    void deleteCollectionMetaField(String shopDomain, String accessToken, Long collectionId, Long metaFieldId);

    /**
     * 重定向
     *
     * @param shopDomain
     * @param accessToken
     * @param source
     * @param target
     * @return
     */
    StoreRedirect createRedirect(String shopDomain, String accessToken, String source, String target);

    /**
     * 获取店铺支持的所有语种
     */
    ShopLocalesDTO shopLocales(String shopDomain, String accessToken);

    /**
     * 翻译版本注册
     */
    TranslationsRegisterDTO translationsRegister(String shopDomain, String accessToken, TranslationsRegisterRequest request);

    /**
     * 根据ID获取翻译资源
     */
    TranslatableResourcesByIdsDTO translatableResourcesByIds(String shopDomain, String accessToken, List<String> resourceIds);

    /**
     * 获取权限列表
     */
    ShopifyAccessScopeResponse accessScopes(String shopDomain, String accessToken);
}
