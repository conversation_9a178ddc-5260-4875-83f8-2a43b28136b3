package com.alibaba.copilot.app.domain.seocopilot.dto;

import lombok.Data;
import com.alibaba.fastjson.annotation.JSONField;

/**
 * KeywordExtensionDataDTO
 *
 * <AUTHOR>
 * @date 2024/6/20 4:44 下午
 */
@Data
public class KeywordExtensionDataDTO {

    /**
     * keyword
     */
    private String keyword;

    /**
     * kd
     */
    private String kd;

    /**
     * searchVolume
     */
    @JSONField(name = "search_volume")
    private String searchVolume;

    /**
     * cpc
     */
    private String cpc;

    /**
     * intent
     */
    private String intent;

    /**
     * qualityScore
     */
    @JSONField(name = "quality_score")
    private String qualityScore;

    /**
     * position
     */
    private String position;

    /**
     * traffic
     */
    private String traffic;

    /**
     * ctr
     */
    private String ctr;

    /**
     * clicks
     */
    private String clicks;

    /**
     * impressions
     */
    private String impressions;
}
