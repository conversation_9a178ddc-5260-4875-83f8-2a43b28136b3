package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.domain.seocopilot.model.SeoShopSetting;

/**
 * SeoShopSettingRepository
 */
public interface SeoShopSettingRepository {

    /**
     * 保存Setting
     *
     * @param seoShopSetting
     * @return
     */
    Integer saveSeoShopSetting(SeoShopSetting seoShopSetting);

    /**
     * 根据ShopId获取Setting
     *
     * @param shopId
     * @return
     */
    SeoShopSetting getSeoShopSettingByShopId(Long shopId);

    /**
     * 逻辑删除
     * @param shopId
     * @return
     */
    Integer deleteSeoShopSettingByShopId(Long shopId);

    /**
     * 更新setting 语种设置
     */
    Integer updateSeoShopSettingLanguage(Long shopId, String language);

}
