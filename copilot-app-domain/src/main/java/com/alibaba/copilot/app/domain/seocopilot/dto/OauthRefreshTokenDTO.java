package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2023/9/25
 */
@Data
public class OauthRefreshTokenDTO {

    /**
     * access_token
     */
    @JsonProperty("access_token")
    private String accessToken;

    /**
     * expires_in (unit:s)
     */
    @JsonProperty("expires_in")
    private Integer expiresIn;

    /**
     * scope
     */
    @JsonProperty("scope")
    private String scope;

    /**
     * token_type
     */
    @JsonProperty("token_type")
    private String tokenType;

}
