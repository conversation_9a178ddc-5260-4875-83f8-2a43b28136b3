package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class ExtendKeywordInfo {

    private String keyword;

    private String kd;

    @J<PERSON><PERSON>ield(name = "search_volume")
    private String searchVolume;

    private String cpc;

    private String intent;

    @J<PERSON>NField(name = "quality_score")
    private String qualityScore;

    private String position;

    private String traffic;

    private String ctr;

    private String clicks;

    private String impressions;
}
