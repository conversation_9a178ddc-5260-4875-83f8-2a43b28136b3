package com.alibaba.copilot.app.domain.n8n.model;

import com.alibaba.copilot.app.domain.n8n.enums.FlowSceneEum;
import lombok.Data;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * .
 *
 * <AUTHOR>
 * @date 2024/05/21
 */
@Data
public class FlowTriggerEvent {

    private String appCode;
    private String sceneCode;
    private Map<String, Object> params;


    public static FlowTriggerEvent of(String appCode, FlowSceneEum sceneEum) {
        FlowTriggerEvent event = new FlowTriggerEvent();
        event.setAppCode(appCode);
        event.setSceneCode(sceneEum.getCode());
        return event;
    }


    public static FlowTriggerEvent of(String appCode, FlowSceneEum sceneEum, Map<String, Object> params) {
        FlowTriggerEvent event = new FlowTriggerEvent();
        event.setAppCode(appCode);
        event.setSceneCode(sceneEum.getCode());
        event.setParams(params);
        return event;
    }


    public FlowTriggerEvent addParam(String key, Object value) {
        if (StringUtils.isNotBlank(key) && Objects.nonNull(value)) {
            if (Objects.isNull(params)) {
                params = new HashMap<>(8);
            }
            params.put(key, value);
        }
        return this;
    }


    public FlowTriggerEvent addUserIdParam(Long userId) {
        return addParam("userId", userId);
    }

    public FlowTriggerEvent addUserEmailParam(String email) {
        return addParam("email", email);
    }

    public Long fetchUserId() {
        if (Objects.isNull(params)) {
            return null;
        }
        return MapUtils.getLong(params, "userId");
    }

}