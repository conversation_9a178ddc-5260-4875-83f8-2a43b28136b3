package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.copilot.app.domain.seocopilot.constant.BlogTDKCheckItem;
import com.alibaba.copilot.app.domain.seocopilot.dto.ArticleKeywordsResultDTO;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoBlogArticleAttributes;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ArticleTDKRequest {

    /**
     * 文章内容
     */
    private SeoBlogArticleAttributes articleAttributes;
    /**
     * keywords提取、诊断结果
     */
    private ArticleKeywordsResultDTO keywordsResultDTO;
    /**
     * 校验项
     */
    private BlogTDKCheckItem checkItem;
}
