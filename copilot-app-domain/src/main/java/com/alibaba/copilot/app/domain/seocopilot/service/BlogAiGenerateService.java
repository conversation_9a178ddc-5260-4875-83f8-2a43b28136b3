package com.alibaba.copilot.app.domain.seocopilot.service;

import com.alibaba.copilot.app.client.seocopilot.request.BlogArticleGenerateRequest;
import com.alibaba.copilot.app.domain.seocopilot.dto.BlogKeywordAiExtractDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.BlogTDUAiGenerateDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.BlogTitleAiGenerateDTO;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShop;
import com.alibaba.copilot.app.domain.seocopilot.request.BlogKeywordAiPromptRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.BlogContentAiPromptRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.BlogOutlineAiPromptRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.BlogTitleAiPromptRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

import java.util.Map;

/**
 * <AUTHOR>
 * @des Blog多轮交互 AI 的内容生成service
 */
public interface BlogAiGenerateService {

    /**
     * blog相关关键词提取
     *
     * @param seoShop
     * @param prompt
     * @param aiPromptRequest
     * @return
     */
    SingleResult<BlogKeywordAiExtractDTO> blogKeywordAiExtract(SeoShop seoShop, String prompt, BlogKeywordAiPromptRequest aiPromptRequest);

    /**
     * outlineForBlogRewrite
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return outline
     */
    SingleResult<String> outlineForBlogRewrite(SeoShop seoShop, BlogOutlineAiPromptRequest aiPromptRequest);

    /**
     * contentForBlogRewrite
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return content
     */
    SingleResult<Map<String, String>> contentForBlogRewrite(SeoShop seoShop, BlogContentAiPromptRequest aiPromptRequest);

    /**
     * titleForProductRewrite
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return title
     */
    SingleResult<BlogTitleAiGenerateDTO> titleForProductRewrite(SeoShop seoShop, BlogTitleAiPromptRequest aiPromptRequest);

    /**
     * outlineForProductRewrite
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return outline
     */
    SingleResult<String> outlineForProductRewrite(SeoShop seoShop, BlogOutlineAiPromptRequest aiPromptRequest);

    /**
     * contentForProductRewrite
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return content
     */
    SingleResult<Map<String, String>> contentForProductRewrite(SeoShop seoShop, BlogContentAiPromptRequest aiPromptRequest);

    /**
     * Blog多轮交互 - 长标题生成
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return
     */
    SingleResult<BlogTitleAiGenerateDTO> blogTitleAiGenerate(SeoShop seoShop, BlogTitleAiPromptRequest aiPromptRequest);

    /**
     * Blog多轮交互 - 长标题生成
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return
     */
    SingleResult<BlogTitleAiGenerateDTO> blogTitleAiGenerateByKeywordAndTopic(SeoShop seoShop, BlogTitleAiPromptRequest aiPromptRequest);

    /**
     * Blog多轮交互 - 提纲生成
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return outline
     */
    SingleResult<String> blogOutlineAiGenerate(SeoShop seoShop, BlogOutlineAiPromptRequest aiPromptRequest);

    /**
     * Blog多轮交互 - 正文生成
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return Map
     * key:currentOutlineH3  value:对应的blog段落content
     */
    SingleResult<Map<String, String>> blogContentAiGenerate(SeoShop seoShop, BlogContentAiPromptRequest aiPromptRequest);

    /**
     * Blog多轮交互 - TDK（title、description、url）生成
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return
     */
    SingleResult<BlogTDUAiGenerateDTO> blogTDKAiGenerate(SeoShop seoShop, BlogContentAiPromptRequest aiPromptRequest);

    /**
     * 异步生成内容
     *
     * @param seoShop
     * @param request
     * @param blogContentAiPromptRequest
     * @return
     */
    SingleResult<Long> submitGenerateBlog4Add(SeoShop seoShop, BlogArticleGenerateRequest request, BlogContentAiPromptRequest blogContentAiPromptRequest, Boolean needCheck, Long seoBlogAnalysisDataId);

    /**
     * 异步生成内容
     *
     * @param seoShop
     * @param request
     * @param blogContentAiPromptRequest
     * @return
     */
    SingleResult<Long> submitGenerateBlog4Optimize(SeoShop seoShop, BlogArticleGenerateRequest request, BlogContentAiPromptRequest blogContentAiPromptRequest);

    /**
     * 获取随机内链
     */
    String getRandomInternalLink(Long shopId);

    /**
     * title重复性校验
     *
     * @param title
     * @return
     */
    Boolean titleRepeatabilityCheck(String title, Long shopId);

    /**
     * titleForProductRank
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return title
     */
    SingleResult<BlogTitleAiGenerateDTO> titleForProductRank(SeoShop seoShop, BlogTitleAiPromptRequest aiPromptRequest);

    /**
     * outlineForProductRank
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return outline
     */
    SingleResult<String> outlineForProductRank(SeoShop seoShop, BlogOutlineAiPromptRequest aiPromptRequest);

    /**
     * contentForProductRank
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return content
     */
    SingleResult<String> contentForProductRank(SeoShop seoShop, BlogContentAiPromptRequest aiPromptRequest);

    /**
     * titleForProductRank
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return title
     */
    SingleResult<BlogTitleAiGenerateDTO> titleForComparison(SeoShop seoShop, BlogTitleAiPromptRequest aiPromptRequest);

    /**
     * outlineForProductRank
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return outline
     */
    SingleResult<String> outlineForComparison(SeoShop seoShop, BlogOutlineAiPromptRequest aiPromptRequest);

    /**
     * contentForProductRank
     *
     * @param seoShop
     * @param aiPromptRequest
     * @return content
     */
    SingleResult<String> contentForComparison(SeoShop seoShop, BlogContentAiPromptRequest aiPromptRequest);
}
