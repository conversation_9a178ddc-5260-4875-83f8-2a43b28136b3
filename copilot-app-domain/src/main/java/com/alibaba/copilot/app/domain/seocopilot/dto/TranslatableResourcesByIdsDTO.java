package com.alibaba.copilot.app.domain.seocopilot.dto;

import lombok.Data;
import lombok.Getter;

import java.util.List;

@Data
public class TranslatableResourcesByIdsDTO extends ShopifyGraphQLBaseResult {

    private Data data;

    @lombok.Data
    public static class Data {
        private TranslatableResourcesByIds translatableResourcesByIds;
    }

    @lombok.Data
    public static class TranslatableResourcesByIds {
        private List<Edge> edges;
    }

    @lombok.Data
    public static class Edge {
        private TranslatableResource node;
    }

    @lombok.Data
    public static class TranslatableResource {
        private String resourceId;

        private List<TranslatableContent> translatableContent;
    }

    @lombok.Data
    public static class TranslatableContent {

        private String key;

        private String value;

        private String digest;

        private String locale;
    }

    @Getter
    public enum Key {
        title,
        body_html,
        handle,
        meta_title,
        meta_description
    }
}
