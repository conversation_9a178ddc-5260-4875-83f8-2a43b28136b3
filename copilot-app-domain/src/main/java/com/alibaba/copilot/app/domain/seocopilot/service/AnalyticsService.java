package com.alibaba.copilot.app.domain.seocopilot.service;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.domain.seocopilot.dto.*;
import com.alibaba.copilot.app.domain.seocopilot.request.AnalyticsDateRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.AnalyticsPageRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

import java.util.List;

public interface AnalyticsService {
    /**
     * 更新Google Analytics PropertiesId
     *
     */
    SingleResult<Boolean> updateGaPropertiesId(Long shopId,String propertiesId);
    /**
     * 获取概览数据
     *
     */
    AnalyticsOverViewDataDTO getSiteOverview(Long shopId, String date);
    /**
     * 获取首页Dashboard概览数据及统计数据
     *
     */
    DashboardOverViewDataDTO getDashboardData(Long shopId);
    /**
     * 获取统计数据
     *
     */
    List<AnalyticsStatisticDataDTO> getSiteStatistics(Long shopId, AnalyticsDateRequest request);
    /**
     * 用户国家分布
     *
     */
    List<List<Object>> getUserCountryDistribution(Long shopId, AnalyticsDateRequest request);
    /**
     * 排名范围分布
     *
     */
    List<AnalyticsPositionDistributionDTO> getPositionDistribution(Long shopId, AnalyticsDateRequest request);
    /**
     * 关键词列表
     *
     */
    PageWrapper<AnalyticsOrganicKeywordsDTO> getOrganicKeywords(Long shopId, AnalyticsPageRequest request);
    /**
     * 站点页面列表
     *
     */
    PageWrapper<AnalyticsOrganicPagesDTO> getOrganicPages(Long shopId,AnalyticsPageRequest request);
    /**
     * 根据关键词列表查询过去30天的GSC数据
     *
     */
    GscQueryDTO queryGSCDataByKeywords(Long shopId, List<String> keywords);
    /**
     * GSC绑定状态
     *
     */
    GoogleBindStatusDTO queryGoogleBindStatus(Long shopId);
    /**
     * google绑定oauth地址
     *
     */
    GoogleBindOAuthAddressDTO googleBindOAuthAddress(Long shopId,String gscRedirectUri,String gaRedirectUri);
    /**
     * 解绑GSC
     *
     */
    Boolean unbindGsc(Long shopId);
    /**
     * 解绑GA
     *
     */
    Boolean unbindGa(Long shopId);
}
