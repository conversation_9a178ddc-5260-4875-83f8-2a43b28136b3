package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.copilot.app.domain.seocopilot.constant.PromptVersionEnum;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @des Blog Outline Ai Prompt
 */
@Data
@Builder
public class BlogOutlineAiPromptRequest {

    private Long articleId;
    /**
     * scene
     * Enum: KEYWORD\TOPIC\PRODUCT\PRODUCT_RANK\BRAND_COMPARISON\CATEGORY_COMPARISON
     * com.alibaba.copilot.app.domain.seocopilot.constant.SeoContentGenSceneEnum
     */
    private String scene;

    /**
     * 关键词
     * 格式：a,b,c,d
     */
    private String keywords;

    /**
     * style
     */
    private String style;

    /**
     * blogCategory
     */
    private String blogCategory;

    /**
     * link
     */
    private String link;

    /**
     * title
     */
    private String title;

    /**
     * country
     */
    private String country;

    /**
     * retrievalText
     */
    private String retrievalText;

    /**
     * prompt version
     */
    private PromptVersionEnum promptVersion;

    /**
     * language
     */
    private String language;

    /**
     * productInformation
     */
    private String productInformation;

    /**
     * productCount
     */
    private String productCount;

    /**
     * comparisonInformation
     */
    private String comparisonInformation;

    /**
     * tendency
     */
    private String tendency;
}
