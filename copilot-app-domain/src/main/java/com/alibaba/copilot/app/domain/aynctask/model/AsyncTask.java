package com.alibaba.copilot.app.domain.aynctask.model;

import com.alibaba.copilot.boot.basic.data.BaseDbObject;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-09-28
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsyncTask extends BaseDbObject {

    /**
     * 所属环境
     */
    private String env;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * seo_shop_id
     */
    private Long shopId;

    /**
     * 应用程序源
     */
    private String appSource;

    /**
     * 外部ID
     */
    private String outerId;

    /**
     * 任务Key
     */
    private String uniqueKey;

    /**
     * 任务类型
     */
    private String type;

    /**
     * 任务优先级
     */
    private Integer priority = 10;

    /**
     * 重试间隔(毫秒)
     */
    private Integer retryInterval;

    /**
     * 执行次数
     */
    private Integer executeCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryTimes;

    /**
     * 开始执行时间
     */
    private Date startExecuteDate;

    /**
     * 下次执行时间
     */
    private Date nextExecuteDate;

    /**
     * 过期时间
     */
    private Date expireDate;

    /**
     * 心跳时间
     */
    private Date heartbeatDate;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 已读状态
     */
    private String readStatus;

    /**
     * 任务请求
     */
    private String request;

    /**
     * 任务结果
     */
    private String result;

    /**
     * 扩展属性
     */
    private AsyncTaskAttributes attributes = new AsyncTaskAttributes("{}");

    public <REQUEST> REQUEST getRequest(Class<REQUEST> requestClass) {
        if (getRequest() == null) {
            return null;
        }
        return JSONObject.parseObject(getRequest(), requestClass);
    }

    public <DATA> SingleResult<DATA> getResult(Class<DATA> dataClass) {
        if (getResult() == null) {
            return null;
        }

        JSONObject resultJSON = JSONObject.parseObject(getResult());
        SingleResult result = resultJSON.toJavaObject(SingleResult.class);

        if (resultJSON.containsKey("data")) {
            result.setData(resultJSON.getObject("data", dataClass));
        }

        return result;
    }

    public void setRequest(Object request) {
        if (request == null) {
            return;
        }
        if (request instanceof String) {
            this.request = request.toString();
        } else {
            this.request = JSONObject.toJSONString(request);
        }
    }

    public void setResult(Object result) {
        if (result == null) {
            return;
        }
        if (result instanceof String) {
            this.result = String.valueOf(result);
        } else {
            this.result = JSONObject.toJSONString(result);
        }
    }

    public boolean isExpired() {
        return getExpireDate().before(new Date());
    }

    public boolean isExceedRetryTimes() {
        return getExecuteCount() > getMaxRetryTimes();
    }
}
