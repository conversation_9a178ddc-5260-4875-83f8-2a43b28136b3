package com.alibaba.copilot.app.domain.seocopilot.dto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GAQueryResponseDTO {
    private List<DimensionHeader> dimensionHeaders;
    private List<MetricHeader> metricHeaders;
    private List<Row> rows;
    private int rowCount;
    private Metadata metadata;
    private String kind;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DimensionHeader {
        private String name;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MetricHeader {
        private String name;
        private String type;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Row {
        private List<DimensionValue> dimensionValues;
        private List<MetricValue> metricValues;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DimensionValue {
        private String value;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class MetricValue {
        private double value;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Metadata {
        private String currencyCode;
        private String timeZone;
    }
}
