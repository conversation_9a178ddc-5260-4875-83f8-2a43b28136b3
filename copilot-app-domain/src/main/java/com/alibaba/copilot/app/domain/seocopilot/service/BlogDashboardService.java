package com.alibaba.copilot.app.domain.seocopilot.service;

import com.alibaba.copilot.app.domain.seocopilot.dto.DashboardBlogDataDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.DashboardUserDailyTaskInfoDTO;

public interface BlogDashboardService {
    /**
     * 获取博客生成数据
     *
     */
    DashboardBlogDataDTO getBlogOverviewData(Long shopId);
    /**
     * 获取用户日常任务信息
     *
     */
    DashboardUserDailyTaskInfoDTO getUserDailyTaskInfo(Long shopId);
}
