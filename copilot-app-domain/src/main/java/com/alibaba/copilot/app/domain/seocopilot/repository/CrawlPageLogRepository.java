package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.client.seocopilot.constant.CrawlPageStatusEnum;
import com.alibaba.copilot.app.domain.seocopilot.model.CrawlPageLog;

import java.util.List;

public interface CrawlPageLogRepository {

    List<CrawlPageLog> getPageLogByJobId(Long jobId);

    Boolean savePageLog(CrawlPageLog crawlPageLog);

    List<CrawlPageLog> getByUrlAndStatus(String url, CrawlPageStatusEnum status);

    CrawlPageLog getById(Long logId);
}
