package com.alibaba.copilot.app.domain.seocopilot.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/9/25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SubmitCrawlJobRequest {
    String url;
    PageOptions pageOptions;
    ExtractorOptions extractorOptions;

    /**
     * 爬虫参数
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class CrawlerOptions {
        public Boolean generateImgAltText;
        public Boolean returnOnlyUrls;
        public Integer maxDepth;
        public String mode;
        public Boolean ignoreSitemap;
        public Integer limit;
        public Boolean allowBackwardCrawling;
    }

    /**
     * 页面参数
     */
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    static class PageOptions {
        Boolean onlyMainContent = true;
        Boolean includeHtml = false;
        Boolean includeRawHtml = false;
        Boolean screenshot=false;
        Integer waitFor;
        List<String> removeTags;
        List<String> onlyIncludeTags;
        Map<String, String> headers;
        Boolean replaceAllPathsWithAbsolutePaths;
    }

    /**
     * LLM参数
     */
    public static class ExtractorOptions {
        String mode;
        String extractionPrompt;
        Map<String, Object> extractionSchema;
    }
}
