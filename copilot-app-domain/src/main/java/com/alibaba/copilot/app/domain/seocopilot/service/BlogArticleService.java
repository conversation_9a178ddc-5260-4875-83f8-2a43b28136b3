package com.alibaba.copilot.app.domain.seocopilot.service;

import com.alibaba.copilot.app.client.seocopilot.request.ArticleAcceptProposalRequest;
import com.alibaba.copilot.app.client.seocopilot.request.ArticleApplyProposalRequest;
import com.alibaba.copilot.app.domain.seocopilot.constant.BlogArticleOptimizeTaskType;
import com.alibaba.copilot.app.domain.seocopilot.dto.*;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoBlogArticle;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoBlogArticleAttributes;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShop;
import com.alibaba.copilot.app.domain.seocopilot.request.*;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.copilot.boot.monitor.annotation.Monitor;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface BlogArticleService {

    /**
     * 获取优化的关键词列表
     *
     * @param seoShop
     * @param seoBlogArticle
     * @return
     */
    ArticleKeywordsResultDTO optimizeKeywords(SeoShop seoShop, SeoBlogArticle seoBlogArticle);

    /**
     * 获取诊断的关键词列表
     *
     * @param seoShop
     * @param seoBlogArticle
     * @return
     */
    ArticleKeywordsResultDTO getCheckKeywords(SeoShop seoShop, SeoBlogArticle seoBlogArticle);

    /**
     * 通过商品获取生成博客title的关键词
     *
     * @param seoShop
     * @return
     */
    List<String> getGenTitleKeywordsByProduct(SeoShop seoShop);

    /**
     * 校验权益
     *
     * @param shopifyShopId
     * @param articleId
     * @return
     */
    SingleResult<Boolean> checkUsage(Long shopifyShopId, Long articleId);

    /**
     * 检查订阅计划文章生成用量
     */
    SingleResult<Boolean> checkArticleGenerateUsage(Long shopifyShopId);

    /**
     * 检查订阅计划文章优化用量
     */
    SingleResult<Boolean> checkArticleOptimizeUsage(Long shopifyShopId);

    /**
     * @param shopifyShopId
     * @param articleId
     * @return
     */
    String genOrOpt(Long shopifyShopId, Long articleId);

    SeoBlogArticle initArticle(Long seoShopId, SeoBlogArticleAttributes seoBlogArticleAttributes);

    /**
     * 新建空blog文章，status为内容生成中
     *
     * @param seoShop
     * @return
     */
    SeoBlogArticle createEmptyArticle(SeoShop seoShop);

    /**
     * 检验blog文章的tdk单项
     *
     * @param request
     * @return
     */
    ArticleTDKCheckResultDTO checkTDK(ArticleTDKRequest request);

    /**
     * 计算blog文章单项得分
     *
     * @param calculateRequest
     * @return
     */
    ArticleCalculateResultDTO calculateScore(ArticleCalculateRequest calculateRequest);

    /**
     * blog文章优化
     *
     * @param optimizeRequest
     * @return
     */
    ArticleOptimizeResultDTO optimizeArticle(SeoShop seoShop, ArticleOptimizeRequest optimizeRequest);

    /**
     * 新生成blog文章
     *
     * @param generateRequest
     * @return
     */
    ArticleGenerateResultDTO generateArticle(ArticleGenerateRequest generateRequest);

    /**
     * 采纳建议
     *
     * @param request
     */
    void acceptProposal(SeoShop seoShop, ArticleAcceptProposalRequest request);

    /**
     * 应用优化提案
     *
     * @param request
     */
    void applyProposal(ArticleApplyProposalRequest request);

    /**
     * 批量采纳
     *
     * @param request
     */
    void batchApplyProposal(ArticleBatchApplyRequest request);

    /**
     * 是否需要检测
     *
     * @param seoShop
     * @param seoBlogArticle
     * @return
     */
    boolean needCheck(SeoShop seoShop, SeoBlogArticle seoBlogArticle);

    /**
     * 保存blog 编辑
     *
     * @param shopifyShopId
     * @param request
     * @return
     */
    SingleResult<Boolean> saveProposal(Long shopifyShopId, BlogArticleOptimizeRequest request,Boolean saveArticle);

    /**
     * 保存blog 编辑
     *
     * @param shopifyShopId
     * @param request
     * @return
     */
    Boolean saveProposalV2(Long shopifyShopId, BlogArticleOptimizeRequest request);

    /**
     * 应用blog
     *
     * @param shopifyShopId
     * @param request
     * @return
     */
    SingleResult<Boolean> applyToShopify(Long shopifyShopId, BlogArticleOptimizeRequest request);

    /**
     * 应用blog
     *
     * @param shopifyShopId
     * @param request
     * @return
     */
    PublishArticleDTO applyToShopifyV2(Long shopifyShopId, BlogArticleOptimizeRequest request);

    /**
     * 是否多轮
     *
     * @param shopifyShopId
     * @param articleId
     * @return
     */
    Boolean isMultiturnGenerate(Long shopifyShopId, Long articleId);

    /**
     * 优化博客
     *
     * @param shopifyShopId
     * @param articleId
     * @param keywords
     */
    SingleResult<String> reGenForOptArticle(Long shopifyShopId, Long articleId, String keywords);
}