package com.alibaba.copilot.app.domain.n8n.provider;

import com.alibaba.copilot.app.client.flow.FlowNodeExecuteSPI;
import com.alibaba.copilot.app.client.flow.model.FlowNodeRequest;
import com.alibaba.copilot.app.client.flow.model.FlowNodeResponse;
import com.alibaba.copilot.app.domain.base.repository.CacheRepository;
import com.alibaba.copilot.app.domain.n8n.enums.FlowSceneEum;
import com.alibaba.copilot.boot.basic.result.SingleResult;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

@Slf4j
@HSFProvider(serviceName = "edmSentLastDaysFlowNodeExecuteSPI", serviceInterface = FlowNodeExecuteSPI.class, serviceVersion = "1.0.0.SEO_COPILOT.isEdmSentLastDays")
public class EdmSentLastDays implements FlowNodeExecuteSPI{
    public static final String EMAIL_KEY = "email";
    public static final String DURING_KEY = "seconds";
    public static final String FLOW_SCENE_KEY = "flowScene";
    public static final String IS_SENT = "isSent";

    @Resource
    private CacheRepository cacheRepository;

    /**
     * @param request req: "email","seconds","flowScene"
     * @return
     */
    @Override
    public SingleResult<FlowNodeResponse> execute(FlowNodeRequest request) {
        log.info("EdmSentLastDays.execute request:{}", JSON.toJSONString(request));
        String email = request.getData().getString(EMAIL_KEY);
        Long seconds = request.getData().getLong(DURING_KEY);
        String flowScene = request.getData().getString(FLOW_SCENE_KEY);
        FlowSceneEum flowSceneEum = FlowSceneEum.getByCode(flowScene);
        if (flowSceneEum == null) {
            log.error("EdmSentLastDays.execute FlowSceneEum check failed:{}", flowScene);
            return SingleResult.buildFailure(String.format("EdmSentLastDays FlowSceneEum doesn't exist. %s %s", flowScene, email));
        }

        String REDIS_KEY_PRE = "EdmSentLastDays";
        String key = String.format("%s_%s_%s", REDIS_KEY_PRE, flowSceneEum.getType(), email);

        FlowNodeResponse response = new FlowNodeResponse();
        response.setAppCode(request.getAppCode());
        response.setActionCode(request.getActionCode());
        JSONObject data = new JSONObject();
        if (cacheRepository.get(key) != null) {
            data.put(IS_SENT, "true");
        } else {
            cacheRepository.put(key, "1", seconds);
            data.put(IS_SENT, "false");
        }

        response.setData(data);
        log.info("FlowSceneEum.execute response:{}", JSON.toJSONString(response));
        return SingleResult.buildSuccess(response);
    }
}
