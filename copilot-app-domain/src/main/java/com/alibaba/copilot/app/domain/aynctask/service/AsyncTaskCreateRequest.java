package com.alibaba.copilot.app.domain.aynctask.service;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

import com.alibaba.copilot.app.domain.seocopilot.constant.SeoJobType;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShop;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-06-27
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AsyncTaskCreateRequest implements Serializable {

    /**
     * 批次号
     */
    private String batchId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * seo_shop_id
     */
    private Long shopId;

    /**
     * seoShop
     */
    private SeoShop shop;

    /**
     * 应用程序源
     */
    private String appSource;

    /**
     * worker类型
     */
    private String type;

    /**
     * 任务类型
     */
    private SeoJobType jobType;

    /**
     * 任务优先级
     */
    private Integer priority;

    /**
     * 重试间隔(毫秒)
     */
    private Integer retryInterval;

    /**
     * 执行次数
     */
    private Integer executeCount;

    /**
     * 最大重试次数
     */
    private Integer maxRetryTimes;

    /**
     * 开始执行时间
     */
    private Date startExecuteDate;

    /**
     * 下次执行时间
     */
    private Date nextExecuteDate;

    /**
     * 过期时间
     */
    private Date expireDate;

    /**
     * 心跳时间
     */
    private Date heartbeatDate;

    /**
     * 任务状态
     */
    private String status;

    /**
     * 已读状态
     */
    private String readStatus;

    /**
     * 任务请求
     */
    private String request;

    /**
     * 扩展属性
     */
    private String attributes;

    /**
     * 优化级别
     * shop\module\blog\product
     */
    private String optimizeLevel;

    /**
     * 优化模块
     * seoScore\seoIndex\seoRank
     */
    private List<String> module;

    /**
     * blogId
     */
    private List<String> blogIds;

    /**
     * 商品id
     */
    private List<String> productIds;

    /**
     * 商品优化项，例如：title、content 等
     */
    private List<String> productOptimizeItems;

    /**
     * 诊断项Id
     */
    private List<String> recordIds;
}
