package com.alibaba.copilot.app.domain.seocopilot.gateway;


import com.alibaba.copilot.app.domain.seocopilot.dto.*;
import com.alibaba.copilot.app.domain.seocopilot.request.*;
import com.alibaba.copilot.boot.basic.result.SingleResult;

import java.util.List;

/**
 * 算法Tpp服务 Gateway
 */
public interface AlgorithmBlogRewriteTppGateway {

    /**
     * 根据url获取关键词
     * <p>
     * api_name：seo_url_to_keywords (必填）
     * src_biz_name: xxx（必填，目前为空字符串即可）
     * scene: Collection/Product/Blog/Homepage（目前没有，为空字符串""，非必填）
     * data
     * {
     * "url": xx // string，必传
     * }
     * <p>
     * "data": ["keywords_1", "keywords_2", "keywords_3"]
     */
    SingleResult<List<GenerateKeywordByUrlDTO>> generateKeywordByUrl(GenerateKeywordByUrlsRequest request);


    /**
     * 关键词排序
     * <p>
     * api_name：seo_keyword_rerank
     * src_biz_name: xxx（必填，目前为空字符串即可）
     * scene: Collection/Product/Blog/Homepage（目前没有，为空字符串""，非必填）
     * data
     * {
     * "keywords_list": [
     * {"keyword":"keyword_1", "kd":kd_1, "search_volume":volume_1}, {"keyword":"keyword_2", "kd":kd_2, "search_volume":volume_2}, {"keyword":"keyword_3", "kd":kd_3, "search_volume":volume_3}] // list，必传
     * }
     * <p>
     * "data": {
     * "keywords": [
     * {"keyword":"keyword_1", "kd":kd_1, "search_volume":volume_1},
     * {"keyword":"keyword_2", "kd":kd_2, "search_volume":volume_2},
     * {"keyword":"keyword_3", "kd":kd_3, "search_volume":volume_3},
     * ],
     * "reason": string
     * }
     *
     * @param request
     * @return
     */
    SingleResult<ReRankKeywordsDTO> reRankKeywords(ReRankKeywordsRequest request);


    /**
     * 生成title和outline
     * <p>
     * api_name: seo_rewrite_title_outline
     * src_biz_name:
     * scene: tools_ranking
     * data
     * {
     * "keywords": ['xx1', 'xx2'] //（非必传）
     * "urls": [""] // list，当前默认一个（必传）
     * }
     * <p>
     * {
     * "title": "" // string
     * "outline": "" // string
     * "references_blogs": "" // string
     * "retrieval_info_list": ["xx1", "xx2"]
     * }
     *
     * @param request
     */
    SingleResult<GenerateTitleAndOutlineDTO> generateTitleAndOutline(GenerateTitleAndOutlineRequest request);


    /**
     * 生成内容
     * api_name: seo_rewrite_content
     * src_biz_name:
     * scene: tools_ranking
     * data
     * {
     * "keywords": ['xx1', 'xx2'] //（非必传）
     * "title": "" // string
     * "outline": "" // string
     * "references_blogs": "" // string
     * "retrieval_info_list": ["xx1", "xx2"]
     * }
     * <p>
     * {
     * "title": "" // string
     * "outline": "" // string
     * "blog": ["xxx1"] // list 取到一个
     * }
     *
     * @param request
     * @return
     */
    SingleResult<GenerateContentDTO> generateContent(GenerateContentRequest request);


    /**
     * 生成TDK
     * <p>
     * api_name: seo_tdk
     * src_biz_name:
     * scene:
     * data
     * {
     * "keywords": ['xx1', 'xx2'] //（非必传）
     * "blog": "" // string
     * }
     * <p>
     * {
     * "title_tag": "" // string
     * "meta_description": "" // string
     * }
     *
     * @param request
     */
    SingleResult<GenerateTDKDTO> generateTDK(GenerateTDKRequest request);

    ;

}
