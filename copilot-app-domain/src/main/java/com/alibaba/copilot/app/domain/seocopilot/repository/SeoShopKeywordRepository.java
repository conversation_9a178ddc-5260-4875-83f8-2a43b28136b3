package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShopKeyword;
import com.alibaba.copilot.app.domain.seocopilot.request.ShopKeywordQuery;

import java.util.List;

public interface SeoShopKeywordRepository {

    /**
     * 保存关键词信息
     */
    Integer save(SeoShopKeyword seoShopKeyword);

    void batchSave(List<SeoShopKeyword> seoShopKeywords);

    /**
     * 根据条件获取关键词
     */
    List<SeoShopKeyword> getByParameters(ShopKeywordQuery query);

    /**
     * 根据条件分页查询关键词
     */
    PageWrapper<SeoShopKeyword> getByParametersWithPage(ShopKeywordQuery query);

    /**
     * 根据关键词获取记录
     *
     * @param shopId
     * @param keyword
     * @return
     */
    SeoShopKeyword getByKeyword(Long shopId, String keyword);

    /**
     * 根据关键词获取记录
     *
     * @param shopId
     * @param topic
     * @return
     */
    SeoShopKeyword getByTopic(Long shopId, String topic);

    /**
     * 根据ShopId逻辑删除关键词信息
     *
     * @param shopId
     * @return
     */
    Integer deleteByShopId(Long shopId);

    /**
     * 分页查询关键词列表
     */
    PageWrapper<SeoShopKeyword> getKeywordsPage(Long shopId, Integer pageNum, Integer pageSize, String type);

    /**
     * 根据来源分页查询关键词列表
     */
    PageWrapper<SeoShopKeyword> getGscKeywordsPage(Long shopId, Integer pageNum, Integer pageSize, String type, String clicksOrder, String impressionsOrder, String ctrOrder, String positionOrder);

    /**
     * 查询关键词
     */
    SeoShopKeyword getByIdAndShopId(Long id,Long shopId);

    /**
     * 查询关键词
     */
    List<SeoShopKeyword> getByShopId(Long shopId);

    /**
     * 查询最新topic
     */
    List<SeoShopKeyword> getLatestTopic(Integer limit, Long shopId, String language);

    /**
     * 根据source查询关键词
     */
    List<SeoShopKeyword> getKeywordBySource(Long shopId, String source, Integer limit);

    /**
     * 查询收藏的关键词数据
     */
    List<SeoShopKeyword> getKeywordsByPreference(Long shopId, Integer limit, String type, List<String> preferences);
}
