package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.boot.basic.data.Attributes;

/**
 * SeoWebPageAttributes
 */
public class SeoShopKeywordAttributes extends Attributes {

    public SeoShopKeywordAttributes(String json) {
        super(json);
    }

    /**
     * semrush 原始数据
     */
    private static final String SEMRUSH_ORIGINAL_CONTENT = "semrushOriginalContent";

    /**
     * gsc 原始数据
     */
    private static final String GSC_ORIGINAL_CONTENT = "gscOriginalContent";

    /**
     * 算法推荐原始数据
     */
    private static final String ALGORITHM_ORIGINAL_CONTENT = "algorithmOriginalContent";

    /**
     * semrush 获取关键词的domain
     *
     * @return
     */
    private static final String SEMRUSH_KEYWORD_DOMAIN = "keywordDomain";

    /**
     * semrush 获取关键词的关键词
     *
     * @return
     */
    private static final String SEMRUSH_KEYWORD_KEYWORD = "keywordKeyword";

    /**
     * semrush 获取topic的关键词
     *
     * @return
     */
    private static final String SEMRUSH_TOPIC_KEYWORD = "topicKeyword";

    /**
     * 拓词的种子关键词
     * @return
     */
    private static final String EXTEND_SEED_KEYWORD = "extendSeedKeyword";

    /**
     * 拓词触发场景
     *
     * @return
     */
    private static final String EXTEND_KEYWORD_SCENE = "extendKeywordScene";


    public String getSemrushOriginalContent() {
        return getAsString(SEMRUSH_ORIGINAL_CONTENT);
    }

    public void setSemrushOriginalContent(String content) {
        put(SEMRUSH_ORIGINAL_CONTENT, content);
    }

    public String getGscOriginalContent() {
        return getAsString(GSC_ORIGINAL_CONTENT);
    }

    public void setGscOriginalContent(String content) {
        put(GSC_ORIGINAL_CONTENT, content);
    }

    public String getSemrushKeywordDomain() {
        return getAsString(SEMRUSH_KEYWORD_DOMAIN);
    }

    public void setSemrushKeywordDomain(String keywordDomain) {
        put(SEMRUSH_KEYWORD_DOMAIN, keywordDomain);
    }

    public String getSemrushKeywordKeyword() {
        return getAsString(SEMRUSH_KEYWORD_KEYWORD);
    }

    public void setSemrushKeywordKeyword(String keywordKeyword) {
        put(SEMRUSH_KEYWORD_KEYWORD, keywordKeyword);
    }

    public String getSemrushTopicKeyword() {
        return getAsString(SEMRUSH_TOPIC_KEYWORD);
    }

    public void setSemrushTopicKeyword(String topicKeyword) {
        put(SEMRUSH_TOPIC_KEYWORD, topicKeyword);
    }

    public String getExtendSeedKeyword() {
        return getAsString(EXTEND_SEED_KEYWORD);
    }

    public void setExtendSeedKeyword(String seedKeyword) {
        put(EXTEND_SEED_KEYWORD, seedKeyword);
    }

    public void setExtendKeywordScene(String extendKeywordScene) {
        put(EXTEND_KEYWORD_SCENE, extendKeywordScene);
    }

    public String getExtendKeywordScene() {
        return getAsString(EXTEND_KEYWORD_SCENE);
    }

    public String getAlgorithmOriginalContent() {
        return getAsString(ALGORITHM_ORIGINAL_CONTENT);
    }

    public void setAlgorithmOriginalContent(String content) {
        put(ALGORITHM_ORIGINAL_CONTENT, content);
    }
}
