package com.alibaba.copilot.app.domain.seocopilot.service;

import com.alibaba.copilot.app.domain.seocopilot.dto.KeywordGenDTO;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShop;
import com.alibaba.copilot.app.domain.seocopilot.request.KeywordGenerateRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

import java.util.List;

/**
 * <AUTHOR>
 * @des keyword 生成service
 */
public interface KeywordGenerateService {

    /**
     * keyword 生成场景
     *
     * @param seoShop
     * @param generateRequest
     * @return
     */
    SingleResult<List<String>> keywordGenerate(SeoShop seoShop, KeywordGenerateRequest generateRequest);

    /**
     * keyword 生成场景(关键词推荐逻辑)
     *
     * @param seoShop
     * @param generateRequest
     * @return
     */
    SingleResult<List<KeywordGenDTO>> keywordGenerate4Recommend(SeoShop seoShop, KeywordGenerateRequest generateRequest);

}
