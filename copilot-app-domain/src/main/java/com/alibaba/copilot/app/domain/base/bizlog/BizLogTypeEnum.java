package com.alibaba.copilot.app.domain.base.bizlog;

import lombok.Getter;

/**
 * .
 *
 * <AUTHOR>
 * @date 2023/08/10
 */
@Getter
public enum BizLogTypeEnum {

    /**
     * 流程触发（发送MQ）
     */
    FLOW_TRIGGER_SEND_MQ("flow_trigger_send_mq"),
    /**
     * 流程触发（接收MQ）
     */
    FLOW_TRIGGER_RECEIVE_MQ("flow_trigger_receive_mq"),

    /**
     * 流程节点执行
     */
    EXECUTE_FLOW_NODE("execute_flow_node"),

    /**
     * 登录鉴权
     */
    LOGIN_AUTH("login_auth");

    ;

    private final String code;

    BizLogTypeEnum(String code) {
        this.code = code;
    }

    public static BizLogTypeEnum fromCode(String code) {
        for (BizLogTypeEnum type : BizLogTypeEnum.values()) {
            if (type.code.equals(code)) {
                return type;
            }
        }
        return null;
    }
}
