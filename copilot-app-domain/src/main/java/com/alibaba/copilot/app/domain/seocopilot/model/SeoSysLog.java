package com.alibaba.copilot.app.domain.seocopilot.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * SeoSysLog 实体
 */
@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class SeoSysLog {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * seoShopId
     */
    private Long shopId;

    /**
     * 优化项数
     */
    private Integer optimizeCount;

    /**
     * 待优化项数
     */
    private Integer toBeOptimizedCount;

    /**
     * 优化失败项数
     */
    private Integer optimizeFailCount;

    /**
     * 页面数
     */
    private Integer pageCount;

    /**
     * 文章数
     */
    private Integer articleCount;

    /**
     * 商品数
     */
    private Integer productCount;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 扩展字段
     */
    private SeoSysLogAttributes attributes = new SeoSysLogAttributes("{}");

    /**
     * 任务id
     */
    private Long taskId;
}
