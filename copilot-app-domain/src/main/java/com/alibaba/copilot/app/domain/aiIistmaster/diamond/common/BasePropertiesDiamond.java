package com.alibaba.copilot.app.domain.aiIistmaster.diamond.common;

import java.io.IOException;
import java.io.StringReader;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Properties;
import java.util.Set;

import javax.annotation.PostConstruct;

import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


/**
 * <AUTHOR>
 * @date 2023/8/10 11:26 AM
 */
public abstract class BasePropertiesDiamond extends ManagerListenerAdapter {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    private volatile LinkedProperties properties = new LinkedProperties();

    public BasePropertiesDiamond() {
    }

    protected abstract String getDataId();

    protected String getGroupId() {
        return "DEFAULT_GROUP";
    }

    public String getString(String key) {
        return this.properties.getProperty(key);
    }

    public Integer getInteger(String key) {
        String value = this.properties.getProperty(key);
        return StringUtils.isBlank(value) ? null : Integer.valueOf(value);
    }

    public Boolean getBoolean(String key) {
        String value = this.properties.getProperty(key);
        return StringUtils.isBlank(value) ? null : Boolean.valueOf(StringUtils.trim(value));
    }

    public Long getLong(String key) {
        String value = this.properties.getProperty(key);
        return StringUtils.isBlank(value) ? null : Long.valueOf(StringUtils.trim(value));
    }

    @PostConstruct
    public void init() {
        Diamond.addListener(this.getDataId(), this.getGroupId(), this);
        this.manualInit();
    }

    public void manualInit() {
        try {
            this.receiveConfigInfo(Diamond.getConfig(this.getDataId(), this.getGroupId(), 3000L));
        } catch (IOException var2) {
            this.LOGGER.error("init diamond config failed", var2);
        }

    }

    @Override
    public synchronized void receiveConfigInfo(String configInfo) {
        this.LOGGER.info(this.getDataId() + " receiveConfigInfo:" + configInfo);
        if (!StringUtils.isBlank(configInfo)) {
            try {
                this.properties.clear();
                this.properties.orderedKeys().clear();
                this.properties.load(new StringReader(configInfo));
            } catch (RuntimeException | IOException var3) {
                this.LOGGER.error(this.getDataId() + " parse failed, configInfo:" + configInfo, var3);
            }

        }
    }

    public Set<String> orderedKeys() {
        return new LinkedHashSet(this.properties.orderedKeys());
    }

    public synchronized boolean put(String key, String value) {
        try {
            this.properties.put(key, value);
            StringBuilder sb = new StringBuilder();
            Iterator var4 = this.orderedKeys().iterator();

            while (var4.hasNext()) {
                String k = (String)var4.next();
                sb.append(k).append(" = ").append(this.properties.get(k)).append("\n");
            }

            return Diamond.publishSingle(this.getDataId(), this.getGroupId(), sb.toString());
        } catch (RuntimeException var6) {
            this.LOGGER.error("put properties diamond config failed", var6);
            return false;
        }
    }

    public synchronized boolean remove(String key) {
        try {
            this.properties.remove(key);
            this.properties.orderedKeys().remove(key);
            StringBuilder sb = new StringBuilder();
            Iterator var3 = this.orderedKeys().iterator();

            while (var3.hasNext()) {
                String k = (String)var3.next();
                sb.append(k).append(" = ").append(this.properties.get(k)).append("\n");
            }

            return Diamond.publishSingle(this.getDataId(), this.getGroupId(), sb.toString());
        } catch (RuntimeException var5) {
            this.LOGGER.error("remove properties diamond config failed", var5);
            return false;
        }
    }

    public static class LinkedProperties extends Properties {
        private final Set<String> keys = new LinkedHashSet();

        public LinkedProperties() {
        }

        Set<String> orderedKeys() {
            return this.keys;
        }

        public synchronized Object put(Object key, Object value) {
            Object putResult = super.put(key, value);
            this.keys.add(key.toString());
            return putResult;
        }
    }
}
