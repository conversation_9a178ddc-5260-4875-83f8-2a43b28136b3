package com.alibaba.copilot.app.domain.seocopilot.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Data
@Accessors(chain = true)
public class ArticleGenerateResultDTO {
    /**
     * 类目
     */
    private String blogCategory;
    /**
     * 内链
     */
    private String link;
    /**
     * 国家
     */
    private String country;
    /**
     * 风格
     */
    private String blogStyle;

    /**
     * 优化后文章标题
     */
    private String title;

    /**
     * 优化后文章提纲
     */
    private String outline;

    /**
     * 优化后文章内容,html
     */
    private String body;

    /**
     * 优化后seo内容，meta title
     */
    private String titleTag;

    /**
     * 优化后seo内容，meta description
     */
    private String descriptionTag;

    /**
     * 优化后handle
     */
    private String handle;

    /**
     * 使用的搜索关键词
     */
    private List<String> keywords;

    /**
     * retrievalText
     */
    private String retrievalText;
}
