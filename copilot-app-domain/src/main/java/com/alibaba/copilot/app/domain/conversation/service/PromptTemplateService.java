package com.alibaba.copilot.app.domain.conversation.service;

import com.alibaba.copilot.app.domain.seocopilot.request.PromptTemplateCreateRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.PromptTemplateUpdateRequest;
import com.alibaba.copilot.app.domain.seocopilot.response.PromptGenerateResponse;
import com.alibaba.copilot.app.domain.seocopilot.response.PromptTemplateCreateResponse;
import com.alibaba.copilot.app.domain.seocopilot.response.PromptTemplateUpdateResponse;

public interface PromptTemplateService {
    /**
     * 创建一个 template,要求这个 code 之前没有出现过。
     */
    PromptTemplateCreateResponse createTemplate(PromptTemplateCreateRequest request);

    /**
     *
     * @param request
     * @return
     */
    PromptTemplateUpdateResponse updateTemplate(PromptTemplateUpdateRequest request);
    
    /**
     * 根据模板代码渲染
     * @param code 模板代码
     * @param variable 变量
     * @return 渲染结果
     */
    PromptGenerateResponse renderByCode(String code, Object variable);
    
    /**
     * 根据模板内容直接渲染
     * @param templateContent 模板内容
     * @param variable 变量
     * @return 渲染结果
     */
    String generate(String templateContent, Object variable);
}
