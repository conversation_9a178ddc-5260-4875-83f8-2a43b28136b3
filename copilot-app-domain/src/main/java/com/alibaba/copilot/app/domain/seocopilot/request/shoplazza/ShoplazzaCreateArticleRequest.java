package com.alibaba.copilot.app.domain.seocopilot.request.shoplazza;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class ShoplazzaCreateArticleRequest extends ShoplazzaB<PERSON>Request {


    private Article article;

    @Data
    public static class Article {
        private String title;
        private String excerpt;
        private String content;
        private Boolean published;

        @J<PERSON><PERSON>ield(name = "published_at")
        private Date publishedAt;

        private String handle;

        private String author;

        @JSONField(name = "seo_title")
        private String seoTitle;

        @J<PERSON>NField(name = "seo_description")
        private String seoDescription;

        @J<PERSON>NField(name = "seo_keywords")
        private List<String> seoKeywords;

        @J<PERSON><PERSON>ield(name = "blog_ids")
        private List<String> blogIds;

        private Image image;
    }

    @Data
    public static class Image {
        private String src;

        private String alt;
    }

}
