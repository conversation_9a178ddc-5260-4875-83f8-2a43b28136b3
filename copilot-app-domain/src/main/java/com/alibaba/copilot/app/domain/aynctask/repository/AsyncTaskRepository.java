package com.alibaba.copilot.app.domain.aynctask.repository;

import com.alibaba.copilot.app.domain.aynctask.model.AsyncTask;
import com.alibaba.copilot.app.domain.aynctask.model.AsyncTaskStatus;

import java.util.List;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-09-28
 **/
public interface AsyncTaskRepository {
    /**
     * 保存任务
     *
     * @param task
     * @return
     */
    void save(AsyncTask task);

    /**
     * 获取任务
     *
     * @param taskId
     * @return
     */
    AsyncTask getTask(Long taskId);

    /**
     * 查询执行中的任务数量
     *
     * @param taskType
     * @return
     */
    Long countRunningTask(String taskType);

    /**
     * 根据uniqueKey获取任务
     *
     * @param uniqueKey
     * @return
     */
    AsyncTask getTaskByUniqueKey(String uniqueKey);

    /**
     * 获取店铺未完成任务
     *
     * @param shopId
     * @return
     */
    List<AsyncTask> getNotFinishedTasks(Long shopId, String taskType);

    /**
     * 根据outerId获取任务列表
     *
     * @param outerId
     * @param taskType
     * @return
     */
    List<AsyncTask> getTasksByOuterId(String outerId, String taskType);

    /**
     * 获取可执行的任务
     *
     * @param size
     * @return
     */
    List<AsyncTask> getExecutableTasks(Integer size);

    /**
     * 获取失去心跳的任务
     *
     * @param size
     * @return
     */
    List<AsyncTask> getLoseHeartbeatTasks(Integer size);

    /**
     * 获取待重新的任务
     *
     * @param size
     * @return
     */
    List<AsyncTask> getNeedRetryTasks(Integer size);

    /**
     * 更新任务心跳
     *
     * @param taskId
     */
    boolean updateTaskHeartbeatDate(Long taskId);

    /**
     * 更新任务状态
     *
     * @param task
     * @param newStatus
     */
    boolean updateTaskStatus(AsyncTask task, AsyncTaskStatus newStatus);

    /**
     * 更新任务状态&结果
     *
     * @param task
     * @param newStatus
     */
    boolean updateTaskStatusAndResult(AsyncTask task, AsyncTaskStatus newStatus);

    /**
     * 更新任务扩展属性
     *
     * @param task
     */
    boolean updateAttributes(AsyncTask task);
}
