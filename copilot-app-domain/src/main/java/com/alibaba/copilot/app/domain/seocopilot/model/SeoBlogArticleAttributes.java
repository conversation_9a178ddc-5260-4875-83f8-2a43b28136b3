package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.app.client.seocopilot.constant.ImageStyleEnum;
import com.alibaba.copilot.app.client.seocopilot.constant.InternalLinkType;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Slf4j
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class SeoBlogArticleAttributes {
    /**
     * 文章作者
     */
    private String author;
    /**
     * 是否是ai生成的
     */
    private Boolean isAIgenerated;
    /**
     * 图片地址
     */
    private String image;

    /**
     * 图片地址
     */
    private String imageAlt;

    /**
     * 文章标题
     */
    private String title;
    /**
     * 文章缩略展示
     */
    private String summary;
    /**
     * 文章主体,html
     */
    private String body;
    /**
     * title标签内容
     */
    private String titleTag;
    /**
     * title在shopify的metaFieldId
     */
    private Long titleId;
    /**
     * description标签内容
     */
    private String descriptionTag;
    /**
     * description在shopify的metaFieldId
     */
    private Long descriptionId;
    /**
     * url
     */
    private String handle;
    /**
     * 是否可见
     */
    private Boolean published;
    /**
     * 原始得分
     */
    private BigDecimal originScore;
    /**
     * 文章url
     */
    private String url;
    /**
     * blog类目名称
     */
    private String blog;
    /**
     * 上一次采纳时间
     */
    private Date lastApplied;
    /**
     * 原创性得分，查重
     */
    private BigDecimal plagiarismScore;
    /**
     * 查重的scanId
     */
    private String plagiarismScanId;
    /**
     * keywords是否由用户确认过
     */
    private Boolean showKeywords;
    /**
     * 关键词
     */
    private List<String> keywords;

    /**
     * 删除原因
     */
    private String deleteReason;
    /**
     * 标题
     */
    private String multiturnTitle;
    /**
     * 类目
     */
    private String blogCategory;
    /**
     * 内链
     */
    private String link;
    /**
     * 国家
     */
    private String country;
    /**
     * 风格
     */
    private String blogStyle;
    /**
     * 大纲
     */
    private String outline;
    /**
     * 由新人引导生成，需要订阅后才能解锁
     */
    private Boolean generatedByUserGuide;
    /**
     * 来源
     */
    private String source;

    /**
     * retrievalText
     */
    private String retrievalText;

    /**
     * 最近一次诊断分数
     */
    private BigDecimal score;

    /**
     * 发布平台
     */
    private String publishPlatform;

    /**
     * 内链类型
     */
    private InternalLinkType linkType;

    /**
     * 是否使用Shopify实体图片
     */
    private Boolean useShopifyEntityImages;

    /**
     * 实体图片
     */
    private List<String> entityImageUrls;

    /**
     * 内链关联的shopify实体id(eg:shopifyProductId)
     */
    private Long shopifyEntityId;

    /**
     * 文章图片风格
     */
    private ImageStyleEnum articleImageStyle;
}
