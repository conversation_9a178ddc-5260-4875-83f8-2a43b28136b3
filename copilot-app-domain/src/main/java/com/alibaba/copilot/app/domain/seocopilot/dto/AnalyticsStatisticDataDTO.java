package com.alibaba.copilot.app.domain.seocopilot.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data
@Builder
public class AnalyticsStatisticDataDTO {
    /**
     * 站点当天的活跃用户数
     */
    private Long users;
    /**
     * 站点当天的新用户数
     */
    private Long newUsers;
    /**
     * 整个站点的用户平均停留时长
     */
    private BigDecimal averageEngageTime;
    /**
     * 感兴趣话题占比
     */
    private BigDecimal engagementRate;
    /**
     * 站点当天的浏览量
     */
    private Long views;
    /**
     * 时间 YYYY-MM-DD
     */
    private String date;

    public static AnalyticsStatisticDataDTO defaultInstance() {
        return AnalyticsStatisticDataDTO.builder()
                .users(0L)
                .newUsers(0L)
                .averageEngageTime(BigDecimal.valueOf(0))
                .engagementRate(BigDecimal.valueOf(0))
                .views(0L)
                .date("")
                .build();
    }
}
