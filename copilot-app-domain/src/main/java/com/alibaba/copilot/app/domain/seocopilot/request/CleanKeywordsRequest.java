package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.copilot.app.domain.seocopilot.dto.CleanKeywordInfo;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class CleanKeywordsRequest {

    private String apiName = "seo_keyword_clean";

    private String srcBizName = "";

    private String scene = "";

    private Request data;


    @Data
    public static class Request {


        private String language;

        /**
         * keywords
         */
        @JSONField(name = "keywords")
        private List<CleanKeywordInfo> keywords;
    }
}
