package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.domain.seocopilot.model.SeoGoogleAnalyticsData;

import java.util.List;

public interface SeoGoogleAnalyticsDataRepository {
    /**
     * 新增或更新
     */
    void insertOrUpdate(SeoGoogleAnalyticsData data);
    /**
     * 获取最新数据
     */
    SeoGoogleAnalyticsData getLatestOne(Long shopId,String pageUrl);
    /**
     * 新增或更新
     */
    List<SeoGoogleAnalyticsData> getDataByDs(Long shopId, String startDs, String endDs,String pageUrl);

    /**
     * 根据shop id获取数据
     */
    List<SeoGoogleAnalyticsData> getDataByShopId(Long shopId);
}
