package com.alibaba.copilot.app.domain.seocopilot.service;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.client.seocopilot.dto.SeoCollectionHistoryDetailDTO;
import com.alibaba.copilot.app.client.seocopilot.dto.SeoProductHistoryDetailDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.SeoCollectionDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.SeoCollectionPageDTO;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoOptimizeProposal;
import com.alibaba.copilot.app.domain.seocopilot.request.CollectionOptimizeRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.CollectionPageRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

import java.util.List;

public interface CollectionService {
    /**
     * shopify collection list
     */
    PageWrapper<SeoCollectionPageDTO> getCollectionList(Long shopifyShopId, CollectionPageRequest collectionPageRequest);

    SingleResult<SeoCollectionDTO> getCollectionDetail(Long entityId, Long shopifyShopId, Long shopifyCollectionId);

    SingleResult<String> checkCollectionStatus(Long shopifyShopId, Long shopifyCollectionId);

    SingleResult<Boolean> optimize(Long shopifyShopId, Long shopifyCollectionId, List<String> keywords, String scene);

    SingleResult<Boolean> saveProposal(Long shopifyShopId, CollectionOptimizeRequest request);

    SingleResult<Boolean> applyToShopify(Long shopifyShopId, CollectionOptimizeRequest request);

    SingleResult<Boolean> applyCollectionProposal(Long shopifyShopId, List<Long> seoCollectionIds, List<Long> collectionCheckRecordIds);

    SingleResult<Boolean> applyToShopify(Long shopifyShopId, Long shopifyCollectionId, List<SeoOptimizeProposal> seoOptimizeProposals);

    SeoCollectionHistoryDetailDTO getCollectionHistoryDetail(Long shopifyShopId, Long collectionId);

    Boolean rollbackCollection(Long shopifyShopId, Long collectionId, Long recordId);
}
