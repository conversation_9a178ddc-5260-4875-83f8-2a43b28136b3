package com.alibaba.copilot.app.domain.seocopilot.gateway;

import com.alibaba.copilot.app.domain.seocopilot.dto.DetectDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.TranslateDTO;
import com.alibaba.copilot.app.domain.seocopilot.request.DetectRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.TranslateRequest;

public interface TranslateGateway {

    /**
     * 文本翻译
     *
     * @param request
     * @return
     */
    TranslateDTO translate(TranslateRequest request);

    /**
     * 文本语种检测
     */
    DetectDTO detect(DetectRequest request);

}
