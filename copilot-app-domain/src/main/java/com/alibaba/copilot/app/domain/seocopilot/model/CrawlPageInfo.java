package com.alibaba.copilot.app.domain.seocopilot.model;

import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.List;

/**
 * 爬虫页面
 */
@Slf4j
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CrawlPageInfo {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * url
     */
    private String url;

    /**
     * page_id
     */
    private Long pageId;

    /**
     * title_tag
     */
    private String titleTag;

    /**
     * meta_description
     */
    private String metaDescription;

    /**
     * page_type
     */
    private String pageType;

    /**
     * page_keywords
     */
    private List<String> pageKeywords;

    /**
     * 逻辑删除
     */
    private Boolean deleted;
}
