package com.alibaba.copilot.app.domain.seocopilot.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

@Getter
@AllArgsConstructor
@Slf4j
public enum BlogArticleScoreType {
    GOOD("Good", BigDecimal.valueOf(90), BigDecimal.valueOf(100)),

    AVERAGE("Average", BigDecimal.valueOf(50), BigDecimal.valueOf(90)),

    BAD("Bad", BigDecimal.valueOf(0), BigDecimal.valueOf(50));

    private final String value;

    private final BigDecimal lower;

    private final BigDecimal upper;

    public static BlogArticleScoreType fromValue(String value) {
        for (BlogArticleScoreType type : BlogArticleScoreType.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid value: " + value);
    }

    public static BlogArticleScoreType fromData(BigDecimal d) {
        if (d == null) {
            log.warn("score is null");
            return BAD;
        }
        for (BlogArticleScoreType type: BlogArticleScoreType.values()) {
            if ((d.compareTo(type.getUpper()) <= 0) && (d.compareTo(type.getLower()) >= 0)) {
                return type;
            }
        }
        log.warn("score is unexpected, score: {}", d);
        return BAD;
    }
}
