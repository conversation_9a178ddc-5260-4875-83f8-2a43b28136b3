package com.alibaba.copilot.app.domain.seocopilot.gateway;

import com.alibaba.copilot.app.domain.seocopilot.dto.shoplazza.ShoplazzaArticleResponse;
import com.alibaba.copilot.app.domain.seocopilot.dto.shoplazza.ShoplazzaBlogsResponse;
import com.alibaba.copilot.app.domain.seocopilot.dto.shoplazza.ShoplazzaShopResponse;
import com.alibaba.copilot.app.domain.seocopilot.dto.shoplazza.ShoplazzaWebhookResponse;
import com.alibaba.copilot.app.domain.seocopilot.request.shoplazza.ShoplazzaBaseRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.shoplazza.ShoplazzaCreateArticleRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.shoplazza.ShoplazzaCreateWebhookRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.shoplazza.ShoplazzaGetBlogsRequest;

/**
 * 店匠gateway
 *
 * @date 2024/9/19 4:47 下午
 */
public interface ShoplazzaGateway {

    ShoplazzaShopResponse getShop(ShoplazzaBaseRequest request);

    ShoplazzaWebhookResponse createWebhook(ShoplazzaCreateWebhookRequest request);

    ShoplazzaBlogsResponse getBlogs(ShoplazzaGetBlogsRequest request);

    ShoplazzaArticleResponse createArticle(ShoplazzaCreateArticleRequest request);

}
