package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.copilot.app.domain.seocopilot.dto.ReRankKeywordInfo;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class ReRankKeywordsRequest {

    private String apiName = "seo_keyword_rerank";

    private String srcBizName = "";

    private String scene = "";

    private Request data;


    @Data
    public static class Request {
        /**
         * keyword
         */
        @JSONField(name = "keywords_list")
        private List<ReRankKeywordInfo> keywordsList;
    }
}
