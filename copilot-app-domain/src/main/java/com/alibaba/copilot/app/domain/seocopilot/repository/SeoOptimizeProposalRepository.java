package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.client.seocopilot.request.BlogProposalRequest;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoOptimizeProposal;

import java.util.List;

/**
 * SeoOptimizeProposalRepository
 */
public interface SeoOptimizeProposalRepository {

    /**
     * 根据Check Record ID查询Proposal
     *
     * @return
     */
    List<SeoOptimizeProposal> getProposalsByCheckRecordIds(List<Long> checkRecordIds);

    /**
     * 根据id查询Proposal list
     *
     * @return
     */
    List<SeoOptimizeProposal> getProposalsByIds(List<Long> ids);

    SeoOptimizeProposal getProposalById(Long id);

    SeoOptimizeProposal getProposalByEntityId(Long shopId,Long entityId);

    /**
     * save
     */
    int save(SeoOptimizeProposal proposal);

    Integer deleteByShopId(Long shopId);

    List<SeoOptimizeProposal> getProposalsRandom(BlogProposalRequest request);

    Integer deleteByEntityId(Long shopId, Long checkRecordId, Long entityId);
}
