package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.app.client.seocopilot.constant.InternalLinkType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InternalLink {

    /**
     * type
     */
    private InternalLinkType type;

    /**
     * 内链
     */
    private List<Link> links;

    @Data
    public static class Link {

        /**
         * seo表主键
         */
        private Long entityId;

        /**
         * 内链url
         */
        private String url;
    }

    public String findRandomUrl() {
        if (CollectionUtils.isEmpty(this.links)) {
            return null;
        }
        return this.links.get(ThreadLocalRandom.current().nextInt(links.size())).getUrl();
    }
}
