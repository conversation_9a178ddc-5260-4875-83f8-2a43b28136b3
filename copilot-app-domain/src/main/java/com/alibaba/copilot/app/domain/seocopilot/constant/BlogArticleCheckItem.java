package com.alibaba.copilot.app.domain.seocopilot.constant;

import lombok.Getter;

import java.math.BigDecimal;

/**
 * blog 文章检测规则量化指标
 */
@Getter
public enum BlogArticleCheckItem {
    /**
     * 没有文章
     */
    synced_opt(BigDecimal.ZERO, "Set up a new blog.", "Set up a new blog."),
    /**
     * 没有文章
     */
    no_article(BigDecimal.ZERO, "We recommend set up a new blog.", "We've crafted your blog using trending keywords."),
    /**
     * 文章没有body
     */
    no_body(BigDecimal.ZERO, "Blog is missing a body description", "We've crafted your blog using trending keywords."),
    /**
     * 文章长度
     */
    article_length(BigDecimal.valueOf(5), "Full text recommendation: 1,000-1,500 words.", "Your blog length has been optimized for reader-friendly experiences."),
    /**
     * 原创度
     */
    unique(BigDecimal.ZERO, "The blogs' plagiarism rate should be lower than 30%.", "We've enhanced your blog's originality through careful editing."),
    /**
     * 关键词密度1%-2%
     */
    keywords_density(BigDecimal.valueOf(15), "We suggest 1%-2%  keywords density.", "We've optimized keywords for increased relevance."),
    /**
     * 需要包含关键词
     */
    need_keywords(BigDecimal.valueOf(10), "Your blog requires additional keywords.", "Keywords are optimized for increased relevance."),
    /**
     * 包含图片
     */
    need_image(BigDecimal.valueOf(10), "Each blog's body should include one image at least.", "Suitable images have been integrated for better readability."),
    /**
     * 图片有alt标签
     */
    image_alt(BigDecimal.valueOf(25), "Each image should have one alt tag at least.", "Image alt tags have been fine-tuned for improved indexing."),
    /**
     * h1标签在顶部
     */
    h1_top(BigDecimal.valueOf(5), "We recommend localize H1 tag at the top.", "H tags in this blog have been optimized for better impact."),
    /**
     * 只能有一个h1标签
     */
    h1_count(BigDecimal.valueOf(20), "We recommend set up one tag for H1 tag.", "H tags in this blog have been optimized for better impact."),
    /**
     * h2标签3-5个
     */
    h2_count(BigDecimal.valueOf(10), "We recommend 3-5 tags for H2 tag.", "H tags in this blog have been optimized for better impact."),
    /**
     * 需要有h3标签
     */
    h3_count(BigDecimal.ZERO, "We recommend insert H3 tag.", "H tags in this blog have been optimized for better impact."),
    /**
     * 未设置title tag
     */
    no_title(BigDecimal.ZERO, "Blog title should be set.", "Blog title is optimized for improved relevance."),
    /**
     * title不包含关键词
     */
    title_no_keyword(BigDecimal.ZERO, "Blog title should include target keywords.", "Blog title is optimized for improved relevance."),
    /**
     * title tag长度应该≤70
     */
    title_length(BigDecimal.ZERO, "Blog titles should be no longer than 225 characters.", "Blog title is optimized for improved relevance.");

    /**
     * 比重
     */
    private BigDecimal weight;
    private String advice;
    private String conclusion;

    BlogArticleCheckItem(BigDecimal weight, String advice, String conclusion) {
        this.weight = weight;
        this.advice = advice;
        this.conclusion = conclusion;
    }

}
