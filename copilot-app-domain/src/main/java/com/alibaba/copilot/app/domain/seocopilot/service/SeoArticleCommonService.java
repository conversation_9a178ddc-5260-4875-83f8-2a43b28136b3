package com.alibaba.copilot.app.domain.seocopilot.service;


import com.alibaba.copilot.app.client.seocopilot.constant.RequestSourceEnum;
import com.alibaba.copilot.app.client.seocopilot.response.RelatedArticle;
import com.alibaba.copilot.app.domain.seocopilot.constant.LanguageEnum;
import com.alibaba.copilot.app.domain.seocopilot.dto.PublishArticleDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.unsplash.SearchPhotosResponse;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoBlogArticle;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShop;
import com.alibaba.copilot.app.domain.seocopilot.request.BlogArticleOptimizeRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.GenerateAltRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.unsplash.SearchPhotosRequest;

import java.util.List;

public interface SeoArticleCommonService {

    List<RelatedArticle> getRelatedArticles(SeoShop seoShop, SeoBlogArticle seoBlogArticle);


    Long translateArticle(LanguageEnum targetLanguage, Long shopId, Long articleId,
                          Long titleProposalId, Long bodyProposalId, Long tdkProposalId);

    PublishArticleDTO publishArticle(Long shopId, String categoryId, Long articleId, BlogArticleOptimizeRequest request, RequestSourceEnum source, Boolean userConfirmPublish);

    String shopifyEscalatingPrivileges(Long shopId);

    Boolean isNeedEscalatingPrivileges(Long shopId);

    Boolean autoTranslateRelatedArticles(Long shopId, Long articleId, Long titleProposalId, Long bodyProposalId, Long tdkProposalId);

    Boolean saveEditArticle(Long shopId, BlogArticleOptimizeRequest request, RequestSourceEnum source);

    String getSiteLang(Long shopId);

    Boolean verifyAndDeleteIfArticleMissing(Long shopId, Long articleId);

    String generateAlt(Long shopId, GenerateAltRequest request);

    SearchPhotosResponse selectPhotosFromUnsplash(Long shopId, SearchPhotosRequest request);

    String downloadPhotosFromUnsplash(Long shopId, String downloadLocation);
}
