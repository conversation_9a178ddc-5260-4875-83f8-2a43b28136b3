package com.alibaba.copilot.app.domain.seocopilot.constant;

import com.alibaba.copilot.boot.basic.enums.IEnum;

/**
 * SeoJobType
 *
 * <AUTHOR>
 * @date 2024/1/26 11:36 上午
 */
public enum SeoJobType implements IEnum<SeoJobType> {

    /**
     * 店铺绑定触发的任务
     */
    NEW_CUS,

    /**
     * 店铺绑定触发的任务
     */
    AUTHORIZE,

    /**
     * 全量任务
     */
    FULL,

    /**
     * 扫描任务
     */
    SCAN,

    /**
     * 诊断任务
     */
    CHECK,

    /**
     * 优化任务
     */
    OPTIMIZE,

    /**
     * 单项优化任务
     */
    SINGLE_OPTIMIZE,

    /**
     * 商品同步
     */
    PRODUCT_SYNC,

    /**
     * 博客同步
     */
    BLOG_SYNC,

    /**
     * collection同步
     */
    COLLECTION_SYNC,

    /**
     * 工作日志
     */
    SYS_LOG,

    /**
     * 店铺级商品关键词
     */
    SHOP_PRODUCT_KEYWORD,

    ARTICLE_GENERATE_MANUAL,

    ARTICLE_GENERATE_AUTO,

    GSC_SHOP_KEYWORD,

    SHOP_TOPIC,
}
