package com.alibaba.copilot.app.domain.seocopilot.constant;

import com.google.common.collect.Lists;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * 语言常量枚举
 */
@Getter
public enum LanguageEnum {


    Chinese_Simplified("Chinese (Simplified)", "zh-CN", "zh-CN", "zh-CN", Lists.newArrayList("zh_CN"), null, "zh", "中文（简体）"),
    Chinese_Traditional("Chinese (Traditional)", "zh-TW", "zh-TW", "zh-TW", Lists.newArrayList("zh_HK", "zh_TW"), null, "zh", "中文（繁体）"),
    Croatian("Croatian", "hr", "hr", "hr", Lists.newArrayList("hr"), "hr", "hr", "克罗地亚语"),
    Czech("Czech", "cs", "cs", "cs", Lists.newArrayList("cs_CZ"), "cz", "cs", "捷克语"),
    Danish("Danish", "da", "da", "da", Lists.newArrayList("da_DK"), null, "da", "丹麦语"),
    Dutch("Dutch", "nl", "nl", "nl", Lists.newArrayList("nl_NL", "nl_NL_formal", "nl_BE"), "nl", "nl", "荷兰语"),
    English("English", "en", "en", "en", Lists.newArrayList("en_AU", "en_ZA", "en_NZ", "en_GB", "en_CA", ""), "us", "en", "英语"),
    Finnish("Finnish", "fi", "fi", "fi", Lists.newArrayList("fi"), "fi", "fi", "芬兰语"),
    French("French", "fr", "fr", "fr", Lists.newArrayList("fr_FR", "fr_CA", "fr_BE"), "fr", "fr", "法语"),
    German("German", "de", "de", "de", Lists.newArrayList("de_AT", "de_CH", "de_CH_informal", "de_DE_formal", "de_DE"), "de", "de", "德语"),
    Greek("Greek", "el", "el", "el", Lists.newArrayList("el"), "gr", "el", "希腊文"),
    Hindi("Hindi", "hi", "hi", "hi", Lists.newArrayList("hi_IN"), null, "hi", "印地语"),
    Hungarian("Hungarian", "hu", "hu", "hu", Lists.newArrayList("hu_HU"), "hu", "hu", "匈牙利语"),
    Indonesian("Indonesian", "id", "id", "id", Lists.newArrayList("id_ID"), "id", "id", "印度尼西亚语"),
    Italian("Italian", "it", "it", "it", Lists.newArrayList("it_IT"), "it", "it", "意大利语"),
    Japanese("Japanese", "ja", "ja", "ja", Lists.newArrayList("ja"), "jp", "ja", "日语"),
    Korean("Korean", "ko", "ko", "ko", Lists.newArrayList("ko_KR"), "kr", "ko", "韩语"),
    Lithuanian("Lithuanian", "lt", "lt", "lt", Lists.newArrayList("lt_LT"), "lt", "lt", "立陶宛语"),
    Malay("Malay", "ms", "ms", "ms", Lists.newArrayList("ms_MY"), "my", "ms", "马来语"),
    Norwegian("Norwegian", "no", "no", "no", Lists.newArrayList("nn_NO", "nb_NO"), "no", "no", "挪威语"),
    Polish("Polish", "pl", "pl", "pl", Lists.newArrayList("pl_PL"), "pl", "pl", "波兰语"),
    Portuguese_Portugal("Portuguese (Portugal)", "pt-PT", "pt-PT", "pt", Lists.newArrayList("pt_BR", "pt_PT", "pt_PT_ao90", "pt_AO"), "pt", "pt", "葡萄牙语（葡萄牙）"),
    Portuguese_Brazil("Portuguese (Brazil)", "pt-BR", "pt-BR", "pt-br", Lists.newArrayList("pt_BR", "pt_PT", "pt_PT_ao90", "pt_AO"), "pt", "pt", "葡萄牙语（巴西）"),
    Romanian("Romanian", "ro", "ro", "ro", Lists.newArrayList("ro_RO"), "ro", "ro", "罗马尼亚语"),
    Russian("Russian", "ru", "ru", "ru", Lists.newArrayList("ru_RU"), "ru", "ru", "俄语"),
    Slovak("Slovak", "sk", "sk", "sk", Lists.newArrayList("sk_SK"), "sk", "sk", "斯洛伐克语"),
    Slovenian("Slovenian", "sl", "sl", "sl", Lists.newArrayList("sl_SI"), "si", "sl", "斯洛文尼亚语"),
    Spanish("Spanish", "es", "es", "es", Lists.newArrayList("es_AR", "es_CO", "es_MX", "es_ES", "es_VE", "es_CR", "es_EC", "es_DO", "es_PE", "es_UY", "es_PR", "es_GT", "es_CL"), "es", "es", "西班牙语"),
    Swedish("Swedish", "sv", "sv", "sv", Lists.newArrayList("sv_SE"), "se", "sv", "瑞典语"),
    Thai("Thai", "th", "th", "th", Lists.newArrayList("th"), "th", "th", "泰语"),
    Turkish("Turkish", "tr", "tr", "tr", Lists.newArrayList("tr_TR"), "tr", "tr", "土耳其语"),
    Vietnamese("Vietnamese", "vi", "vi", "vi", Lists.newArrayList("vi"), "vn", "vi", "越南语");


    /**
     * value
     */
    private final String value;

    /**
     * code
     */
    private final String code;

    /**
     * shopify code
     */
    private final String shopifyCode;

    /**
     * wordpress.com code
     */
    private final String wordpressComCode;

    /**
     * wordpress.org code
     */
    private final List<String> wordpressOrgCode;

    /**
     * semrush database code
     */
    private final String semrushCode;

    /**
     * algorithm code
     */
    private final String algorithmCode;

    /**
     * 描述
     */
    private final String desc;

    LanguageEnum(String value, String code, String shopifyCode, String wordpressComCode, List<String> wordpressOrgCode, String semrushCode, String algorithmCode, String desc) {
        this.value = value;
        this.code = code;
        this.shopifyCode = shopifyCode;
        this.wordpressComCode = wordpressComCode;
        this.wordpressOrgCode = wordpressOrgCode;
        this.semrushCode = semrushCode;
        this.algorithmCode = algorithmCode;
        this.desc = desc;
    }

    /**
     * 根据value获取对应的语言枚举
     *
     * @param value
     * @return
     */
    public static LanguageEnum fromValue(String value) {
        for (LanguageEnum language : LanguageEnum.values()) {
            if (language.getValue().equals(value)) {
                return language;
            }
        }
        throw new IllegalArgumentException("LanguageEnum.fromValue Invalid value: " + value);
    }

    /**
     * 根据code获取对应的语言枚举
     *
     * @param code
     * @return
     */
    public static LanguageEnum fromCode(String code) {
        for (LanguageEnum language : LanguageEnum.values()) {
            if (StringUtils.equalsIgnoreCase(code, language.getCode())) {
                return language;
            }
        }
        throw new IllegalArgumentException("LanguageEnum.fromCode Invalid code: " + code);
    }

    /**
     * 根据code获取对应的语言枚举
     *
     * @param code
     * @return
     */
    public static LanguageEnum fromAlgorithmCode(String code) {
        for (LanguageEnum language : LanguageEnum.values()) {
            if (StringUtils.equalsIgnoreCase(code, language.getAlgorithmCode())) {
                return language;
            }
        }
        return null;
    }

    /**
     * 根据code获取对应的语言枚举
     *
     * @param code
     * @return
     */
    public static LanguageEnum fromWordpressOrgCode(String code) {
        for (LanguageEnum language : LanguageEnum.values()) {
            if (language.getWordpressOrgCode().contains(code)) {
                return language;
            }
        }
        return null;
    }

    /**
     * 根据code获取对应的语言枚举
     *
     * @param code
     * @return
     */
    public static LanguageEnum fromWordpressComCode(String code) {
        for (LanguageEnum language : LanguageEnum.values()) {
            if (StringUtils.equals(code, language.getWordpressComCode())) {
                return language;
            }
        }
        return null;
    }

    /**
     * 根据code获取对应的语言枚举
     *
     * @param code
     * @return
     */
    public static LanguageEnum fromShopifyCode(String code) {
        for (LanguageEnum language : LanguageEnum.values()) {
            if (StringUtils.equals(code, language.getShopifyCode())) {
                return language;
            }
        }
        return null;
    }


    public static List<LanguageEnum> getAll() {
        return Lists.newArrayList(LanguageEnum.values());
    }
}
