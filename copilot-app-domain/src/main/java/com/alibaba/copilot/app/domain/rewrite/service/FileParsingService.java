package com.alibaba.copilot.app.domain.rewrite.service;

import com.alibaba.copilot.app.domain.rewrite.entity.FormattedText;
import java.io.IOException;
import java.util.List;

public interface FileParsingService {
    /**
     * 解析Word文档并保留格式
     * @param content Word文档的字节内容
     * @return 格式化的文本列表
     * @throws IOException 如果解析过程中发生IO错误
     */
    List<FormattedText> parseWordDocument(byte[] content) throws IOException;
    
    /**
     * 解析PDF文档并保留格式
     * @param content PDF文档的字节内容
     * @return 格式化的文本列表
     * @throws IOException 如果解析过程中发生IO错误
     */
    List<FormattedText> parsePdfDocument(byte[] content) throws IOException;

    /**
     * 将文档内容转换为纯文本格式
     * @param formattedTexts 格式化的文本列表
     * @return 纯文本格式的内容
     */
    String convertToPlainText(List<FormattedText> formattedTexts);

    /**
     * 将文档内容转换为Markdown格式
     * @param formattedTexts 格式化的文本列表
     * @return Markdown格式的文本
     */
    String convertToMarkdown(List<FormattedText> formattedTexts);

    /**
     * 直接从Word文档提取纯文本
     * @param content Word文档的字节内容
     * @return 纯文本内容
     * @throws IOException 如果解析过程中发生IO错误
     */
    default String extractPlainTextFromWord(byte[] content) throws IOException {
        return convertToPlainText(parseWordDocument(content));
    }

    /**
     * 直接从PDF文档提取纯文本
     * @param content PDF文档的字节内容
     * @return 纯文本内容
     * @throws IOException 如果解析过程中发生IO错误
     */
    default String extractPlainTextFromPdf(byte[] content) throws IOException {
        return convertToPlainText(parsePdfDocument(content));
    }

    /**
     * 直接从Word文档提取Markdown格式的文本
     * @param content Word文档的字节内容
     * @return Markdown格式的文本
     * @throws IOException 如果解析过程中发生IO错误
     */
    default String extractMarkdownFromWord(byte[] content) throws IOException {
        return convertToMarkdown(parseWordDocument(content));
    }

    /**
     * 直接从PDF文档提取Markdown格式的文本
     * @param content PDF文档的字节内容
     * @return Markdown格式的文本
     * @throws IOException 如果解析过程中发生IO错误
     */
    default String extractMarkdownFromPdf(byte[] content) throws IOException {
        return convertToMarkdown(parsePdfDocument(content));
    }
} 