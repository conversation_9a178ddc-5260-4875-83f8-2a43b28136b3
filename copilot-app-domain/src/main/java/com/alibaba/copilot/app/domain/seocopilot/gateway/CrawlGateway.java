package com.alibaba.copilot.app.domain.seocopilot.gateway;


import com.alibaba.copilot.app.domain.seocopilot.dto.CrawHtmlByUrlDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.RecallKeywordsByHtmlDTO;
import com.alibaba.copilot.app.domain.seocopilot.request.CrawHtmlByUrlRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.RecallKeywordsByHtmlRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

/**
 * 算法关键词TPP服务
 */
public interface CrawlGateway {

    /**
     * 根据url爬取页面html
     *
     * @param request
     * @return
     */
    SingleResult<CrawHtmlByUrlDTO> crawlHtmlByUrl(CrawHtmlByUrlRequest request);

}
