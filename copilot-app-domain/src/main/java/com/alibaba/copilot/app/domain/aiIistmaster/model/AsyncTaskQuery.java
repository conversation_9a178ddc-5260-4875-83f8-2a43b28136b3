package com.alibaba.copilot.app.domain.aiIistmaster.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * @desc:
 * @author: yixiao.cx
 * @create: 2023-06-27
 **/
@Setter
@Getter
@Builder
public class AsyncTaskQuery {
    /**
     * 任务ID
     */
    private Long taskId;
    /**
     * 所属店铺
     */
    private String shop;
    /**
     * 外部ID
     */
    private String outerId;
    /**
     * 任务Key(幂等用)
     */
    private String uniqueKey;
    /**
     * 任务类型
     */
    private String type;
    /**
     * 任务状态
     */
    private String status;
    /**
     * 已读状态
     */
    private String readStatus;
}
