package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.domain.seocopilot.model.ConversationMessage;

import java.util.List;

public interface ConversationMessageRepository {
    ConversationMessage getById(Long id);
    Long save(ConversationMessage conversationMessage);
    void saveBatch(List<ConversationMessage> conversationMessages);

    List<ConversationMessage> getMessageList(Long conversationId);
}
