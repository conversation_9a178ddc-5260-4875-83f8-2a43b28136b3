package com.alibaba.copilot.app.domain.seocopilot.constant;


import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CategoryMapping {
    public static final Map<String, List<String>> SHOPIFY_AE_CATEGORY_MAP = new HashMap<>();

    public static final Map<String, String> AE_SHOPIFY_CATEGORY_MAP = new HashMap<>();

    static {
        SHOPIFY_AE_CATEGORY_MAP.put("Animals & Pet supplies", Arrays.asList("Animals, Pets"));
        SHOPIFY_AE_CATEGORY_MAP.put("Arts & Entertainment", Arrays.asList("Sports & Entertainment"));
        SHOPIFY_AE_CATEGORY_MAP.put("Baby & Toddler", Arrays.asList("Mother & Kids"));
        SHOPIFY_AE_CATEGORY_MAP.put("Business & Industrial", Arrays.asList("Special Category"));
        SHOPIFY_AE_CATEGORY_MAP.put("Cameras & Optics", Arrays.asList("Cameras & Optics", "Electronic Components & Supplies"));
        SHOPIFY_AE_CATEGORY_MAP.put("Electronics", Arrays.asList("Consumer Electronics", "Electronic Components & Supplies"));
        SHOPIFY_AE_CATEGORY_MAP.put("Food, Beverages & Tobacco", Arrays.asList("Food", "Food & Special Category"));
        SHOPIFY_AE_CATEGORY_MAP.put("Furniture", Arrays.asList("Furniture"));
        SHOPIFY_AE_CATEGORY_MAP.put("Hardware", Arrays.asList("Automobiles, Parts & Accessories", "Motorcycle Equipments & Parts"));
        SHOPIFY_AE_CATEGORY_MAP.put("Health & Beauty", Arrays.asList("Beauty & Health"));
        SHOPIFY_AE_CATEGORY_MAP.put("Home & Garden", Arrays.asList("Home & Garden"));
        SHOPIFY_AE_CATEGORY_MAP.put("Luggage & Bags", Arrays.asList("Luggage & Bags"));
        SHOPIFY_AE_CATEGORY_MAP.put("Mature", Arrays.asList("Novelty & Special Use", "Special Category"));
        SHOPIFY_AE_CATEGORY_MAP.put("Media", Arrays.asList("Virtual Products"));
        SHOPIFY_AE_CATEGORY_MAP.put("Office Supplies", Arrays.asList("Office & School Supplies", "Computer & Office"));
        SHOPIFY_AE_CATEGORY_MAP.put("Religious & Ceremonial", Arrays.asList("Special Category", "Novelty & Special Use"));
        SHOPIFY_AE_CATEGORY_MAP.put("Software", Arrays.asList("Virtual Products", "Phones & Telecommunications"));
        SHOPIFY_AE_CATEGORY_MAP.put("Sporting Goods", Arrays.asList("Sports & Entertainment", "Sports Shoes", "Clothing & Accessories"));
        SHOPIFY_AE_CATEGORY_MAP.put("Toys & Games", Arrays.asList("Toys & Hobbies"));
        SHOPIFY_AE_CATEGORY_MAP.put("Vehicles & Parts", Arrays.asList("Motorcycle Equipments & Parts", "Automobiles, Parts & Accessories"));
        SHOPIFY_AE_CATEGORY_MAP.put("Gift Cards", Arrays.asList("Weddings & Events", "Special Category"));
        SHOPIFY_AE_CATEGORY_MAP.put("Apparel & Accessories", Arrays.asList("Apparel Accessories", "Jewelry & Accessories", "Sports Shoes", "Clothing & Accessories"));

        for (Map.Entry<String, List<String>> entry: SHOPIFY_AE_CATEGORY_MAP.entrySet()) {
            String key = entry.getKey();
            for (String value: entry.getValue()) {
                AE_SHOPIFY_CATEGORY_MAP.put(value, key);
            }
        }
    }
}