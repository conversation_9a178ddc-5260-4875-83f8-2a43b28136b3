package com.alibaba.copilot.app.domain.seocopilot.dto.shoplazza;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;

@Data
public class ShoplazzaShop {

    public String account;
    public String address1;
    public String address2;
    public String city;

    @J<PERSON>NField(name = "country_code")
    public String countryCode;

    @JSONField(name = "created_at")
    public Date createdAt;
    public String currency;

    @JSONField(name = "customer_email")
    public String customerEmail;
    public String domain;
    public String email;
    public Icon icon;
    public Long id;
    public String name;
    public String phone;

    @J<PERSON>NField(name = "primary_locale")
    public String primaryLocale;

    @<PERSON><PERSON><PERSON><PERSON>(name = "province_code")
    public String provinceCode;

    @J<PERSON>NField(name = "root_url")
    public String rootUrl;

    @J<PERSON><PERSON>ield(name = "shop_owner")
    public String shopOwner;
    public String state;

    @JSONField(name = "system_domain")
    public String systemDomain;

    public String timezone;

    @J<PERSON><PERSON>ield(name = "updated_at")
    public Date updatedAt;
    public String zip;

    @Data
    public static class Icon {
        public String alt;
        public String path;
        public String src;
    }
}
