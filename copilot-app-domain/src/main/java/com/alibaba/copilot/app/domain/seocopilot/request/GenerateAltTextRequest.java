package com.alibaba.copilot.app.domain.seocopilot.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

@Accessors(chain = true )
@Data
public class GenerateAltTextRequest {
    /**
     * 产品的标题，通常包含产品的详细描述和关键特性
     */
    private String productTitle;
    /**
     * 字符串	产品图片的 URL 链接
     */
    private String imageUrl;
    /**
     * 语言代码，用于指定生成文本的语言，例如 "en" 表示英语
     */
    private String languageCode;
    private List<String> properNouns;
    private String originalAlt;
    private List<String> focusKeywordList;
}
