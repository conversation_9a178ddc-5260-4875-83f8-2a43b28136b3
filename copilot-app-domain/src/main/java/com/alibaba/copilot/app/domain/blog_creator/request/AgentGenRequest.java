package com.alibaba.copilot.app.domain.blog_creator.request;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Accessors(chain=true)
public class AgentGenRequest {
    private String system;
    private List<Map<String, String>> messages;
    private Map<String, Object> evaluation = new HashMap<String, Object>() {{
        put("enable", false);
    }};
    private Map<String, Object> segment = new HashMap<String, Object>() {{
        put("enable", false);
        put("intro_prompt", "");
        put("model", "gpt-4o");
    }};
    private Map<String, Object> humanize = new HashMap<String, Object>() {{
        put("enable", false);
    }};
    private Map<String, Object> option = new HashMap<String, Object>() {{
        put("enable", false);
        put("version_count", 5);
    }};

}
