package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

/**
 * KeywordGenDTO
 */
@Data
public class KeywordGenDTO {
    /**
     * keyword
     */
    private String keyword;

    /**
     * kd
     */
    private String kd;

    /**
     * searchVolume
     */
    @JSONField(name = "search_volume")
    private String searchVolume;

    /**
     * cpc
     */
    private String cpc;

    /**
     * intent
     */
    private String intent;

    /**
     * qualityScore
     */
    @JSONField(name = "quality_score")
    private String qualityScore;

    /**
     * position
     */
    private String position;

    /**
     * traffic
     */
    private String traffic;

    /**
     * ctr
     */
    private String ctr;

    /**
     * clicks
     */
    private String clicks;

    /**
     * impressions
     */
    private String impressions;

    /**
     * rank_score
     */
    @JSONField(name = "rank_score")
    private String rankScore;
}
