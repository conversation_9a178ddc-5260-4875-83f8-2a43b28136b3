package com.alibaba.copilot.app.domain.seocopilot.gateway;


import com.alibaba.copilot.app.domain.seocopilot.dto.*;
import com.alibaba.copilot.app.domain.seocopilot.request.*;
import com.alibaba.copilot.boot.basic.result.SingleResult;

import java.util.List;
import java.util.Map;

/**
 * 算法关键词TPP服务
 */
public interface AlgorithmKeywordTppGateway {

    /**
     * 根据页面内容生成关键词
     * 输入参数：
     * api_nameseo_keyword_recall（必填）
     * src_biz_namexxx（必填，目前为空字符串即可）
     * scenexxx（目前没有，为空字符串""，非必填）
     * data{"html_content": xx //string } （必填）
     * <p>
     * 输出：
     * "data": {
     * "keyword_list": [{"keyword":keyword_1, "kd":kd_1, "search_volume":volume_1, "cpc":cpc_1, "intent": intent_1}, {"keyword":keyword_2, "kd":kd_2, "search_volume":volume_2, "cpc":cpc_2, "intent": intent_2}, {"keyword":keyword_3, "kd":kd_3, "search_volume":volume_3, "cpc":cpc_3, "intent": intent_3}]
     * "main_content": string,
     * "model_predict": [pre_keyword_1, pre_keyword_2, pre_keyword_3]
     * }
     *
     * @param request
     * @return
     */
    SingleResult<RecallKeywordsByHtmlDTO> recallKeywordsByHtml(RecallKeywordsByHtmlRequest request);

    /**
     * 关键词拓词服务
     * 输入参数：
     * api_name seo_keyword_extension（必填）
     * src_biz_name xxx（必填，目前为空字符串即可）
     * scene xxx（目前没有，为空字符串""，非必填）
     * data{"max_return_num": xx // int，最大的返回个数"thresh": xx // float，相关性阈值"topic": [xx_1, xx_2, xx_3] // list，必传，必须要有一个，为保证耗时长度不要超过100 } （必填）
     * <p>
     * 输出：
     * "data": [
     * {"topic": xx_1, "keyword_list": [{"keyword": , "kd":, "search_volumn":, "cpc":, "intent": }, {"keyword": , "kd":, "search_volumn":, "cpc":, "intent"}]},
     * {"topic": xx_2, "keyword_list": [{"keyword": , "kd":, "search_volumn":, "cpc":, "intent": }, {"keyword": , "kd":, "search_volumn":, "cpc":, "intent"}]}
     * ]
     *
     * @param request
     * @return
     */
    SingleResult<List<ExtendKeywordsDTO>> extendKeywords(ExtendKeywordsRequest request);

    /**
     * 关键词拓词服务
     * 输入参数：
     * api_name seo_keyword_extension（必填）
     * src_biz_name xxx（必填，目前为空字符串即可）
     * scene xxx（目前没有，为空字符串""，非必填）
     * data{"max_return_num": xx // int，最大的返回个数"thresh": xx // float，相关性阈值"topic": [xx_1, xx_2, xx_3] // list，必传，必须要有一个，为保证耗时长度不要超过100 } （必填）
     * <p>
     * 输出：
     * "data": [
     * {"topic": xx_1, "keyword_list": [{"keyword": , "kd":, "search_volumn":, "cpc":, "intent": }, {"keyword": , "kd":, "search_volumn":, "cpc":, "intent"}]},
     * {"topic": xx_2, "keyword_list": [{"keyword": , "kd":, "search_volumn":, "cpc":, "intent": }, {"keyword": , "kd":, "search_volumn":, "cpc":, "intent"}]}
     * ]
     *
     * @param request
     * @return
     */
    SingleResult<ReRankKeywordsDTO> reRankKeywords(ReRankKeywordsRequest request);

    /**
     * 小语种关键词清洗
     */
    SingleResult<CleanKeywordsDTO> cleanKeywords(CleanKeywordsRequest request);

    /**
     * 语种识别
     */
    SingleResult<DetectKeywordLanguageDTO> detectKeywordLanguage(DetectKeywordLanguageRequest request);

    /**
     * 关键词聚类
     */
    SingleResult<Map<String, List<String>>> clusterKeywords(ClusterKeywordsRequest request);
}
