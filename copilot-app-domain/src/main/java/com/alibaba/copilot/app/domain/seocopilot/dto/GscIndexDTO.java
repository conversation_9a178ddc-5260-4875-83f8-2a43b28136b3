package com.alibaba.copilot.app.domain.seocopilot.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/25
 * @link https://developers.google.com/webmaster-tools/v1/urlInspection.index/inspect?hl=zh-cn
 */
@Data
public class GscIndexDTO {
    private UrlInspectionResult inspectionResult;

    @Data
    public static class UrlInspectionResult {

        private String inspectionResultLink;

        private IndexStatusInspectionResult indexStatusResult;

        private AmpInspectionResult ampResult;

        private MobileUsabilityInspectionResult mobileUsabilityResult;

        private RichResultsInspectionResult richResultsResult;
    }

    @Data
    public static class IndexStatusInspectionResult {
        private List<String> sitemap;

        private List<String> referringUrls;

        private String verdict;

        private String coverageState;

        private String robotsTxtState;

        private String indexingState;

        private String lastCrawlTime;

        private String pageFetchState;

        private String googleCanonical;

        private String userCanonical;

        private String crawledAs;
    }

    @Data
    public static class AmpInspectionResult {
        private List<AmpIssue> issues;

        private String verdict;

        private String ampUrl;

        private String robotsTxtState;

        private String indexingState;

        private String ampIndexStatusVerdict;

        private String lastCrawlTime;

        private String pageFetchState;
    }

    @Data
    public static class MobileUsabilityInspectionResult {
        private List<MobileUsabilityIssue> issues;

        private String verdict;
    }

    @Data
    public static class RichResultsInspectionResult {

        private List<DetectedItems> detectedItems;


        private String verdict;
    }

    @Data
    public static class AmpIssue {
        private String issueMessage;

        private String severity;
    }

    @Data
    public static class MobileUsabilityIssue {
        private String issueType;

        private String severity;

        private String message;
    }

    @Data
    public static class DetectedItems {
        private String richResultType;

        private List<Item> items;
    }

    @Data
    public static class Item {
        private String name;

        private List<RichResultsIssue> issues;
    }

    @Data
    public static class RichResultsIssue {
        private String issueMessage;

        private String severity;
    }
}
