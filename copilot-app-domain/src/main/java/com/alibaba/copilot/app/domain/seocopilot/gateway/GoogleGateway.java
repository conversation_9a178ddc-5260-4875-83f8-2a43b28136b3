package com.alibaba.copilot.app.domain.seocopilot.gateway;

import com.alibaba.copilot.app.domain.seocopilot.dto.*;
import com.alibaba.copilot.app.domain.seocopilot.request.GscIndexRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.GscQueryRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.OauthRefreshTokenRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.OauthTokenRequest;

import java.util.List;

/**
 * Google 服务
 */
public interface GoogleGateway {

    /**
     * Google token
     */
    OauthTokenDTO token(OauthTokenRequest request);

    /**
     * Google token refresh
     */
    OauthRefreshTokenDTO refreshToken(OauthRefreshTokenRequest request);

    /**
     * Google Search Console-query
     */
    GscQueryDTO query(String accessToken, String siteUrl, GscQueryRequest request, Integer maxRetry);

    /**
     * Google Search Console-index
     */
    GscIndexDTO index(String accessToken, GscIndexRequest request);

    /**
     * 用户信息
     *
     * @param accessToken
     * @return
     */
    UserInfoDTO getUserInfo(String accessToken);

    /**
     * 根据关键词列表查询过去30天的GSC数据
     */
    GscQueryDTO queryGSCDataByKeywords(String token, String siteUrl, List<String> keywords);

    /**
     * 提交sitemap
     *
     * @param token    Oauth token
     * @param siteUrl  资源网站
     * @param feedPath 站点地图
     * @return
     */
    Boolean submitSitemap(String token, String siteUrl, String feedPath);

}
