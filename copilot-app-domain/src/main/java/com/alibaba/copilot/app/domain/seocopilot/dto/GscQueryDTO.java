package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2023/9/25
 * @link https://developers.google.com/webmaster-tools/v1/searchanalytics/query?hl=zh-cn
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GscQueryDTO {

    private List<Row> rows;

    private String responseAggregationType;

    /**
     * 数据总量，接口未返回该字段，不要用这个字段判断数据条数，仅方便缓存使用
     */
    private Long rowCount;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Row {
        private List<String> keys;

        private double clicks;

        private double impressions;

        private double ctr;

        private double position;

        public Row(String key) {
            this.keys = new ArrayList<>();
            this.keys.add(key);
            this.clicks = 0;
            this.impressions = 0;
            this.ctr = 0;
            this.position = 0;
        }
    }

    public static String toJsonString(GscQueryDTO gscQueryDTO) throws JsonProcessingException {
        // 缩减rows到最多1000条，防止缓存过大
        if (gscQueryDTO.getRows() != null && gscQueryDTO.getRows().size() > 1000) {
            gscQueryDTO.setRows(gscQueryDTO.getRows().stream()
                    .limit(1000)
                    .collect(Collectors.toList()));
        }

        // 使用Jackson库将对象转换为JSON字符串
        ObjectMapper mapper = new ObjectMapper();
        return mapper.writeValueAsString(gscQueryDTO);
    }

    public static GscQueryDTO fromJsonString(String dataJson) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        return mapper.readValue(dataJson, GscQueryDTO.class);
    }
}
