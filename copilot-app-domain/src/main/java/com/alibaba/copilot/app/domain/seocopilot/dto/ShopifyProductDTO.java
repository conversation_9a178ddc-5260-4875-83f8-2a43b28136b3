package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedList;
import java.util.List;

/**
 * ShopifyProductDTO
 *
 * <AUTHOR>
 * @date 2024/7/8 4:06 下午
 */
@Data
public class ShopifyProductDTO {

    /**
     * 商品id
     */
    private Long id;

    /**
     * 商品标题
     */
    private String title;

    /**
     * handle
     */
    private String handle;

    /**
     * 商品所属类型
     */
    @JSONField(name = "product_type")
    private String productType;

    /**
     * 商品详情描述
     */
    @JSONField(name = "body_html")
    private String bodyHtml;

    /**
     * 制造商
     */
    private String vendor;

    /**
     * 商品标签
     */
    private String tags;

    /**
     * 商品sku属性
     */
    private List<OptionDTO> options = new LinkedList<>();

    /**
     * 包含产品图像的列表
     */
    private List<ImageDTO> images = new LinkedList<>();

    /**
     * sku列表
     */
    private List<ShopifyVariantDTO> variants = new LinkedList<>();

    /**
     * 发布时间
     */
    @JSONField(name = "published_at")
    private String publishedAt;

    /**
     * 更新时间
     */
    @JSONField(name = "updated_at")
    private String updatedAt;

    /**
     * 是否发布
     */
    private Boolean published;

    /**
     * 状态
     */
    private String status;

    public Boolean isPublished() {
        return (published == null) ? StringUtils.isNotBlank(publishedAt) : published;
    }

    public ImageDTO getImageByImageId(String imageId) {
        if (CollectionUtils.isEmpty(images) || StringUtils.isBlank(imageId)) {
            return null;
        }
        return images.stream().filter(o -> imageId.equals(o.getId())).findFirst().orElse(null);
    }

    public ImageDTO getImageByVariantId(String variantId) {
        if (CollectionUtils.isEmpty(images) || StringUtils.isBlank(variantId)) {
            return null;
        }
        return images.stream().filter(o -> CollectionUtils.isNotEmpty(o.getVariantIds()) && o.getVariantIds().contains(variantId)).findFirst().orElse(null);
    }
}
