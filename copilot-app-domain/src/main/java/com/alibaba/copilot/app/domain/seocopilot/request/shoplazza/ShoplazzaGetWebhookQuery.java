package com.alibaba.copilot.app.domain.seocopilot.request.shoplazza;

import com.alibaba.copilot.boot.basic.request.MapQuery;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

@Data
public class ShoplazzaGetWebhookQuery implements MapQuery {

    private String address;

    private String topic;

    @J<PERSON><PERSON>ield(name = "created_at_min")
    private String createdAtMin;

    @JSONField(name = "created_at_max")
    private String createdAtMax;

    @JSONField(name = "updated_at_min")
    private String updatedAtMin;

    @JSONField(name = "updated_at_max")
    private String updatedAtMax;

    private Integer limit;

    private Integer page;

}
