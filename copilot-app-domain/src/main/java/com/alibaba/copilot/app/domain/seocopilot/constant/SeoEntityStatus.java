package com.alibaba.copilot.app.domain.seocopilot.constant;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum SeoEntityStatus {

    /**
     * 待诊断 展示单列
     */
    INIT("Init"),
    /**
     * 诊断中 展示单列
     */
    CHECKING("Checking"),
    /**
     * Check Fail
     */
    CHECK_FAIL("Check Fail"),
    /**
     * 无需优化 展示单列（已经收录）
     */
    NO_OPTIMIZE_NEEDED("No Optimization Needed"),
    /**
     * 无需优化 展示单列(14天逻辑)
     */
    PAGE_WAIT_INDEX("Google Audit"),
    /**
     * 有诊断建议
     */
    NEEDS_OPTIMIZE("New Suggestions"),
    /**
     * 生成中
     */
    GENERATING("Generating"),
    /**
     * 优化中
     */
    OPTIMIZING("Optimizing"),
    /**
     * 生成完成
     */
    COMPLETED("Awaiting Application"),
    /**
     * 优化失败
     */
    OPTIMIZE_FAIL("Optimize fail"),
    /**
     * 生成失败
     */
    GENERATE_FAIL("Generate fail"),
    /**
     * 应用失败
     */
    APPLY_FAIL("Apply fail"),

    /**
     * 不展示
     */
    HIDDEN("Hidden"),
    /**
     * 已完成 apply后
     */
    APPLIED("Optimized");

    private String value;

    public static SeoEntityStatus fromValue(String value) {
        for (SeoEntityStatus status : SeoEntityStatus.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid value: " + value);
    }

    public static boolean canChecking(SeoEntityStatus status) {
        return !SeoEntityStatus.NO_OPTIMIZE_NEEDED.equals(status)
                && !SeoEntityStatus.COMPLETED.equals(status)
                && !SeoEntityStatus.OPTIMIZING.equals(status)
                && !SeoEntityStatus.APPLIED.equals(status)
                && !SeoEntityStatus.PAGE_WAIT_INDEX.equals(status);
    }

    public static boolean canBlogCheck(SeoEntityStatus status) {
        return !SeoEntityStatus.GENERATING.equals(status)
                && !SeoEntityStatus.OPTIMIZING.equals(status)
                && !SeoEntityStatus.NO_OPTIMIZE_NEEDED.equals(status)
                && !SeoEntityStatus.PAGE_WAIT_INDEX.equals(status)
                && !SeoEntityStatus.APPLIED.equals(status);
    }

    public static boolean canOptimizing(SeoEntityStatus status) {
        return !SeoEntityStatus.INIT.equals(status) && !SeoEntityStatus.CHECKING.equals(status);
    }

    public static List<String> translate(String value) {
        if (StringUtils.isBlank(value)) {
            return new ArrayList<>();
        }

        SeoEntityStatus status = fromValue(value);
        if (status == NO_OPTIMIZE_NEEDED || status == APPLIED ) {
            return Lists.newArrayList(NO_OPTIMIZE_NEEDED.name(), APPLIED.name());
        }
        return Lists.newArrayList(status.name());
    }
}
