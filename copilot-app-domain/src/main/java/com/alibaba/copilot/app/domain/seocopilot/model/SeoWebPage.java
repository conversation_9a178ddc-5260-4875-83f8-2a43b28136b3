package com.alibaba.copilot.app.domain.seocopilot.model;


import com.alibaba.copilot.app.domain.seocopilot.constant.SeoWebPageType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;

/**
 * seo_web_page 实体
 *
 * <AUTHOR>
 */
@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class SeoWebPage {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * seo copilot店铺ID
     */
    private Long shopId;

    /**
     * 批次id
     */
    private String batchId;

    /**
     * shopify店铺域名
     */
    private String shopDomain;

    /**
     * 页面URL
     */
    private String pageUrl;

    /**
     * 页面类型
     */
    private SeoWebPageType type;

    /**
     * PC SEO分数
     */
    private BigDecimal pcSeoScore;

    /**
     * Mobile SEO分数
     */
    private BigDecimal mobileSeoScore;

    /**
     * 店铺收录率
     */
    private BigDecimal shopIndex;

    /**
     * 关键词排名
     */
    private BigDecimal keywordRank;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 扩展字段
     */
    private SeoWebPageAttributes attributes = new SeoWebPageAttributes(null);
}
