package com.alibaba.copilot.app.domain.seocopilot.request;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/9/25
 * @link https://developers.google.com/webmaster-tools/v1/searchanalytics/query?hl=zh-cn
 */
@Data
@Builder
public class GscQueryRequest {

    private String startDate;

    private String endDate;

    private List<String> dimensions;

    private String type;

    private List<DimensionFilterGroup> dimensionFilterGroups;

    private String aggregationType;

    private Integer rowLimit;

    private Integer startRow;

    private String dataState;

    @Data
    @Builder
    public static class DimensionFilterGroup {

        private String groupType;

        private List<Filter> filters;
    }

    @Data
    @Builder
    public static class Filter {

        private String dimension;

        private String operator;

        private String expression;
    }
}
