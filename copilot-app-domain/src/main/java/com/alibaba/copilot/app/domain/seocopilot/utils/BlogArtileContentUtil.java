package com.alibaba.copilot.app.domain.seocopilot.utils;

import com.alibaba.copilot.app.client.seocopilot.response.MultipleInteractionDTO;
import com.alibaba.copilot.app.domain.seocopilot.model.Image;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoBlogArticle;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoBlogArticleAttributes;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoCheckRecord;
import com.alibaba.copilot.app.domain.seocopilot.request.BlogContentAiPromptRequest;
import com.alibaba.fastjson.JSON;
import com.vladsch.flexmark.ext.tables.TablesExtension;
import com.vladsch.flexmark.html.HtmlRenderer;
import com.vladsch.flexmark.parser.Parser;
import com.vladsch.flexmark.util.ast.Node;
import com.vladsch.flexmark.util.data.MutableDataSet;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.nodes.TextNode;
import org.jsoup.select.Elements;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class BlogArtileContentUtil {

    /**
     * markdown 转为 html
     *
     * @param markdown
     * @return
     */
    public static String markdown2html(String markdown) {
        if (StringUtils.isNotBlank(markdown)) {
            markdown = markdown.replace("\n", "\n\n");
        }
        MutableDataSet options = new MutableDataSet();
        options.set(Parser.EXTENSIONS, Arrays.asList(TablesExtension.create()));
        Parser parser = Parser.builder(options).build();
        HtmlRenderer renderer = HtmlRenderer.builder(options).build();
        Node document = parser.parse(markdown);
        String html = renderer.render(document);
        Document doc = Jsoup.parse(html, "UTF-8");
        Elements lis = doc.select("li");
        lis.forEach(li -> {
            Element element = li.firstElementChild();
            if (element != null && element.is("p")) {
                element.unwrap();
            }
        });
        Element body = doc.body();
        Element style = body.appendElement("style");
        style.text("h1, h2, h3, h4, h5, h6 { font-size: 20px; line-height: 30px; } p { font-size: 16px; line-height: 24px; margin-bottom: 16px; } img { margin: 16px 0; }");
        log.info("markdown2html, markdown: {} ; html: {}", markdown, body.html());
        return body.html();
    }

    /**
     * markdown 转为 html(无style)
     *
     * @param markdown
     * @return
     */
    public static String markdown2htmlWithoutStyle(String markdown) {
        if (StringUtils.isNotBlank(markdown)) {
            markdown = markdown.replace("\n", "\n\n");
        }
        MutableDataSet options = new MutableDataSet();
        options.set(Parser.EXTENSIONS, Arrays.asList(TablesExtension.create()));
        Parser parser = Parser.builder(options).build();
        HtmlRenderer renderer = HtmlRenderer.builder(options).build();
        Node document = parser.parse(markdown);
        String html = renderer.render(document);
        Document doc = Jsoup.parse(html, "UTF-8");
        Elements lis = doc.select("li");
        lis.forEach(li -> {
            Element element = li.firstElementChild();
            if (element != null && element.is("p")) {
                element.unwrap();
            }
        });
        Element body = doc.body();
        String htmlBody = body.html();
        log.info("markdown2html, markdown: {} ; html: {}", markdown, htmlBody);
        return htmlBody;
    }

    /**
     * 从html中提取图片。包括 url 和 alt 标签
     *
     * @param html
     * @return
     */
    public static List<Image> extractImages(String html) {
        List<Image> result = new ArrayList<>();
        Document doc = Jsoup.parse(html, "UTF-8");
        // 获取所有图片元素
        Elements images = doc.select("img[src~=(?i)\\.(png|jpe?g|gif)]");
        for (Element image : images) {
            String src = image.attr("abs:src");
            if (result.stream().anyMatch(img -> StringUtils.equals(img.getUrl(), src))) {
                continue;
            }
            String alt = image.attr("alt");
            Image img = new Image();
            img.setUrl(src);
            img.setAlt(alt);
            result.add(img);
        }
        return result;
    }

    /**
     * 在每个段落后插入图片，直到没有图片
     *
     * @param html
     * @param url
     * @return
     */
    public static String mergeTextAndImages(String html, List<String> alts, List<String> url) {
        if (CollectionUtils.isEmpty(url)) {
            return html;
        }
        Document doc = Jsoup.parse(html, "UTF-8");
        int index = 0;
        for (Element paragraph : doc.select("p")) {
            if (index >= url.size()) {
                break;
            }
            String alt = "image";
            if (index < CollectionUtils.size(alts)) {
                alt = alts.get(index);
            }
            paragraph.after(String.format("<img src='%s' alt='%s' style='display: block; margin: 0 auto;' />", url.get(index), alt));
            index += 1;
        }
        return doc.body().html();
    }

    /**
     * 在H2后面的第一个段落后插入图片，直到没有图片
     *
     * @param html
     * @param keywords
     * @param images
     * @return
     */
    public static String mergeTextAndImagesInH2(String html, List<String> keywords, List<String> images) {
        if (CollectionUtils.isEmpty(images)) {
            return html;
        }
        String alt = "Image";
        if (!CollectionUtils.isEmpty(keywords)) {
            alt = keywords.get(0);
        }
        Document doc = Jsoup.parse(html, "UTF-8");
        int index = 0;

        Elements h2Elements = doc.select("h2");
        for (Element h2 : h2Elements) {
            if (index >= images.size()) {
                break;
            }
            Element nextSibling = h2.nextElementSibling();
            while (nextSibling != null && !nextSibling.tagName().equals("p")) {
                nextSibling = nextSibling.nextElementSibling();
            }
            if (nextSibling != null && nextSibling.tagName().equals("p")) {
                nextSibling.after(String.format("<img src='%s' alt='%s' style='display: block; margin: 0 auto;' />", images.get(index), alt));
                index += 1;
            }
        }
        return doc.body().html();
    }


    /**
     * 特定H2插入图片
     * @param html
     * @param alts
     * @param images
     * @return
     */
    public static String mergeTextAndImagesInSpecialH2(String html, List<String> alts, Map<String, String> images) {
        if (images == null || images.isEmpty()) {
            return html;
        }

        Document doc = Jsoup.parse(html, "UTF-8");
        Elements h2Elements = doc.select("h2");

        int altIndex = 0;

        for (Element h2 : h2Elements) {
            String h2Text = h2.text();
            if (images.containsKey(h2Text)) {
                String alt = "image";
                if (altIndex < CollectionUtils.size(alts)) {
                    alt = alts.get(altIndex);
                }

                Element nextSibling = h2.nextElementSibling();
                boolean imageInserted = false;

                while (nextSibling != null) {
                    if ("p".equals(nextSibling.tagName())) {
                        nextSibling.after(String.format("<img src='%s' alt='%s' style='display: block; margin: 0 auto;' />", images.get(h2Text), alt));
                        imageInserted = true;
                        break;
                    }
                    nextSibling = nextSibling.nextElementSibling();
                }

                if (!imageInserted) {
                    h2.after(String.format("<img src='%s' alt='%s' style='display: block; margin: 0 auto;' />", images.get(h2Text), alt));
                }
                altIndex++;
            }
        }
        return doc.body().html();
    }

    /**
     * 关键词加入内链
     *
     * @param html
     * @param url
     * @return
     */
    public static String addInnerLink(String html, List<String> keywords, String url) {
        log.info("BlogArtileContentUtil.addInnerLink, html={}, keywords={}, url={}", html, JSON.toJSONString(keywords), url);

        if (StringUtils.isBlank(url)) {
            return html;
        }

        Document doc = Jsoup.parse(html, "UTF-8");
        for (String keyword : keywords) {
            if (StringUtils.isBlank(keyword)) {
                continue;
            }
            List<String> forms = Arrays.asList(keyword, convertPlural(keyword));
            forms.forEach(form -> {
                Pattern pattern = Pattern.compile("(" + Pattern.quote(form) + ")", Pattern.CASE_INSENSITIVE);
                for (Element p : doc.select("p")) {
                    for (TextNode textNode : p.textNodes()) {
                        Matcher matcher = pattern.matcher(textNode.getWholeText());
                        if (matcher.find()) {
                            Element a = new Element("a");
                            a.attr("href", addHttpPrefixIfNeeded(url));
                            a.attr("target", "_blank");
                            a.text(matcher.group(1));  // use the matched text
                            int start = matcher.start();
                            TextNode after = textNode.splitText(start);
                            after.splitText(matcher.group(1).length());
                            after.replaceWith(a);
                            break;
                        }
                    }
                }
            });
        }

        String result = doc.body().html();
        log.info("BlogArtileContentUtil.addInnerLink, result={}", result);
        return result;
    }

    public static String addHttpPrefixIfNeeded(String url) {
        if (StringUtils.isNotBlank(url) && !url.toLowerCase().startsWith("http://") && !url.toLowerCase().startsWith("https://")) {
            return "https://" + url;
        }
        return url;
    }

    /**
     * 博客锚文本插入
     *
     * @param html
     * @param anchorCount
     * @return
     */
    public static String insertInternalLink(String html, List<Pair<String, String>> wordUrlPairs, int anchorCount) {
        log.info("BlogArtileContentUtil.insertInternalLink, html={}, wordUrlPairs={},  anchorCount={}", html, JSON.toJSONString(wordUrlPairs), anchorCount);

        if (CollectionUtils.isEmpty(wordUrlPairs)) {
            return html;
        }

        Document doc = Jsoup.parse(html, "UTF-8");
        int count = 0;
        for (Pair<String, String> pair: wordUrlPairs) {
            String keyword = pair.getLeft();
            Pattern pattern = Pattern.compile("(" + Pattern.quote(keyword) + ")", Pattern.CASE_INSENSITIVE);
            Boolean flag = false;
            if (count >= anchorCount) {
                log.info("BlogArtileContentUtil.insertInternalLink, quantity up to standard, anchorCount={}", anchorCount);
                break;
            }
            for (Element p : doc.select("p")) {
                for (TextNode textNode : p.textNodes()) {
                    Matcher matcher = pattern.matcher(textNode.getWholeText());
                    if (matcher.find()) {
                        Element a = new Element("a");
                        a.attr("href", addHttpPrefixIfNeeded(pair.getRight()));
                        a.attr("target", "_blank");
                        a.text(matcher.group(1));  // use the matched text
                        int start = matcher.start();
                        TextNode after = textNode.splitText(start);
                        after.splitText(matcher.group(1).length());
                        after.replaceWith(a);
                        flag = true;
                        count++;
                        break;
                    }
                }
                if (flag) {
                    break;
                }
            }
        }

        String result = doc.body().html();
        log.info("BlogArtileContentUtil.insertInternalLink, result={}", result);
        return result;
    }

    /**
     * 把大纲按顺序解析成包含h2 h3的list
     * 20240129 update 如果某个h2下面没有h3，单独作为一个item
     */
    public static List<BlogContentAiPromptRequest.CurrentOutline> parseH2H3ListFromMarkdown(String outline) {
        List<BlogContentAiPromptRequest.CurrentOutline> result = new ArrayList<>();

        // 匹配 H2 和 H3 标题
        String pattern = "(?m)^## (.+)$|(?m)^### (.+)$";
        Pattern regex = Pattern.compile(pattern);
        Matcher matcher = regex.matcher(outline);

        String latestH2 = null;
        boolean noH3Item = false;
        while (matcher.find()) {
            String h2 = matcher.group(1);
            String h3 = matcher.group(2);

            if (h2 != null) {
                if (latestH2 != null && noH3Item) {
                    //只有H2，没有H3
                    BlogContentAiPromptRequest.CurrentOutline currentOutline = new BlogContentAiPromptRequest.CurrentOutline();
                    currentOutline.setCurrentOutlineH2(latestH2);
                    result.add(currentOutline);
                }
                latestH2 = h2;
                noH3Item = true;
            } else if (h3 != null && latestH2 != null) {
                noH3Item = false;
                BlogContentAiPromptRequest.CurrentOutline currentOutline = new BlogContentAiPromptRequest.CurrentOutline();
                currentOutline.setCurrentOutlineH2(latestH2);
                currentOutline.setCurrentOutlineH3(h3);
                result.add(currentOutline);
            }
        }
        if (latestH2 != null && noH3Item) {
            //只有H2，没有H3
            BlogContentAiPromptRequest.CurrentOutline currentOutline = new BlogContentAiPromptRequest.CurrentOutline();
            currentOutline.setCurrentOutlineH2(latestH2);
            result.add(currentOutline);
        }
        return result;
    }

    /**
     * 根据大纲把生成的段落按顺序拼接成整篇文章
     */
    public static String buildArticleContent(Map<String, String> content, List<BlogContentAiPromptRequest.CurrentOutline> outline) {
        StringBuilder result = new StringBuilder();
        String currentH2 = null;
        for (BlogContentAiPromptRequest.CurrentOutline currentOutline : outline) {
            String key = currentOutline.getCurrentOutlineH2();
            if (currentOutline.getCurrentOutlineH3() != null) {
                key = currentOutline.getCurrentOutlineH2() + "|||" + currentOutline.getCurrentOutlineH3();
            }
            String p = content.getOrDefault(key, null);
            if (p == null) {
                log.error("can not find content, h3: {}", currentOutline.getCurrentOutlineH3());
                continue;
            }
            if (result.length() > 0) {
                result.append("\n");
            }
            if (StringUtils.isBlank(currentH2) || !currentH2.equals(currentOutline.getCurrentOutlineH2())) {
                //一样的H2只插入一次
                result.append("## ");
                result.append(currentOutline.getCurrentOutlineH2());
                result.append("\n");
            }
            if (currentOutline.getCurrentOutlineH3() != null) {
                result.append("### ");
                result.append(currentOutline.getCurrentOutlineH3());
                result.append("\n");
            }
            result.append(p);
            currentH2 = currentOutline.getCurrentOutlineH2();
        }
        String articleContent = result.toString();
        return BlogArtileContentUtil.markdown2htmlWithoutStyle(articleContent);
    }

    /**
     * 关键词转换复数(简单处理)
     *
     * @param keyword
     * @return
     */
    public static String convertPlural(String keyword) {
        if (keyword.endsWith("y")) {
            return keyword.substring(0, keyword.length() - 1) + "ies";
        } else if (keyword.endsWith("s") || keyword.endsWith("x") || keyword.endsWith("ch") || keyword.endsWith("sh")) {
            return keyword + "es";
        } else {
            return keyword + "s";
        }
    }

    public static String getFirstParagraph(String blogBody) {
        Document doc = Jsoup.parse(blogBody);
        String text = doc.wholeText();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(text)) {
            return text.split("\n")[0];
        }
        return "";
    }

    public static Integer getMinutesToRead(String blogBody) {
        if (org.apache.commons.lang3.StringUtils.isBlank(blogBody)) {
            return 1;
        }
        Document doc = Jsoup.parse(blogBody);
        String[] words = doc.wholeText().split("\\s+");
        return (int) Math.ceil(words.length * 1.0 / 500);
    }

    public static BigDecimal calculateRemainingTime(SeoCheckRecord seoCheckRecord) {
        LocalDateTime currentTime = LocalDateTime.now();
        LocalDateTime modifiedTime = new java.sql.Timestamp(seoCheckRecord.getGmtModified().getTime()).toLocalDateTime();
        // 都转为分钟
        long elapsedTime = ChronoUnit.MINUTES.between(modifiedTime, currentTime);

        double remainingTime = 5 - elapsedTime; // 3分钟转换为秒，并减去已经执行的秒数

        return BigDecimal.valueOf(Math.max(remainingTime, 0)); // 防止返回负数
    }

    public static String getImageLink(SeoBlogArticle article) {
        String imageLink = null;

        // 优先使用详情图片
        if (StringUtils.isNotBlank(article.getContent())) {
            Document doc = Jsoup.parse(article.getContent(), "UTF-8");
            Elements images = doc.select("img[src~=(?i)^http]");
            if (CollectionUtils.isNotEmpty(images)) {
                imageLink = images.get(0).attr("src");
            }
        }

        if (StringUtils.isBlank(imageLink)
                && article.getAttributes() != null
                && StringUtils.isNotBlank(article.getAttributes().getImage())) {
            imageLink = article.getAttributes().getImage();
        }

        return imageLink;
    }

    /**
     * 获取多轮交互 7 要素
     *
     * @return
     */
    public static MultipleInteractionDTO getMultipleInteraction(SeoBlogArticle seoBlogArticle) {
        SeoBlogArticleAttributes attributes = seoBlogArticle.getAttributes();
        return MultipleInteractionDTO.builder()
                .keywords(attributes.getKeywords())
                .source(Optional.ofNullable(attributes)
                        .map(SeoBlogArticleAttributes::getSource)
                        .orElse(""))
                .blogStyle(Optional.ofNullable(attributes)
                        .map(SeoBlogArticleAttributes::getBlogStyle)
                        .orElse(""))
                .country(Optional.ofNullable(attributes)
                        .map(SeoBlogArticleAttributes::getCountry)
                        .orElse(""))
                .blogCategory(Optional.ofNullable(attributes)
                        .map(SeoBlogArticleAttributes::getBlogCategory)
                        .orElse(""))
                .link(Optional.ofNullable(attributes)
                        .map(SeoBlogArticleAttributes::getLink)
                        .orElse(""))
                .title(Optional.ofNullable(attributes)
                        .map(SeoBlogArticleAttributes::getMultiturnTitle)
                        .orElse(""))
                .outline(Optional.ofNullable(attributes)
                        .map(SeoBlogArticleAttributes::getOutline)
                        .orElse(""))
                .build();
    }

    /**
     * 根据大纲把生成的段落按顺序拼接成整篇文章
     */
    public static String buildArticleContentMarkdown(Map<String, String> content, List<BlogContentAiPromptRequest.CurrentOutline> outline) {
        StringBuilder result = new StringBuilder();
        String currentH2 = null;
        for (BlogContentAiPromptRequest.CurrentOutline currentOutline : outline) {
            String key = currentOutline.getCurrentOutlineH2();
            if (currentOutline.getCurrentOutlineH3() != null) {
                key = currentOutline.getCurrentOutlineH2() + "|||" + currentOutline.getCurrentOutlineH3();
            }
            String p = content.getOrDefault(key, null);
            if (p == null) {
                log.error("can not find content, h3: {}", currentOutline.getCurrentOutlineH3());
                continue;
            }
            if (result.length() > 0) {
                result.append("\n");
            }
            if (StringUtils.isBlank(currentH2) || !currentH2.equals(currentOutline.getCurrentOutlineH2())) {
                //一样的H2只插入一次
                result.append("## ");
                result.append(currentOutline.getCurrentOutlineH2());
                result.append("\n");
            }
            if (currentOutline.getCurrentOutlineH3() != null) {
                result.append("### ");
                result.append(currentOutline.getCurrentOutlineH3());
                result.append("\n");
            }
            result.append(p);
            currentH2 = currentOutline.getCurrentOutlineH2();
        }
        String articleContent = result.toString();

        log.info("BlogArtileContentUtil.buildArticleContentMarkdown, articleContent={}", articleContent);
        return articleContent;
    }

    /**
     * 构造没有提纲的正文
     */
    public static String buildArticleContentWithoutOutlineMarkdown(String contentMarkdown) {
        String patternString = "(?m)^#+.*\\n?";
        Pattern pattern = Pattern.compile(patternString);
        Matcher matcher = pattern.matcher(contentMarkdown);
        String articleContent = matcher.replaceAll("");

        log.info("BlogArtileContentUtil.buildArticleContentWithoutOutlineMarkdown, articleContent={}", articleContent);
        return articleContent;
    }

    public static int countWords(String str) {
        log.info("BlogArtileContentUtil.countWords, str={}", str);

        if (str == null || str.isEmpty()) {
            log.info("BlogArtileContentUtil.countWords, wordCount is 0");
            return 0;
        }

        String[] words = str.trim().split("\\s+");
        int wordCount = words.length;

        log.info("BlogArtileContentUtil.countWords, wordCount={}", wordCount);
        return wordCount;
    }

    /**
     * 将HTML内容按照<h2>标签进行分割，并将结果存储为键值对
     *
     * @param htmlContent HTML内容字符串
     * @return 分割后的键值对，键为<h2>标题，值为对应的内容
     */
    public static LinkedHashMap<String, String> splitHtmlByH2(String htmlContent) {
        Document doc = Jsoup.parse(htmlContent);
        Elements h2Elements = doc.select("h2");
        LinkedHashMap<String, String> resultMap = new LinkedHashMap<>();
        if (h2Elements.isEmpty()) {
            return resultMap;
        }
        for (int i = 0; i < h2Elements.size(); i++) {
            Element h2Element = h2Elements.get(i);
            String h2Text = h2Element.text();
            StringBuilder contentBuilder = new StringBuilder();
            Element nextSibling = h2Element.nextElementSibling();
            while (nextSibling != null && !nextSibling.tagName().equals("h2")) {
                contentBuilder.append(nextSibling.outerHtml());
                nextSibling = nextSibling.nextElementSibling();
            }
            resultMap.put(h2Text, contentBuilder.toString());
        }
        return resultMap;
    }

    public static List<String> getHtmlH2(String htmlContent) {

        List<String> h2List = new ArrayList<>();

        Document doc = Jsoup.parse(htmlContent);
        Elements h2Elements = doc.select("h2");

        for (int i = 0; i < h2Elements.size(); i++) {
            Element h2Element = h2Elements.get(i);
            String h2Text = h2Element.text();
            h2List.add(h2Text);
        }
        return h2List;
    }
}
