package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class WordpressOrgTranslationsDTO {

    private List<Translation> translations;


    @Data
    public static class Translation {

        private String language;

        private String version;

        private String updated;

        @JSONField(name = "english_name")
        private String englishName;

        @JSONField(name = "native_name")
        private String nativeName;

        @JSONField(name = "package")
        private String packageUrl;

        private Map<String, String> iso = new HashMap<>();

        private Map<String, String> strings = new HashMap<>();
    }
}
