package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ExtendKeywordsRequest {

    private String apiName = "seo_keyword_extension";

    private String srcBizName = "";

    private String scene = "";

    private Request data;


    @Data
    public static class Request {
        /**
         * max_return_num
         */
        @JSONField(name = "max_return_num")
        private Integer maxReturnNum;

        /**
         * thresh
         */
        private BigDecimal thresh;

        /**
         * topic
         */
        private List<String> topic;
    }
}
