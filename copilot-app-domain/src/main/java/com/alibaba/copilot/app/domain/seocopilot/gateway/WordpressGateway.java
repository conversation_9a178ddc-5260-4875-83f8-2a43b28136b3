package com.alibaba.copilot.app.domain.seocopilot.gateway;

import com.alibaba.copilot.boot.wordpress.web.data.media.WordpressUploadMediaResponse;
import com.alibaba.copilot.boot.wordpress.web.data.oauth.WordpressTokenInfoQuery;
import com.alibaba.copilot.boot.wordpress.web.data.oauth.WordpressTokenInfoResponse;
import com.alibaba.copilot.boot.wordpress.web.data.posts.WordpressCreatePostsRequest;
import com.alibaba.copilot.boot.wordpress.web.data.posts.WordpressCreatePostsResponse;
import com.alibaba.copilot.boot.wordpress.web.data.sites.WordpressSitesResponse;
import com.alibaba.copilot.boot.wordpress.web.data.taxonomy.WordpressGetCategoriesQuery;
import com.alibaba.copilot.boot.wordpress.web.data.taxonomy.WordpressGetCategoriesResponse;
import com.alibaba.copilot.boot.wordpress.web.data.users.WordpressGetMeResponse;

import java.util.List;
import java.util.Map;

/**
 * wordpress.com 服务
 */
public interface WordpressGateway {

    WordpressTokenInfoResponse tokenInfo(WordpressTokenInfoQuery query, String site, String accessToken);

    WordpressSitesResponse getSites(String site, String accessToken);

    WordpressCreatePostsResponse createPost(WordpressCreatePostsRequest request, Map<String, Object> parameters, String site, String accessToken);

    WordpressUploadMediaResponse uploadMedia(String site, String accessToken, List<String> mediaUrls, Map<String, Object> attrs, Map<String, Object> parameters);

    WordpressGetCategoriesResponse getCategories(String site, String accessToken, WordpressGetCategoriesQuery query);

    WordpressGetMeResponse getMe(String site, String accessToken);
}
