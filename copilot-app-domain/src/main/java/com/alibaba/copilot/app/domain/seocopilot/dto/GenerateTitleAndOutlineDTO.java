package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class GenerateTitleAndOutlineDTO {

    private String title;

    private String outline;

    @JSONField(name = "references_blogs")
    private String referencesBlogs;

    @JSONField(name = "retrieval_info_list")
    private List<String> retrievalInfoList;
}
