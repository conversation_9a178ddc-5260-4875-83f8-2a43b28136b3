package com.alibaba.copilot.app.domain.seocopilot.request;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

@Data
public class UserGrowthGenContentRequest {

    private String apiName = "seo_offline_content";

    private String srcBizName = "";

    private Request data;


    @Data
    public static class Request {

        /**
         * keywords
         */
        private String keywords;

        /**
         * title
         */
        private String title;

        /**
         * outline
         */
        private String outline;

        private String category;

        private String style;

        private String country;

        @JSONField(name = "parallel_generate")
        private Boolean parallelGenerate;

        private String promptVersion;

        /**
         * references_blogs
         */
        @J<PERSON>NField(name = "references_blogs")
        private String referencesBlogs;

        /**
         * retrieval_info_list
         */
        @JSONField(name = "retrieval_info_list")
        private List<String> retrievalInfoList;
    }
}
