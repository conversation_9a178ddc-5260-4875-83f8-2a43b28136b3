package com.alibaba.copilot.app.domain.seocopilot.constant;

import java.math.BigDecimal;
import java.util.Arrays;

/**
 * BlogCheckType
 */
public enum HomePageCheckType {
    robotsTxt("robots-txt", new BigDecimal(1)),
    structuredData("structured-data", new BigDecimal(2)),
    fontSize("font-size", new BigDecimal(3)),
    tapTargets("tap-targets", new BigDecimal(4)),
    viewport("viewport", new BigDecimal(5)),
    documentTitle("document-title", new BigDecimal(6)),
    metaDescription("meta-description", new BigDecimal(7)),
    httpStatusCode("http-status-code", new BigDecimal(8)),
    crawlableAnchors("crawlable-anchors", new BigDecimal(9)),
    isCrawlable("is-crawlable", new BigDecimal(10)),
    canonical("canonical", new BigDecimal(11)),
    imageAlt("image-alt", new BigDecimal(12)),
    plugins("plugins", new BigDecimal(13)),
    hreflang("hreflang", new BigDecimal(14)),
    linkText("link-text", new BigDecimal(15));

    /**
     * 名称
     */
    private String name;

    /**
     * 权重
     */
    private BigDecimal weight;

    HomePageCheckType(String name, BigDecimal weight) {
        this.name = name;
        this.weight = weight;
    }

    public String getName() {
        return name;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public static HomePageCheckType getEnumByName(String name) {
        return Arrays.stream(HomePageCheckType.values())
                .filter(homePageCheckType -> homePageCheckType.getName().equals(name))
                .findFirst()
                .orElse(null);
    }
}
