package com.alibaba.copilot.app.domain.seocopilot.service;

import com.alibaba.copilot.app.domain.seocopilot.dto.ContentAiGenerateDTO;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoShop;
import com.alibaba.copilot.app.domain.seocopilot.request.ContentAiGenerateRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

/**
 * <AUTHOR>
 * @des AI 的内容生成service
 */
public interface ContentAiGenerateService {

    /**
     * 三期 product/collection 内容 生成场景，调用算法 TPP
     *
     * @param seoShop
     * @param generateRequest
     * @return
     */
    SingleResult<ContentAiGenerateDTO> contentAiGenerate(SeoShop seoShop, ContentAiGenerateRequest generateRequest);


}
