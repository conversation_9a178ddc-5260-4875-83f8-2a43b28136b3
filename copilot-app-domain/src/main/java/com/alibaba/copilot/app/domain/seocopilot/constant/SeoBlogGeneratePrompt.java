package com.alibaba.copilot.app.domain.seocopilot.constant;

public class SeoBlogGeneratePrompt {

    public static final String GENERATE_OUTLINE = "[Task Description]\n" +
            "You are an assistant for Search Engine Optimization, your task is to generate a blog outline based on the provided search %s and follow the below rules.\n" +
            "[Rules]\n" +
            "* The output must be in markdown format.\n" +
            "* The outline must only include H1H2 title.The best number of H2 titles is between 3 and 5.\n" +
            "* You should consider what the user want for searching \"kernel keyword\", then write the outline .\n" +
            "* Each content of H2 outline will be writen alone.\n" +
            "* Return outline and keywords without any other words.\n" +
            "[examples]\n" +
            "# Title for blog\n" +
            "## Subtitle B for blog\n" +
            "keywords for this paragraph\n" +
            "## subtitle C for blog\n" +
            "keywords for this paragraph\n" +
            "## subtitle D for blog\n" +
            "keywords for this paragraph\n" +
            "## ... other subtitles for blog\n" +
            "\n" +
            "[Kernel Keyword]" +
            "%s";

    public static final String GENERATE_CONTENT = "As a blog writer, your task is to generate content for specific subtitle in the outline.\n" +
            "    [Rules]\n" +
            "     * Generate content for \"Current Outline title\".\n" +
            "     * Consider carefully if the content should contain a list, if should, generate in markdown with a list.\n" +
            "     * Only return content for specific subtitle without any other words.\n" +
            "    [example]\n" +
            "    outline:\n" +
            "    # blog title\n" +
            "    ## subtitle 1\n" +
            "    keywords\n" +
            "    ## subtitle 2\n" +
            "    keywords\n" +
            "    ## subtitle 3\n" +
            "    keywords\n" +
            "\n" +
            "    your subtitle:\n" +
            "    ## subtitle 2\n" +
            "    keywords\n" +
            "\n" +
            "    output:\n" +
            "    Content for subtitle 2,this paragraph must be between 150 and 250 words in length.\n" +
            "    [user input]\n" +
            "    outline:\n" +
            "    %s\n" +
            "    your subtitle:\n" +
            "    %s\n" +
            "    %s";

    public static final String GENERATE_DESCRIPTION_TAG = "[Basic Knowledge]\n" +
            "The HTML <meta> description tag is an HTML attribute that provides a brief summary of a web page's content. Search engines like Google often display the meta description in search results, which can influence click-through rates.\n" +
            "It's important to write clear and enticing meta descriptions for your web pages to improve visibility on search engine results pages and entice users to click through to your website.\n" +
            "[Task Description]\n" +
            "As a blog writer, given a blog kernel keyword, your task is to generate a meta description following the below rules.\n" +
            "[Rules]\n" +
            " * Use active voice.\n" +
            " * Output should be in list, for example: [\"meta description 1\", \"meta description 2\",...]\n" +
            " * Generate 5 possible meta description for me to choose, each one should be between 150 and 200 characters in length, from brief to detail, short to longer, but each contains two sentence.\n" +
            "[Kernel Keyword]\n" +
            "%s\n" +
            "[Meta Description]";

    public static final String GENERATE_TITLE_TAG = "Assume that you are a Google SEO expert, provide you with a title, and generate a relevant title tag following the below rules.\n" +
            "\n" +
            "[Rules]\n" +
            "* Output should be in list, for example: [\"title tag 1\", \"title tag 2\",...]\n" +
            "* Generate 5 possible title tags for me to choose, each one should be between 55 and 70 characters in length, from brief to detail, short to longer.\n" +
            "[Title]\n" +
            "%s";


}
