package com.alibaba.copilot.app.domain.seocopilot.gateway;


import com.alibaba.copilot.app.domain.seocopilot.dto.LoginSystemTokenDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.LoginSystemUserDTO;
import com.alibaba.copilot.app.domain.seocopilot.request.LoginSystemTokenRequest;

/**
 * 登录基建服务
 */
public interface LoginSystemGateway {


    /**
     * 获取登录体系-Oauth token
     *
     * @param request
     * @return
     */
    LoginSystemTokenDTO getAccessToken(LoginSystemTokenRequest request);


    /**
     * 获取登录体系-用户信息
     *
     * @return
     */
    LoginSystemUserDTO getUser(String userId, String accessToken);
}
