package com.alibaba.copilot.app.domain.seocopilot.gateway;


import com.alibaba.copilot.app.domain.seocopilot.dto.UserGrowthGenContentDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.UserGrowthGenTDKDTO;
import com.alibaba.copilot.app.domain.seocopilot.dto.UserGrowthGenTitleAndOutlineDTO;
import com.alibaba.copilot.app.domain.seocopilot.request.UserGrowthGenContentRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.UserGrowthGenTDKRequest;
import com.alibaba.copilot.app.domain.seocopilot.request.UserGrowthGenTitleAndOutlineRequest;
import com.alibaba.copilot.boot.basic.result.SingleResult;

/**
 * 算法Tpp服务 Gateway
 */
public interface UserGrowthAlgorithmBlogTppGateway {


    /**
     * 生成title和outline
     *
     * @param request
     */
    SingleResult<UserGrowthGenTitleAndOutlineDTO> generateTitleAndOutline(UserGrowthGenTitleAndOutlineRequest request);


    /**
     * 生成内容
     *
     * @param request
     * @return
     */
    SingleResult<UserGrowthGenContentDTO> generateContent(UserGrowthGenContentRequest request);


    /**
     * 生成TDK
     *
     * @param request
     */
    SingleResult<UserGrowthGenTDKDTO> generateTDK(UserGrowthGenTDKRequest request);

}
