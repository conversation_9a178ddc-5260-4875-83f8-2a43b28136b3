package com.alibaba.copilot.app.domain.seocopilot.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * SeoShop 实体
 */
@Data
@Builder
@Slf4j
@NoArgsConstructor
@AllArgsConstructor
public class SeoShop {

    /**
     * id
     */
    private Long id;

    /**
     * userId
     */
    private Long userId;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * shopify店铺ID
     */
    private Long shopifyShopId;

    /**
     * 鉴权访问token
     */
    private String accessToken;

    /**
     * gsc鉴权访问refresh_token
     */
    private String gscRefreshToken;

    /**
     * gsc email
     */
    private String gscEmail;
    /**
     * ga鉴权访问refresh_token
     */
    private String gaRefreshToken;

    /**
     * ga email
     */
    private String gaEmail;

    /**
     * shopify店铺对外域名
     */
    private String shopDomain;

    /**
     * shopify店铺域名
     */
    private String shopifyShopDomain;

    /**
     * 店铺名
     */
    private String shopName;

    /**
     * shopify的email
     */
    private String shopEmail;

    /**
     * shopify的email
     */
    private Boolean fullyManagedEnabled;

    /**
     * 能够自动优化的类型
     */
    private String autoOptimizationType;

    /**
     * 最后一次全局任务批次号
     */
    private String batchId;

    /**
     * 状态：绑定/解绑
     */
    private Boolean status;

    /**
     * 逻辑删除
     */
    private Boolean deleted;

    /**
     * 站点类型
     * shopify/wordpress
     */
    private String siteType;

    /**
     * 扩展字段
     */
    private SeoShopAttributes attributes = new SeoShopAttributes("{}");

    /**
     * 无鉴权的状态下构建Seoshop
     */
    public static SeoShop genSeoShopNoAuth(String email, String website) {
        SeoShop seoShop = new SeoShop();
        seoShop.setShopEmail(email);
        seoShop.setShopDomain(website);
        return seoShop;
    }
}
