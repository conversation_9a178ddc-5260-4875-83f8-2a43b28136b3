package com.alibaba.copilot.app.domain.seocopilot.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.text.DecimalFormat;

/**
 * KeywordRerankDataDTO
 *
 * <AUTHOR>
 * @date 2024/6/20 4:33 下午
 */
@Slf4j
@Data
public class KeywordRerankDataDTO {

    private final static DecimalFormat df = new DecimalFormat("#.00");

    /**
     * keyword
     */
    private String keyword;

    /**
     * kd
     */
    private String kd;

    /**
     * searchVolume
     */
    @JSONField(name = "search_volume")
    private String searchVolume;

    /**
     * cpc
     */
    private String cpc;

    /**
     * intent
     */
    private String intent;

    /**
     * qualityScore
     */
    @JSONField(name = "quality_score")
    private String qualityScore;

    /**
     * position
     */
    private String position;

    public String getSearchVolumeStr() {
        try {
            return String.valueOf(Long.parseLong(this.getSearchVolume()));
        } catch (Exception e) {
            log.error("KeywordRerankDataDTO.getSearchVolumeStr exp", e);
            return null;
        }
    }

    public String getQualityScoreStr() {
        try {
            return String.valueOf((int) Double.parseDouble(this.getQualityScore()));
        } catch (Exception e) {
            log.error("KeywordRerankDataDTO.getQualityScoreStr exp", e);
            return null;
        }
    }

    public String getPositionStr() {
        try {
            return df.format(Double.valueOf(this.getPosition()));
        } catch (Exception e) {
            log.error("KeywordRerankDataDTO.getPositionStr exp", e);
            return null;
        }
    }
}
