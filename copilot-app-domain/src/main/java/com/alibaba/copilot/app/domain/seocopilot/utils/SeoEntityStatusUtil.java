package com.alibaba.copilot.app.domain.seocopilot.utils;

import com.alibaba.copilot.app.domain.seocopilot.constant.SeoEntityStatus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;
import java.util.Objects;

@Slf4j
public class SeoEntityStatusUtil {

    private static final String PAGE_INDEX_PASS = "PASS";

    /**
     * 获取SEO entity状态
     */
    public static SeoEntityStatus getStatus(Date shopifyUpdateAt, String indexStatus, Integer checkGapDays, SeoEntityStatus entityStatus, Long entityId) {
        if (PAGE_INDEX_PASS.equals(indexStatus)) {
            log.info("SeoEntityStatus getStatus,shopify collection id={},index status=PASS", entityId);
            return SeoEntityStatus.NO_OPTIMIZE_NEEDED;
        }

        if (StringUtils.isBlank(indexStatus)) {
            log.info("SeoEntityStatus getStatus,shopify collection id={},index status is blank", entityId);
            return SeoEntityStatus.NO_OPTIMIZE_NEEDED;
        }

        if (DateUtil.isWithinNDays(shopifyUpdateAt, checkGapDays)) {
            log.info("SeoEntityStatus getStatus,shopify collection id={},update at in 14 days", entityId);
            return SeoEntityStatus.PAGE_WAIT_INDEX;
        }
        if (Objects.equals(entityStatus, SeoEntityStatus.COMPLETED)) {
            log.info("SeoEntityStatus getStatus,shopify collection id={},status={}", entityId, SeoEntityStatus.COMPLETED.name());
            return SeoEntityStatus.COMPLETED;
        }
        return SeoEntityStatus.INIT;
    }
}
