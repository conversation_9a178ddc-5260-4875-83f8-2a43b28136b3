package com.alibaba.copilot.app.domain.seocopilot.model;

import com.alibaba.copilot.app.client.seocopilot.constant.CrawlPageStatusEnum;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 爬虫页面
 */
@Slf4j
@Builder
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CrawlPageLog {

    /**
     * id
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * job_id
     */
    private Long jobId;

    /**
     * url
     */
    private String url;

    /**
     * http_status
     */
    private Integer httpStatus;

    /**
     * http_status_text
     */
    private String httpStatusText;

    /**
     * html_oss_url
     */
    private String htmlOssUrl;

    /**
     * crawl_status
     */
    private CrawlPageStatusEnum crawlStatus;

    /**
     * 逻辑删除
     */
    private Boolean deleted;
}
