package com.alibaba.copilot.app.domain.seocopilot.repository;

import com.alibaba.copilot.app.client.base.model.PageWrapper;
import com.alibaba.copilot.app.domain.seocopilot.constant.SeoEntityStatus;
import com.alibaba.copilot.app.domain.seocopilot.model.SeoProduct;
import com.alibaba.copilot.app.domain.seocopilot.request.SeoProductQueryRequest;

import java.util.List;
import java.util.Map;

/**
 * SeoProductRepository
 */
public interface SeoProductRepository {

    /**
     * 保存seoProduct
     *
     * @param seoProduct
     * @return
     */
    Long saveSeoProduct(SeoProduct seoProduct);

    /**
     * 根据id查询seoProduct
     *
     * @param id
     * @return
     */
    SeoProduct getSeoProductById(Long id);

    /**
     * 根据shopifyProductId查询seoProduct
     *
     * @param shopId
     * @param shopifyProductId
     * @return
     */
    SeoProduct getSeoProductByShopifyProductId(Long shopId, Long shopifyProductId);

    /**
     * 分页查询seoProduct
     *
     * @param seoProductQueryRequest
     * @return
     */
    PageWrapper<SeoProduct> getSeoProductsByShopId(SeoProductQueryRequest seoProductQueryRequest);

    /**
     * 查询seoProduct
     *
     * @param shopId
     * @return
     */
    List<SeoProduct> getSeoProductsByShopId(Long shopId);

    /**
     * 查询seoProduct
     *
     * @param shopId
     * @return
     */
    SeoProduct getSeoProductsByShopIdAndHandle(Long shopId,String handle);

    /**
     * 删除seoProduct
     *
     * @param shopId
     * @return
     */
    Integer deleteByShopId(Long shopId);

    SeoProduct getById(Long productId);

    List<SeoProduct> getByShopId(Long shopId);

    /**
     * 查询14天内更新且未被收录的记录
     * @param shopId
     * @return
     */
    List<SeoProduct> getByShopIdIn14DayAndNoIndex(Long shopId);

    Integer updateStatus(Long productId, SeoEntityStatus status);

    Integer updateIssueType(Long productId, String issueType);

    /**
     * 删除 seoproduct 改 delete 状态
     *
     * @param shopId
     * @param shopifyProductId
     * @return
     */
    Integer updateDelete(Long shopId, Long shopifyProductId);

    Integer updateAttribute(Long id, Map<String, Object> updateAttributeMap);

    List<SeoProduct> getByShopifyProductIdIgnoreDeleted(Long shopId, Long shopifyProductId);
}
