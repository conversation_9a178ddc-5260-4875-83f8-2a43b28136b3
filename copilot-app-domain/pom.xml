<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.alibaba.app</groupId>
		<artifactId>copilot-app</artifactId>
		<version>1.0.0-SNAPSHOT</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>copilot-app-domain</artifactId>
	<packaging>jar</packaging>
	<name>copilot-app-domain</name>

	<dependencies>
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-boot-shopify</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>log4j</artifactId>
					<groupId>log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>pandora-diamond-spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>log4j-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-to-slf4j</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>pandora-hsf-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>pandora-tddl-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-boot-llm-openai</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-boot-monitor-starter</artifactId>
		</dependency>
		<dependency>
		<groupId>org.freemarker</groupId>
		<artifactId>freemarker</artifactId>
	   </dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<scope>compile</scope>
		</dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
		<dependency>
			<groupId>com.alibaba.app</groupId>
			<artifactId>copilot-app-client</artifactId>
		</dependency>

		<!-- Normandy 资源凭证 SDK -->
		<dependency>
			<groupId>com.alibaba.normandy.credential</groupId>
			<artifactId>normandy-credential-spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
		</dependency>
        <dependency>
            <groupId>com.vladsch.flexmark</groupId>
            <artifactId>flexmark-all</artifactId>
        </dependency>

		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-boot-semrush</artifactId>
		</dependency>

		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-boot-wordpress</artifactId>
		</dependency>

		<dependency>
			<groupId>com.alibaba.copilot</groupId>
			<artifactId>copilot-boot-wordpress-org</artifactId>
		</dependency>

    </dependencies>
</project>
